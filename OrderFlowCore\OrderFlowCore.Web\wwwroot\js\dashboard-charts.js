// Dashboard Charts JavaScript Module
(function () {
    'use strict';

    // Chart configuration
    const chartConfig = {
        colors: {
            primary: '#007bff',
            success: '#28a745',
            warning: '#ffc107',
            danger: '#dc3545',
            info: '#17a2b8',
            secondary: '#6c757d',
            light: '#f8f9fa',
            dark: '#343a40'
        },
        chartColors: [
            '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF',
            '#FF9F40', '#FF6384', '#C9CBCF', '#4BC0C0', '#FF6384'
        ]
    };

    // Initialize charts when DOM is ready
    document.addEventListener('DOMContentLoaded', function() {
        initializeCharts();
        initializeAnimations();
    });

    // Initialize all charts
    function initializeCharts() {
        initializeOrderStatusChart();
        initializeOrderTypeChart();
        initializeProgressBars();
    }

    // Initialize Order Status Chart
    function initializeOrderStatusChart() {
        const statusCtx = document.getElementById('orderStatusChart');
        if (!statusCtx) return;

        const chartData = getOrderStatusChartData();
        if (!chartData) return;

        new Chart(statusCtx.getContext('2d'), {
            type: 'doughnut',
            data: {
                labels: chartData.labels,
                datasets: [{
                    data: chartData.data,
                    backgroundColor: chartConfig.chartColors,
                    borderWidth: 3,
                    borderColor: '#fff',
                    hoverBorderWidth: 4,
                    hoverBorderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                cutout: '60%',
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true,
                            font: {
                                family: 'Cairo, sans-serif',
                                size: 12
                            }
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleColor: '#fff',
                        bodyColor: '#fff',
                        borderColor: chartConfig.colors.primary,
                        borderWidth: 1,
                        cornerRadius: 8,
                        displayColors: true
                    }
                },
                animation: {
                    animateRotate: true,
                    animateScale: true,
                    duration: 1000,
                    easing: 'easeOutQuart'
                }
            }
        });
    }

    // Initialize Order Type Chart
    function initializeOrderTypeChart() {
        const typeCtx = document.getElementById('orderTypeChart');
        if (!typeCtx) return;

        const chartData = getOrderTypeChartData();
        if (!chartData) return;

        new Chart(typeCtx.getContext('2d'), {
            type: 'bar',
            data: {
                labels: chartData.labels,
                datasets: [{
                    label: 'عدد الطلبات',
                    data: chartData.data,
                    backgroundColor: 'rgba(54, 162, 235, 0.8)',
                    borderColor: 'rgba(54, 162, 235, 1)',
                    borderWidth: 2,
                    borderRadius: 8,
                    borderSkipped: false,
                    hoverBackgroundColor: 'rgba(54, 162, 235, 1)',
                    hoverBorderColor: 'rgba(54, 162, 235, 1)',
                    hoverBorderWidth: 3
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleColor: '#fff',
                        bodyColor: '#fff',
                        borderColor: chartConfig.colors.primary,
                        borderWidth: 1,
                        cornerRadius: 8,
                        callbacks: {
                            label: function(context) {
                                return `عدد الطلبات: ${context.parsed.y}`;
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0,0,0,0.1)',
                            drawBorder: false
                        },
                        ticks: {
                            font: {
                                family: 'Cairo, sans-serif',
                                size: 12
                            },
                            color: '#6c757d'
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        },
                        ticks: {
                            font: {
                                family: 'Cairo, sans-serif',
                                size: 11
                            },
                            color: '#6c757d',
                            maxRotation: 45
                        }
                    }
                },
                animation: {
                    duration: 1000,
                    easing: 'easeOutQuart'
                }
            }
        });
    }

    // Initialize Progress Bars Animation
    function initializeProgressBars() {
        const progressBars = document.querySelectorAll('.progress-modern .progress-bar');
        progressBars.forEach(bar => {
            const width = bar.style.width;
            bar.style.width = '0%';
            
            setTimeout(() => {
                bar.style.transition = 'width 1.5s ease-out';
                bar.style.width = width;
            }, 500);
        });
    }

    // Initialize Animations
    function initializeAnimations() {
        // Animate stat cards on scroll
        const statCards = document.querySelectorAll('.stat-card');
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-in');
                }
            });
        }, { threshold: 0.1 });

        statCards.forEach(card => observer.observe(card));

        // Animate workflow stages
        const workflowStages = document.querySelectorAll('.workflow-stage');
        workflowStages.forEach((stage, index) => {
            setTimeout(() => {
                stage.classList.add('fade-in');
            }, index * 200);
        });
    }

    // Get Order Status Chart Data from the page
    function getOrderStatusChartData() {
        try {
            const statusLabels = window.dashboardData?.orderStatusLabels || [];
            const statusData = window.dashboardData?.orderStatusData || [];
            
            if (statusLabels.length === 0 || statusData.length === 0) {
                return null;
            }

            return {
                labels: statusLabels,
                data: statusData
            };
        } catch (error) {
            console.error('Error getting order status chart data:', error);
            return null;
        }
    }

    // Get Order Type Chart Data from the page
    function getOrderTypeChartData() {
        try {
            const typeLabels = window.dashboardData?.orderTypeLabels || [];
            const typeData = window.dashboardData?.orderTypeData || [];
            
            if (typeLabels.length === 0 || typeData.length === 0) {
                return null;
            }

            return {
                labels: typeLabels,
                data: typeData
            };
        } catch (error) {
            console.error('Error getting order type chart data:', error);
            return null;
        }
    }

    // Export functions for external use
    window.dashboardCharts = {
        initializeCharts: initializeCharts,
        initializeAnimations: initializeAnimations,
        refreshCharts: function() {
            // Destroy existing charts and reinitialize
            Chart.helpers.each(Chart.instances, function(instance) {
                instance.destroy();
            });
            initializeCharts();
        }
    };

})(); 