<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.Extensions.FileProviders.Abstractions</name>
    </assembly>
    <members>
        <member name="T:Microsoft.Extensions.FileProviders.IDirectoryContents">
            <summary>
            Represents a directory's content in the file provider.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.FileProviders.IDirectoryContents.Exists">
            <summary>
            True if a directory was located at the given path.
            </summary>
        </member>
        <member name="T:Microsoft.Extensions.FileProviders.IFileInfo">
            <summary>
            Represents a file in the given file provider.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.FileProviders.IFileInfo.Exists">
            <summary>
            True if resource exists in the underlying storage system.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.FileProviders.IFileInfo.Length">
            <summary>
            The length of the file in bytes, or -1 for a directory or non-existing files.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.FileProviders.IFileInfo.PhysicalPath">
            <summary>
            The path to the file, including the file name. Return null if the file is not directly accessible.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.FileProviders.IFileInfo.Name">
            <summary>
            The name of the file or directory, not including any path.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.FileProviders.IFileInfo.LastModified">
            <summary>
            When the file was last modified
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.FileProviders.IFileInfo.IsDirectory">
            <summary>
            True for the case TryGetDirectoryContents has enumerated a sub-directory
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.FileProviders.IFileInfo.CreateReadStream">
            <summary>
            Return file contents as readonly stream. Caller should dispose stream when complete.
            </summary>
            <returns>The file stream</returns>
        </member>
        <member name="T:Microsoft.Extensions.FileProviders.IFileProvider">
            <summary>
            A read-only file provider abstraction.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.FileProviders.IFileProvider.GetFileInfo(System.String)">
            <summary>
            Locate a file at the given path.
            </summary>
            <param name="subpath">Relative path that identifies the file.</param>
            <returns>The file information. Caller must check Exists property.</returns>
        </member>
        <member name="M:Microsoft.Extensions.FileProviders.IFileProvider.GetDirectoryContents(System.String)">
            <summary>
            Enumerate a directory at the given path, if any.
            </summary>
            <param name="subpath">Relative path that identifies the directory.</param>
            <returns>Returns the contents of the directory.</returns>
        </member>
        <member name="M:Microsoft.Extensions.FileProviders.IFileProvider.Watch(System.String)">
            <summary>
            Creates a <see cref="T:Microsoft.Extensions.Primitives.IChangeToken"/> for the specified <paramref name="filter"/>.
            </summary>
            <param name="filter">Filter string used to determine what files or folders to monitor. Example: **/*.cs, *.*, subFolder/**/*.cshtml.</param>
            <returns>An <see cref="T:Microsoft.Extensions.Primitives.IChangeToken"/> that is notified when a file matching <paramref name="filter"/> is added, modified or deleted.</returns>
        </member>
        <member name="T:Microsoft.Extensions.FileProviders.NotFoundDirectoryContents">
            <summary>
            Represents a non-existing directory
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.FileProviders.NotFoundDirectoryContents.Singleton">
            <summary>
            A shared instance of <see cref="T:Microsoft.Extensions.FileProviders.NotFoundDirectoryContents"/>
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.FileProviders.NotFoundDirectoryContents.Exists">
            <summary>
            Always false.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.FileProviders.NotFoundDirectoryContents.GetEnumerator">
            <summary>Returns an enumerator that iterates through the collection.</summary>
            <returns>An enumerator to an empty collection.</returns>
        </member>
        <member name="M:Microsoft.Extensions.FileProviders.NotFoundDirectoryContents.System#Collections#IEnumerable#GetEnumerator">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.Extensions.FileProviders.NotFoundFileInfo">
            <summary>
            Represents a non-existing file.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.FileProviders.NotFoundFileInfo.#ctor(System.String)">
            <summary>
            Initializes an instance of <see cref="T:Microsoft.Extensions.FileProviders.NotFoundFileInfo"/>.
            </summary>
            <param name="name">The name of the file that could not be found</param>
        </member>
        <member name="P:Microsoft.Extensions.FileProviders.NotFoundFileInfo.Exists">
            <summary>
            Always false.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.FileProviders.NotFoundFileInfo.IsDirectory">
            <summary>
            Always false.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.FileProviders.NotFoundFileInfo.LastModified">
            <summary>
            Returns <see cref="F:System.DateTimeOffset.MinValue"/>.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.FileProviders.NotFoundFileInfo.Length">
            <summary>
            Always equals -1.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.FileProviders.NotFoundFileInfo.Name">
            <inheritdoc />
        </member>
        <member name="P:Microsoft.Extensions.FileProviders.NotFoundFileInfo.PhysicalPath">
            <summary>
            Always null.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.FileProviders.NotFoundFileInfo.CreateReadStream">
            <summary>
            Always throws. A stream cannot be created for non-existing file.
            </summary>
            <exception cref="T:System.IO.FileNotFoundException">Always thrown.</exception>
            <returns>Does not return</returns>
        </member>
        <member name="T:Microsoft.Extensions.FileProviders.NullChangeToken">
            <summary>
            An empty change token that doesn't raise any change callbacks.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.FileProviders.NullChangeToken.Singleton">
            <summary>
            A singleton instance of <see cref="T:Microsoft.Extensions.FileProviders.NullChangeToken"/>
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.FileProviders.NullChangeToken.HasChanged">
            <summary>
            Always false.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.FileProviders.NullChangeToken.ActiveChangeCallbacks">
            <summary>
            Always false.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.FileProviders.NullChangeToken.RegisterChangeCallback(System.Action{System.Object},System.Object)">
            <summary>
            Always returns an empty disposable object. Callbacks will never be called.
            </summary>
            <param name="callback">This parameter is ignored</param>
            <param name="state">This parameter is ignored</param>
            <returns>A disposable object that noops on dispose.</returns>
        </member>
        <member name="T:Microsoft.Extensions.FileProviders.NullFileProvider">
            <summary>
            An empty file provider with no contents.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.FileProviders.NullFileProvider.GetDirectoryContents(System.String)">
            <summary>
            Enumerate a non-existent directory.
            </summary>
            <param name="subpath">A path under the root directory. This parameter is ignored.</param>
            <returns>A <see cref="T:Microsoft.Extensions.FileProviders.IDirectoryContents"/> that does not exist and does not contain any contents.</returns>
        </member>
        <member name="M:Microsoft.Extensions.FileProviders.NullFileProvider.GetFileInfo(System.String)">
            <summary>
            Locate a non-existent file.
            </summary>
            <param name="subpath">A path under the root directory.</param>
            <returns>A <see cref="T:Microsoft.Extensions.FileProviders.IFileInfo"/> representing a non-existent file at the given path.</returns>
        </member>
        <member name="M:Microsoft.Extensions.FileProviders.NullFileProvider.Watch(System.String)">
            <summary>
            Returns a <see cref="T:Microsoft.Extensions.Primitives.IChangeToken"/> that monitors nothing.
            </summary>
            <param name="filter">Filter string used to determine what files or folders to monitor. This parameter is ignored.</param>
            <returns>A <see cref="T:Microsoft.Extensions.Primitives.IChangeToken"/> that does not register callbacks.</returns>
        </member>
        <member name="P:System.SR.FileNotExists">
            <summary>The file {0} does not exist.</summary>
        </member>
        <member name="T:System.Runtime.InteropServices.LibraryImportAttribute">
            <summary>
            Attribute used to indicate a source generator should create a function for marshalling
            arguments instead of relying on the runtime to generate an equivalent marshalling function at run-time.
            </summary>
            <remarks>
            This attribute is meaningless if the source generator associated with it is not enabled.
            The current built-in source generator only supports C# and only supplies an implementation when
            applied to static, partial, non-generic methods.
            </remarks>
        </member>
        <member name="M:System.Runtime.InteropServices.LibraryImportAttribute.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Runtime.InteropServices.LibraryImportAttribute"/>.
            </summary>
            <param name="libraryName">Name of the library containing the import.</param>
        </member>
        <member name="P:System.Runtime.InteropServices.LibraryImportAttribute.LibraryName">
            <summary>
            Gets the name of the library containing the import.
            </summary>
        </member>
        <member name="P:System.Runtime.InteropServices.LibraryImportAttribute.EntryPoint">
            <summary>
            Gets or sets the name of the entry point to be called.
            </summary>
        </member>
        <member name="P:System.Runtime.InteropServices.LibraryImportAttribute.StringMarshalling">
            <summary>
            Gets or sets how to marshal string arguments to the method.
            </summary>
            <remarks>
            If this field is set to a value other than <see cref="F:System.Runtime.InteropServices.StringMarshalling.Custom" />,
            <see cref="P:System.Runtime.InteropServices.LibraryImportAttribute.StringMarshallingCustomType" /> must not be specified.
            </remarks>
        </member>
        <member name="P:System.Runtime.InteropServices.LibraryImportAttribute.StringMarshallingCustomType">
            <summary>
            Gets or sets the <see cref="T:System.Type"/> used to control how string arguments to the method are marshalled.
            </summary>
            <remarks>
            If this field is specified, <see cref="P:System.Runtime.InteropServices.LibraryImportAttribute.StringMarshalling" /> must not be specified
            or must be set to <see cref="F:System.Runtime.InteropServices.StringMarshalling.Custom" />.
            </remarks>
        </member>
        <member name="P:System.Runtime.InteropServices.LibraryImportAttribute.SetLastError">
            <summary>
            Gets or sets whether the callee sets an error (SetLastError on Windows or errno
            on other platforms) before returning from the attributed method.
            </summary>
        </member>
        <member name="T:System.Runtime.InteropServices.StringMarshalling">
            <summary>
            Specifies how strings should be marshalled for generated p/invokes
            </summary>
        </member>
        <member name="F:System.Runtime.InteropServices.StringMarshalling.Custom">
            <summary>
            Indicates the user is suppling a specific marshaller in <see cref="P:System.Runtime.InteropServices.LibraryImportAttribute.StringMarshallingCustomType"/>.
            </summary>
        </member>
        <member name="F:System.Runtime.InteropServices.StringMarshalling.Utf8">
            <summary>
            Use the platform-provided UTF-8 marshaller.
            </summary>
        </member>
        <member name="F:System.Runtime.InteropServices.StringMarshalling.Utf16">
            <summary>
            Use the platform-provided UTF-16 marshaller.
            </summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.AllowNullAttribute">
            <summary>Specifies that null is allowed as an input even if the corresponding type disallows it.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.DisallowNullAttribute">
            <summary>Specifies that null is disallowed as an input even if the corresponding type allows it.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.MaybeNullAttribute">
            <summary>Specifies that an output may be null even if the corresponding type disallows it.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.NotNullAttribute">
            <summary>Specifies that an output will not be null even if the corresponding type allows it. Specifies that an input argument was not null when the call returns.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.MaybeNullWhenAttribute">
            <summary>Specifies that when a method returns <see cref="P:System.Diagnostics.CodeAnalysis.MaybeNullWhenAttribute.ReturnValue"/>, the parameter may be null even if the corresponding type disallows it.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MaybeNullWhenAttribute.#ctor(System.Boolean)">
            <summary>Initializes the attribute with the specified return value condition.</summary>
            <param name="returnValue">
            The return value condition. If the method returns this value, the associated parameter may be null.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.MaybeNullWhenAttribute.ReturnValue">
            <summary>Gets the return value condition.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.NotNullWhenAttribute">
            <summary>Specifies that when a method returns <see cref="P:System.Diagnostics.CodeAnalysis.NotNullWhenAttribute.ReturnValue"/>, the parameter will not be null even if the corresponding type allows it.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.NotNullWhenAttribute.#ctor(System.Boolean)">
            <summary>Initializes the attribute with the specified return value condition.</summary>
            <param name="returnValue">
            The return value condition. If the method returns this value, the associated parameter will not be null.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.NotNullWhenAttribute.ReturnValue">
            <summary>Gets the return value condition.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.NotNullIfNotNullAttribute">
            <summary>Specifies that the output will be non-null if the named parameter is non-null.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.NotNullIfNotNullAttribute.#ctor(System.String)">
            <summary>Initializes the attribute with the associated parameter name.</summary>
            <param name="parameterName">
            The associated parameter name.  The output will be non-null if the argument to the parameter specified is non-null.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.NotNullIfNotNullAttribute.ParameterName">
            <summary>Gets the associated parameter name.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.DoesNotReturnAttribute">
            <summary>Applied to a method that will never return under any circumstance.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.DoesNotReturnIfAttribute">
            <summary>Specifies that the method will not return if the associated Boolean parameter is passed the specified value.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.DoesNotReturnIfAttribute.#ctor(System.Boolean)">
            <summary>Initializes the attribute with the specified parameter value.</summary>
            <param name="parameterValue">
            The condition parameter value. Code after the method will be considered unreachable by diagnostics if the argument to
            the associated parameter matches this value.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.DoesNotReturnIfAttribute.ParameterValue">
            <summary>Gets the condition parameter value.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.MemberNotNullAttribute">
            <summary>Specifies that the method or property will ensure that the listed field and property members have not-null values.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MemberNotNullAttribute.#ctor(System.String)">
            <summary>Initializes the attribute with a field or property member.</summary>
            <param name="member">
            The field or property member that is promised to be not-null.
            </param>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MemberNotNullAttribute.#ctor(System.String[])">
            <summary>Initializes the attribute with the list of field and property members.</summary>
            <param name="members">
            The list of field and property members that are promised to be not-null.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.MemberNotNullAttribute.Members">
            <summary>Gets field or property member names.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.MemberNotNullWhenAttribute">
            <summary>Specifies that the method or property will ensure that the listed field and property members have not-null values when returning with the specified return value condition.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MemberNotNullWhenAttribute.#ctor(System.Boolean,System.String)">
            <summary>Initializes the attribute with the specified return value condition and a field or property member.</summary>
            <param name="returnValue">
            The return value condition. If the method returns this value, the associated parameter will not be null.
            </param>
            <param name="member">
            The field or property member that is promised to be not-null.
            </param>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MemberNotNullWhenAttribute.#ctor(System.Boolean,System.String[])">
            <summary>Initializes the attribute with the specified return value condition and list of field and property members.</summary>
            <param name="returnValue">
            The return value condition. If the method returns this value, the associated parameter will not be null.
            </param>
            <param name="members">
            The list of field and property members that are promised to be not-null.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.MemberNotNullWhenAttribute.ReturnValue">
            <summary>Gets the return value condition.</summary>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.MemberNotNullWhenAttribute.Members">
            <summary>Gets field or property member names.</summary>
        </member>
    </members>
</doc>
