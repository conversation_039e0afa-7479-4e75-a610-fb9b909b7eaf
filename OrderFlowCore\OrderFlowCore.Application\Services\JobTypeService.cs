using System.Collections.Generic;
using System.Threading.Tasks;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Application.Interfaces.Repositories;
using OrderFlowCore.Application.Interfaces.Services;
using OrderFlowCore.Application.Common;

namespace OrderFlowCore.Application.Services
{
    public class JobTypeService : IJobTypeService
    {
        private readonly IUnitOfWork _unitOfWork;
        public JobTypeService(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }
        
        public async Task<ServiceResult<IEnumerable<JobTypeDto>>> GetAllAsync()
        {
            try
            {
                var jobTypes = await _unitOfWork.JobTypes.GetAllAsync();
                return ServiceResult<IEnumerable<JobTypeDto>>.Success(jobTypes);
            }
            catch (Exception ex)
            {
                return ServiceResult<IEnumerable<JobTypeDto>>.Failure($"Error retrieving job types: {ex.Message}");
            }
        }
        
        public async Task<ServiceResult<JobTypeDto>> GetByIdAsync(int id)
        {
            try
            {
                var jobType = await _unitOfWork.JobTypes.GetByIdAsync(id);
                if (jobType == null)
                    return ServiceResult<JobTypeDto>.Failure("Job type not found");
                    
                return ServiceResult<JobTypeDto>.Success(jobType);
            }
            catch (Exception ex)
            {
                return ServiceResult<JobTypeDto>.Failure($"Error retrieving job type: {ex.Message}");
            }
        }
        
        public async Task<ServiceResult> CreateAsync(JobTypeDto dto)
        {
            try
            {
                var result = await _unitOfWork.JobTypes.CreateAsync(dto);
                await _unitOfWork.SaveChangesAsync();
                
                if (result)
                    return ServiceResult.Success("Job type created successfully");
                else
                    return ServiceResult.Failure("Failed to create job type");
            }
            catch (Exception ex)
            {
                return ServiceResult.Failure($"Error creating job type: {ex.Message}");
            }
        }
        
        public async Task<ServiceResult> UpdateAsync(JobTypeDto dto)
        {
            try
            {
                var result = await _unitOfWork.JobTypes.UpdateAsync(dto);
                await _unitOfWork.SaveChangesAsync();
                
                if (result)
                    return ServiceResult.Success("Job type updated successfully");
                else
                    return ServiceResult.Failure("Failed to update job type");
            }
            catch (Exception ex)
            {
                return ServiceResult.Failure($"Error updating job type: {ex.Message}");
            }
        }
        
        public async Task<ServiceResult> DeleteAsync(int id)
        {
            try
            {
                var result = await _unitOfWork.JobTypes.DeleteAsync(id);
                await _unitOfWork.SaveChangesAsync();
                
                if (result)
                    return ServiceResult.Success("Job type deleted successfully");
                else
                    return ServiceResult.Failure("Failed to delete job type");
            }
            catch (Exception ex)
            {
                return ServiceResult.Failure($"Error deleting job type: {ex.Message}");
            }
        }
        
        public async Task<bool> ExistsAsync(int id) => await _unitOfWork.JobTypes.ExistsAsync(id);
    }
} 