﻿// Order Progress Tracker
// ==================================================

/**
 * Order Progress Management Class
 * Handles order status tracking and progress bar updates
 */
class OrderProgressTracker {
    constructor() {
        this.progressBar = document.getElementById('progressBar');
        this.progressText = document.getElementById('progressText');
        this.steps = document.querySelectorAll('.progress-step');
        this.statusConfig = this.initializeStatusConfig();

        // Bind methods to maintain context
        this.updateProgressBar = this.updateProgressBar.bind(this);
        this.init = this.init.bind(this);
    }

    /**
     * Initialize status configuration mapping
     * @returns {Object} Status configuration object
     */
    initializeStatusConfig() {
        return {
            // Normal progression statuses
            [OrderStatus.DM]: {
                completedSteps: [0],
                activeStep: 1,
                percentage: 17,
                specialClass: null
            },
            [OrderStatus.A1]: {
                completedSteps: [0, 1],
                activeStep: 2,
                percentage: 34,
                specialClass: null
            },
            [OrderStatus.A2]: {
                completedSteps: [0, 1],
                activeStep: 2,
                percentage: 34,
                specialClass: null
            },
            [OrderStatus.A3]: {
                completedSteps: [0, 1],
                activeStep: 2,
                percentage: 34,
                specialClass: null
            },
            [OrderStatus.A4]: {
                completedSteps: [0, 1],
                activeStep: 2,
                percentage: 34,
                specialClass: null
            },
            [OrderStatus.B]: {
                completedSteps: [0, 1, 2],
                activeStep: 3,
                percentage: 51,
                specialClass: null
            },
            [OrderStatus.C]: {
                completedSteps: [0, 1, 2, 3],
                activeStep: 4,
                percentage: 68,
                specialClass: null
            },
            [OrderStatus.D]: {
                completedSteps: [0, 1, 2, 3, 4],
                activeStep: 5,
                percentage: 85,
                specialClass: null
            },
            [OrderStatus.Accepted]: {
                completedSteps: [0, 1, 2, 3, 4, 5],
                activeStep: null,
                percentage: 100,
                specialClass: null
            },

            // Return cases
            [OrderStatus.ReturnedByAssistantManager]: {
                completedSteps: [0, 2],
                activeStep: 1,
                percentage: 17,
                specialClass: null
            },
            [OrderStatus.ReturnedByCoordinator]: {
                completedSteps: [0, 1, 3],
                activeStep: 2,
                percentage: 34,
                specialClass: null
            },
            [OrderStatus.ReturnedBySupervisor]: {
                completedSteps: [0, 1, 2, 4],
                activeStep: 3,
                percentage: 51,
                specialClass: null
            },
            [OrderStatus.ReturnedByManager]: {
                completedSteps: [0, 1, 2, 3],
                activeStep: 4,
                percentage: 68,
                specialClass: null
            },

            // Action required cases
            [OrderStatus.ActionRequired]: {
                completedSteps: [0, 1, 2],
                activeStep: 3,
                percentage: 51,
                specialClass: 'needs-action'
            },
            [OrderStatus.ActionRequiredBySupervisor]: {
                completedSteps: [0, 1, 2, 3],
                activeStep: 4,
                percentage: 68,
                specialClass: 'needs-action'
            },

            // Cancellation cases
            [OrderStatus.CancelledByDepartmentManager]: {
                completedSteps: [0],
                activeStep: 1,
                percentage: 17,
                specialClass: 'cancelled'
            },
            [OrderStatus.CancelledByAssistantManager]: {
                completedSteps: [0, 1],
                activeStep: 2,
                percentage: 34,
                specialClass: 'cancelled'
            },
            [OrderStatus.CancelledBySupervisor]: {
                completedSteps: [0, 1],
                activeStep: 2,
                percentage: 51,
                specialClass: 'cancelled'
            },
            [OrderStatus.CancelledByCoordinator]: {
                completedSteps: [0, 1, 2],
                activeStep: 3,
                percentage: 68,
                specialClass: 'cancelled'
            },
            [OrderStatus.CancelledByManager]: {
                completedSteps: [0, 1, 2, 3, 4],
                activeStep: 5,
                percentage: 85,
                specialClass: 'cancelled'
            }
        };
    }

    /**
     * Reset all step classes to default state
     */
    resetSteps() {
        this.steps.forEach(step => {
            step.classList.remove('completed', 'active', 'needs-action', 'cancelled');
        });
    }

    /**
     * Apply step states based on configuration
     * @param {Object} config - Status configuration
     */
    applyStepStates(config) {
        // Mark completed steps
        config.completedSteps.forEach(stepIndex => {
            if (this.steps[stepIndex]) {
                this.steps[stepIndex].classList.add('completed');
            }
        });

        // Mark active step
        if (config.activeStep !== null && this.steps[config.activeStep]) {
            this.steps[config.activeStep].classList.add('active');

            // Apply special class if specified
            if (config.specialClass) {
                this.steps[config.activeStep].classList.remove('active');
                this.steps[config.activeStep].classList.add(config.specialClass);
            }
        }
    }

    /**
     * Update progress bar visual elements
     * @param {number} percentage - Progress percentage
     * @param {string} status - Current order status
     */
    updateProgressDisplay(percentage, status) {
        if (!this.progressBar || !this.progressText) return;

        this.progressBar.style.width = `${percentage}%`;
        this.progressText.textContent = `${percentage}% مكتمل`;

        // Add action required notification
        if (this.isActionRequired(status)) {
            this.progressText.innerHTML += ' <span class="text-warning">- يتطلب إجراءات</span>';
        }
    }

    /**
     * Check if status requires action
     * @param {string} status - Order status
     * @returns {boolean} True if action is required
     */
    isActionRequired(status) {
        return status === OrderStatus.ActionRequired ||
            status === OrderStatus.ActionRequiredBySupervisor;
    }

    /**
     * Handle unknown status
     */
    handleUnknownStatus() {
        if (this.steps[0]) {
            this.steps[0].classList.add('cancelled');
        }
        this.updateProgressDisplay(0, null);
    }

    /**
     * Update progress bar based on order status
     * @param {string} status - Current order status
     */
    updateProgressBar(status) {
        if (!status) {
            console.warn('No status provided to updateProgressBar');
            return;
        }

        const trimmedStatus = status.trim();
        const config = this.statusConfig[trimmedStatus];

        this.resetSteps();

        if (config) {
            this.applyStepStates(config);
            this.updateProgressDisplay(config.percentage, trimmedStatus);
        } else {
            console.warn(`Unknown status: ${trimmedStatus}`);
            this.handleUnknownStatus();
        }
    }

    /**
     * Initialize fade-in animations for sections
     */
    initializeFadeInAnimations() {
        const sections = document.querySelectorAll('.details-section');
        sections.forEach((section, index) => {
            section.style.opacity = '0';
            section.style.animation = `fadeInUp 0.8s ease ${index * 0.1}s forwards`;
        });
    }

    /**
     * Initialize the progress tracker
     * @param {string} initialStatus - Initial order status
     */
    init(initialStatus) {
        if (!initialStatus) {
            console.error('Initial status is required');
            return;
        }

        this.updateProgressBar(initialStatus);
        this.initializeFadeInAnimations();
    }
}



// OrderDetails functionality
// File upload functionality
function uploadFile(fileNumber) {
    const fileInput = document.getElementById(`FileUpload${fileNumber}`);
    const statusLabel = document.getElementById(`lblFile${fileNumber}Status`);

    if (!fileInput.files || fileInput.files.length === 0) {
        showStatus(statusLabel, 'يرجى اختيار ملف', 'warning');
        return;
    }

    const file = fileInput.files[0];

    // Validate file type
    if (file.type !== 'application/pdf') {
        showStatus(statusLabel, 'يجب أن يكون الملف بصيغة PDF', 'danger');
        return;
    }

    // Validate file size (5MB)
    if (file.size > 5242880) {
        showStatus(statusLabel, 'حجم الملف يتجاوز 5 ميجابايت', 'danger');
        return;
    }

    // Create FormData for upload
    const formData = new FormData();
    formData.append('file', file);
    formData.append('orderId', '@Model.Id');
    formData.append('fileNumber', fileNumber);

    // Show uploading status
    showStatus(statusLabel, 'جاري الرفع...', 'info');

    // Disable upload button
    const uploadBtn = fileInput.parentElement.parentElement.querySelector('.btn-modern');
    if (uploadBtn) {
        uploadBtn.disabled = true;
    }

    // Send upload request
    fetch('/Order/UploadFile', {
        method: 'POST',
        body: formData
    })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showStatus(statusLabel, 'تم الرفع بنجاح', 'success');
                fileInput.value = '';

                // Refresh page after short delay
                setTimeout(() => {
                    location.reload();
                }, 1500);
            } else {
                showStatus(statusLabel, data.message || 'حدث خطأ أثناء الرفع', 'danger');
            }
        })
        .catch(error => {
            console.error('Upload error:', error);
            showStatus(statusLabel, 'حدث خطأ في الاتصال', 'danger');
        })
        .finally(() => {
            if (uploadBtn) {
                uploadBtn.disabled = false;
            }
        });
}

// Status display function
function showStatus(element, message, type) {
    element.textContent = message;
    element.className = `status-badge bg-${type}`;

    // Add animation
    element.style.animation = 'none';
    setTimeout(() => {
        element.style.animation = 'fadeInUp 0.5s ease';
    }, 10);
}