using System.Linq;
using System.Threading.Tasks;
using OrderFlowCore.Web.ViewModels;
using OrderFlowCore.Application.Interfaces.Services;
using System.IO;
using System.Text;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.FileSystemGlobbing.Abstractions;

namespace OrderFlowCore.Web.Controllers
{
    public class SupervisorFollowUpController : Controller
    {
        private readonly ISupervisorsFollowUpService _followUpService;
        private readonly IOrderService _orderService;
        public SupervisorFollowUpController(ISupervisorsFollowUpService followUpService, IOrderService orderService)
        {
            _followUpService = followUpService;
            _orderService = orderService;
        }

        // GET: SupervisorFollowUp
        public async Task<ActionResult> Index(string selectedOrderNumber = null)
        {
            // Get supervisor ID from authenticated user
            string supervisorId = User.Identity?.Name ?? "supervisor";
            var records = await _followUpService.GetBySupervisorAsync(supervisorId);
            var model = new SupervisorFollowUpViewModel
            {
                FollowUpRecords = records.Select(r => new FollowUpRecordViewModel
                {
                    CivilRegistry = r.CivilRegistry,
                    OwnerName = r.OwnerName,
                    SpecialProcedure = r.SpecialProcedure
                }).ToList()
            };

            // Load order numbers for dropdown
            var pendingOrders = await _orderService.GetPendingOrdersForDirectMangerAsync();
            if (pendingOrders.IsSuccess)
            {
                model.OrderNumbers = pendingOrders.Data.Select(o => new OrderDropdownItem
                {
                    Value = o.Id.ToString(),
                    Text = string.IsNullOrEmpty(o.EmployeeName) ? o.Id.ToString() : o.Id + " | " + o.EmployeeName
                }).ToList();
            }

            // If an order is selected, load its details
            if (!string.IsNullOrEmpty(selectedOrderNumber) && int.TryParse(selectedOrderNumber, out int orderId))
            {
                var detailsResult = await _orderService.GetOrderDetailsAsync(orderId);
                if (detailsResult.IsSuccess && detailsResult.Data != null)
                {
                    model.OrderDetails = OrderDetailsViewModel.FromDto(detailsResult.Data);
                }
            }

            return View(model);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<ActionResult> ConfirmOrder(string selectedOrderNumber)
        {
            // Get username from authenticated user
            string username = User.Identity?.Name ?? "user";
            var result = await _orderService.ConfirmOrderByDirectManagerAsync(int.Parse(selectedOrderNumber), username);
            TempData["Message"] = result.Message;
            return RedirectToAction("Index");
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<ActionResult> NeedsAction(int selectedOrderNumber, string actionRequired)
        {
            // Get username from authenticated user
            string username = User.Identity?.Name ?? "user";
            var result = await _orderService.MarkOrderNeedsActionAsync(selectedOrderNumber, actionRequired, username);
            TempData["Message"] = result.Message;
            return RedirectToAction("Index");
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<ActionResult> RejectOrder(int selectedOrderNumber, string rejectReason)
        {
            // Get username from authenticated user
            string username = User.Identity?.Name ?? "user";
            var result = await _orderService.ReturnOrderAsync(selectedOrderNumber, rejectReason, username);
            TempData["Message"] = result.Message;
            return RedirectToAction("Index");
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<ActionResult> AddFollowUp(string civilRegistry, string ownerName, string specialProcedure)
        {
            // Get supervisor ID from authenticated user
            string supervisorId = User.Identity?.Name ?? "supervisor";
            var record = new OrderFlowCore.Core.Models.SupervisorsFollowUp
            {
                SupervisorId = supervisorId,
                CivilRegistry = civilRegistry,
                OwnerName = ownerName,
                SpecialProcedure = specialProcedure
            };
            await _followUpService.AddAsync(record);
            TempData["Message"] = "تمت إضافة السجل بنجاح";
            return RedirectToAction("Index");
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<ActionResult> EditFollowUp(string civilRegistry, string ownerName, string specialProcedure)
        {
            string supervisorId = "testSupervisor";
            var record = new OrderFlowCore.Core.Models.SupervisorsFollowUp
            {
                SupervisorId = supervisorId,
                CivilRegistry = civilRegistry,
                OwnerName = ownerName,
                SpecialProcedure = specialProcedure
            };
            await _followUpService.UpdateAsync(record);
            TempData["Message"] = "تم تعديل السجل بنجاح";
            return RedirectToAction("Index");
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<ActionResult> DeleteFollowUp(string civilRegistry)
        {
            string supervisorId = "testSupervisor";
            await _followUpService.DeleteAsync(supervisorId, civilRegistry);
            TempData["Message"] = "تم حذف السجل بنجاح";
            return RedirectToAction("Index");
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<ActionResult> ImportCsv(IFormFile csvFile)
        {
            if (csvFile == null || csvFile.Length == 0)
            {
                TempData["Message"] = "يرجى اختيار ملف CSV.";
                return RedirectToAction("Index");
            }
            string supervisorId = "testSupervisor";
            int successCount = 0, errorCount = 0;
            var errorMessages = new StringBuilder();

            // read file line by line
            using (var reader = new StreamReader(csvFile.OpenReadStream(), Encoding.UTF8) )
            {
                string line;
                bool isFirstLine = true;
                while ((line = reader.ReadLine()) != null)
                {
                    if (isFirstLine) { isFirstLine = false; continue; }
                    var fields = line.Split(';');
                    if (fields.Length < 3)
                    {
                        errorCount++;
                        errorMessages.AppendLine($"سطر غير صالح: {line}");
                        continue;
                    }
                    var record = new OrderFlowCore.Core.Models.SupervisorsFollowUp
                    {
                        SupervisorId = supervisorId,
                        CivilRegistry = fields[0].Trim(),
                        OwnerName = fields[1].Trim(),
                        SpecialProcedure = fields[2].Trim()
                    };
                    try
                    {
                        await _followUpService.AddAsync(record);
                        successCount++;
                    }
                    catch
                    {
                        errorCount++;
                        errorMessages.AppendLine($"خطأ في السطر: {line}");
                    }
                }
            }
            TempData["Message"] = $"تم استيراد {successCount} سجل بنجاح. أخطاء: {errorCount}. {errorMessages}";
            return RedirectToAction("Index");
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<ActionResult> ExportCsv()
        {
            string supervisorId = "testSupervisor";
            var records = await _followUpService.GetBySupervisorAsync(supervisorId);
            var sb = new StringBuilder();
            sb.AppendLine("CivilRegistry;OwnerName;SpecialProcedure");
            foreach (var r in records)
            {
                sb.AppendLine($"{r.CivilRegistry};{r.OwnerName};{r.SpecialProcedure}");
            }
            var bytes = Encoding.UTF8.GetBytes(sb.ToString());
            return File(bytes, "text/csv", "FollowUpRecords.csv");
        }

        // Additional actions can be added here as needed for enhanced functionality
    }
} 