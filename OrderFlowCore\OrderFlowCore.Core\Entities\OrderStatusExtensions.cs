using System;

namespace OrderFlowCore.Core.Entities
{
    public static class OrderStatusExtensions
    {
        public static string ToDisplayString(this OrderStatus status)
        {
            return status switch
            {
                OrderStatus.DM => "(DM)",
                OrderStatus.A1 => "(A1)",
                OrderStatus.A2 => "(A2)",
                OrderStatus.A3 => "(A3)",
                OrderStatus.A4 => "(A4)",
                OrderStatus.B => "(B)",
                OrderStatus.C => "(C)",
                OrderStatus.D => "(D)",

                OrderStatus.ReturnedByCoordinator => "أُعيد بواسطة أحد المنسقين",
                OrderStatus.ReturnedBySupervisor => "أُعيد بواسطة أحد المشرفين",
                OrderStatus.ReturnedByAssistantManager => "تمت الإعادة من مساعد المدير",
                OrderStatus.ReturnedByManager => "تمت الإعادة من المدير",

                OrderStatus.ActionRequired => "يتطلب إجراءات",
                OrderStatus.ActionRequiredBySupervisor => "يتطلب إجراءات من المشرف",

                OrderStatus.CancelledByDepartmentManager => "تم الإلغاء من مدير القسم",
                OrderStatus.CancelledBySupervisor => "تم الإلغاء من قبل المشرف",
                OrderStatus.CancelledByCoordinator => "تم الإلغاء من قبل المنسق",
                OrderStatus.CancelledByManager => "تم الإلغاء من قبل المدير",
                OrderStatus.CancelledByAssistantManager => "تم الإلغاء من مساعد المدير",

                OrderStatus.Accepted => "مقبول",
                _ => status.ToString()
            };
        }
    }
} 