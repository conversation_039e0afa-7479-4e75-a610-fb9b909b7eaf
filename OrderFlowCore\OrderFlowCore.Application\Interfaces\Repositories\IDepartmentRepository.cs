using OrderFlowCore.Application.DTOs;

namespace OrderFlowCore.Application.Interfaces.Repositories;

public interface IDepartmentRepository
{
    Task<IEnumerable<DepartmentDto>> GetAllAsync();
    Task<DepartmentDto?> GetByIdAsync(int id);
    Task<DepartmentDto?> GetByNameAsync(string name);
    Task<DepartmentDto> CreateAsync(DepartmentDto department);
    Task<DepartmentDto?> UpdateAsync(DepartmentDto department);
    Task<bool> DeleteAsync(int id);
    Task<bool> ExistsAsync(string name, int? excludeId = null);
} 