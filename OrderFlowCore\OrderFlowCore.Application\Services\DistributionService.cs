using Microsoft.Extensions.Logging;
using OrderFlowCore.Application.Common;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Application.Interfaces.Services;
using OrderFlowCore.Core.Entities;

namespace OrderFlowCore.Application.Services;

public class DistributionService : IDistributionService
{
    private readonly IDepartmentService _departmentService;
    private readonly ILogger<DistributionService> _logger;

    public DistributionService(
        IDepartmentService departmentService,
        ILogger<DistributionService> logger)
    {
        _departmentService = departmentService;
        _logger = logger;
    }

    public async Task<ServiceResult<DistributionDto>> GetDistributionDataAsync()
    {
        try
        {
            var departmentsResult = await _departmentService.GetAllDepartmentsAsync();
            if (!departmentsResult.IsSuccess || departmentsResult.Data == null)
            {
                return ServiceResult<DistributionDto>.Failure("حدث خطأ في تحميل بيانات الأقسام");
            }

            var departments = departmentsResult.Data.ToList();
            var dto = new DistributionDto();

            // Load available departments
            dto.AvailableDepartments = departments.Select(d => d.Name).ToList();

            // Load current distribution
            dto.DepartmentDistribution = departments.Select(d => new DepartmentDistributionDto
            {
                DepartmentName = d.Name,
                AssistantManagerId = d.AssistantManagerId
            }).ToList();

            // Load distribution statistics
            var statsGroups = departments
                .GroupBy(d => d.AssistantManagerId)
                .Select(g => new DistributionStatsDto
                {
                    AssistantManager = g.Key,
                    TotalDepartments = g.Count()
                }).ToList();

            dto.DistributionStats = statsGroups;

            return ServiceResult<DistributionDto>.Success(dto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading distribution data");
            return ServiceResult<DistributionDto>.Failure("حدث خطأ أثناء تحميل بيانات التوزيع");
        }
    }

    public async Task<ServiceResult> UpdateDepartmentDistributionAsync(string departmentName, OrderFlowCore.Core.Entities.AssistantManagerType assistantManagerId)
    {
        try
        {
            if (string.IsNullOrEmpty(departmentName))
            {
                return ServiceResult.Failure("اسم القسم مطلوب");
            }

            if (assistantManagerId == OrderFlowCore.Core.Entities.AssistantManagerType.Unknown)
            {
                return ServiceResult.Failure("مساعد المدير مطلوب");
            }

            // Get all departments to find the one to update
            var departmentsResult = await _departmentService.GetAllDepartmentsAsync();
            if (!departmentsResult.IsSuccess || departmentsResult.Data == null)
            {
                return ServiceResult.Failure("حدث خطأ في تحميل بيانات الأقسام");
            }

            // Find the department by name
            var department = departmentsResult.Data.FirstOrDefault(d => d.Name == departmentName);
            if (department == null)
            {
                return ServiceResult.Failure("القسم المحدد غير موجود");
            }

            // Update the department's assistant manager
            department.AssistantManagerId = assistantManagerId;

            var updateResult = await _departmentService.UpdateDepartmentAsync(department);
            if (updateResult.IsSuccess)
            {
                var assistantManagerName = assistantManagerId.ToDisplayName();
                return ServiceResult.Success($"تم تحديث توزيع قسم '{departmentName}' إلى '{assistantManagerName}' بنجاح");
            }

            return ServiceResult.Failure(updateResult.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating distribution for department {DepartmentName}", departmentName);
            return ServiceResult.Failure("حدث خطأ أثناء تحديث التوزيع");
        }
    }

    public async Task<ServiceResult<BulkDistributionResult>> BulkUpdateDistributionAsync(List<string> departmentNames, OrderFlowCore.Core.Entities.AssistantManagerType assistantManagerId)
    {
        try
        {
            if (departmentNames == null || !departmentNames.Any())
            {
                return ServiceResult<BulkDistributionResult>.Failure("يرجى اختيار قسم واحد على الأقل");
            }

            if (assistantManagerId == OrderFlowCore.Core.Entities.AssistantManagerType.Unknown)
            {
                return ServiceResult<BulkDistributionResult>.Failure("مساعد المدير مطلوب");
            }

            // Get all departments
            var departmentsResult = await _departmentService.GetAllDepartmentsAsync();
            if (!departmentsResult.IsSuccess || departmentsResult.Data == null)
            {
                return ServiceResult<BulkDistributionResult>.Failure("حدث خطأ في تحميل بيانات الأقسام");
            }

            var allDepartments = departmentsResult.Data.ToList();
            var result = new BulkDistributionResult
            {
                AssistantManagerName = assistantManagerId.ToDisplayName()
            };

            // Update each selected department
            foreach (var departmentName in departmentNames)
            {
                var department = allDepartments.FirstOrDefault(d => d.Name == departmentName);
                if (department == null)
                {
                    result.Errors.Add($"القسم '{departmentName}' غير موجود");
                    continue;
                }

                // Update the department's assistant manager
                department.AssistantManagerId = assistantManagerId;

                var updateResult = await _departmentService.UpdateDepartmentAsync(department);
                if (updateResult.IsSuccess)
                {
                    result.UpdatedCount++;
                }
                else
                {
                    result.Errors.Add($"فشل في تحديث قسم '{departmentName}': {updateResult.Message}");
                }
            }

            return ServiceResult<BulkDistributionResult>.Success(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error bulk updating distribution for {DepartmentCount} departments", departmentNames?.Count ?? 0);
            return ServiceResult<BulkDistributionResult>.Failure("حدث خطأ أثناء تحديث التوزيع");
        }
    }

    public async Task<ServiceResult> ClearDepartmentDistributionAsync(string departmentName)
    {
        try
        {
            if (string.IsNullOrEmpty(departmentName))
            {
                return ServiceResult.Failure("اسم القسم مطلوب");
            }

            // Get all departments
            var departmentsResult = await _departmentService.GetAllDepartmentsAsync();
            if (!departmentsResult.IsSuccess || departmentsResult.Data == null)
            {
                return ServiceResult.Failure("حدث خطأ في تحميل بيانات الأقسام");
            }

            // Find the department by name
            var department = departmentsResult.Data.FirstOrDefault(d => d.Name == departmentName);
            if (department == null)
            {
                return ServiceResult.Failure("القسم المحدد غير موجود");
            }

            // Clear the assistant manager assignment
            department.AssistantManagerId = AssistantManagerType.Unknown;

            var updateResult = await _departmentService.UpdateDepartmentAsync(department);
            if (updateResult.IsSuccess)
            {
                return ServiceResult.Success($"تم إلغاء توزيع قسم '{departmentName}' بنجاح");
            }

            return ServiceResult.Failure(updateResult.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error clearing distribution for department {DepartmentName}", departmentName);
            return ServiceResult.Failure("حدث خطأ أثناء إلغاء التوزيع");
        }
    }

    public async Task<ServiceResult<DistributionReportDto>> GetDistributionReportAsync()
    {
        try
        {
            var departmentsResult = await _departmentService.GetAllDepartmentsAsync();
            if (!departmentsResult.IsSuccess || departmentsResult.Data == null)
            {
                return ServiceResult<DistributionReportDto>.Failure("حدث خطأ في تحميل بيانات الأقسام");
            }

            var departments = departmentsResult.Data.ToList();
            var report = new DistributionReportDto
            {
                TotalDepartments = departments.Count,
                AssignedDepartments = departments.Count(d => d.AssistantManagerId != AssistantManagerType.Unknown),
                UnassignedDepartments = departments.Count(d => d.AssistantManagerId == AssistantManagerType.Unknown),
                DistributionByManager = departments
                    .Where(d => d.AssistantManagerId != AssistantManagerType.Unknown)
                    .GroupBy(d => d.AssistantManagerId)
                    .Select(g => new ManagerDistributionDto
                    {
                        ManagerId = g.Key,
                        ManagerName = g.Key.ToDisplayName(),
                        DepartmentCount = g.Count(),
                        Departments = g.Select(d => d.Name).ToList()
                    }).ToList()
            };

            return ServiceResult<DistributionReportDto>.Success(report);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating distribution report");
            return ServiceResult<DistributionReportDto>.Failure("حدث خطأ أثناء إنشاء التقرير");
        }
    }

    public string GetAssistantManagerName(OrderFlowCore.Core.Entities.AssistantManagerType assistantManagerId)
    {
        return assistantManagerId.ToDisplayName();
    }
}
