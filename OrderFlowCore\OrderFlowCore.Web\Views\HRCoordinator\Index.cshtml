@model OrderFlowCore.Web.ViewModels.HRCoordinatorViewModel
@{
    ViewData["Title"] = "منسق الموارد البشرية";
}

<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <h2 class="text-center mb-4">منسق الموارد البشرية</h2>

            <!-- Order Selection Section -->
            <div class="order-select-container">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <label for="orderSelect" class="form-label text-white">📋 رقم الطلب:</label>
                        @Html.DropDownListFor(m => m.SelectedOrderId, Model.OrderNumbers, "اختر الطلب من القائمة",
                            new { @class = "form-select", @id = "orderSelect", onchange = "loadOrderDetails(this.value)" })
                    </div>
                    <div class="col-md-6">
                        <div class="text-center">
                            <p class="mb-0">اختر طلباً لعرض تفاصيله واتخاذ الإجراء المناسب</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions Section -->
            <div id="quickActions" class="quick-actions" style="display: none;">
                <h4 class="text-center mb-4">🚀 الإجراءات السريعة</h4>

                <!-- Row 1: Path Buttons -->
                <div class="row mb-3 p-3 bg-light rounded border">
                    <div class="col-md-2 mb-2">
                        <button type="button" class="btn btn-secondary w-100" onclick="applyPath(1)">مسار 1</button>
                    </div>
                    <div class="col-md-2 mb-2">
                        <button type="button" class="btn btn-secondary w-100" onclick="applyPath(2)">مسار 2</button>
                    </div>
                    <div class="col-md-2 mb-2">
                        <button type="button" class="btn btn-secondary w-100" onclick="applyPath(3)">مسار 3</button>
                    </div>
                    <div class="col-md-2 mb-2">
                        <button type="button" class="btn btn-secondary w-100" onclick="applyPath(4)">مسار 4</button>
                    </div>
                    <div class="col-md-2 mb-2">
                        <button type="button" class="btn btn-secondary w-100" onclick="applyPath(5)">مسار 5</button>
                    </div>
                    <div class="col-md-2 mb-2">
                        <button type="button" class="btn btn-secondary w-100" onclick="applyPath(6)">مسار 6</button>
                    </div>
                </div>

                <!-- Row 2: Auto Route and Selection -->
                <div class="row mb-3 p-3 bg-light rounded border">
                    <div class="col-md-3 mb-2">
                        <button type="button" id="btnAutoPath" class="btn btn-info w-100" onclick="autoRouteOrder()" disabled>
                            <span class="action-icon">🎯</span>التوجيه التلقائي
                        </button>
                    </div>
                    <div class="col-md-3 mb-2">
                        <button type="button" class="btn btn-success w-100" onclick="selectAllSupervisors()">
                            <span class="action-icon">✅</span>تحديد الكل
                        </button>
                    </div>
                    <div class="col-md-3 mb-2">
                        <button type="button" class="btn btn-warning w-100" onclick="unselectAllSupervisors()">
                            <span class="action-icon">❌</span>إلغاء الكل
                        </button>
                    </div>
                    <div class="col-md-3 mb-2">
                        <button type="button" class="btn btn-danger w-100" onclick="directToManager()">
                            <span class="action-icon">⚡</span>تحويل للمدير
                        </button>
                    </div>
                </div>

                <!-- Row 3: Download Attachments -->
                <div class="row mb-3 p-3 bg-light rounded border">
                    <div class="col-md-6 mb-2">
                        <button id="downloadAttachmentsBtn" class="btn download-button w-100" onclick="downloadAttachments()">
                            <span class="action-icon">📎</span>تحميل المرفقات
                        </button>
                    </div>
                    <div class="col-md-6 mb-2">
                        <button type="button" class="btn btn-info w-100" onclick="toggleRestoreSection()">
                            <span class="action-icon">🔄</span>قسم الاستعادة
                        </button>
                    </div>
                </div>
            </div>

            <!-- Auto Path Info Panel -->
            <div id="AutoPathInfoPanel" style="display: none;" class="alert alert-info mb-4"></div>

            <!-- Rejection Alert Panel -->
            <div id="RejectionAlertPanel" style="display: none;" class="mb-3"></div>

            <!-- Supervisors Selection Section -->
            <div id="supervisorsSection" class="supervisors-section" style="display: none;">
                <h5 class="text-center mb-3">اختيار الأقسام للاعتماد</h5>
                <div class="supervisors-container">
                    <div class="form-check">
                        <input type="checkbox" class="form-check-input" id="chk1" name="SelectedSupervisors" value="خدمات الموظفين" />
                        <label class="form-check-label" for="chk1">خدمات الموظفين</label>
                    </div>
                    <div class="form-check">
                        <input type="checkbox" class="form-check-input" id="chk2" name="SelectedSupervisors" value="إدارة تخطيط الموارد البشرية" />
                        <label class="form-check-label" for="chk2">إدارة تخطيط الموارد البشرية</label>
                    </div>
                    <div class="form-check">
                        <input type="checkbox" class="form-check-input" id="chk3" name="SelectedSupervisors" value="إدارة تقنية المعلومات" />
                        <label class="form-check-label" for="chk3">إدارة تقنية المعلومات</label>
                    </div>
                    <div class="form-check">
                        <input type="checkbox" class="form-check-input" id="chk4" name="SelectedSupervisors" value="مراقبة الدوام" />
                        <label class="form-check-label" for="chk4">مراقبة الدوام</label>
                    </div>
                    <div class="form-check">
                        <input type="checkbox" class="form-check-input" id="chk5" name="SelectedSupervisors" value="السجلات الطبية" />
                        <label class="form-check-label" for="chk5">السجلات الطبية</label>
                    </div>
                    <div class="form-check">
                        <input type="checkbox" class="form-check-input" id="chk6" name="SelectedSupervisors" value="إدارة الرواتب والاستحقاقات" />
                        <label class="form-check-label" for="chk6">إدارة الرواتب والاستحقاقات</label>
                    </div>
                    <div class="form-check">
                        <input type="checkbox" class="form-check-input" id="chk7" name="SelectedSupervisors" value="إدارة القانونية والالتزام" />
                        <label class="form-check-label" for="chk7">إدارة القانونية والالتزام</label>
                    </div>
                    <div class="form-check">
                        <input type="checkbox" class="form-check-input" id="chk8" name="SelectedSupervisors" value="خدمات الموارد البشرية" />
                        <label class="form-check-label" for="chk8">خدمات الموارد البشرية</label>
                    </div>
                    <div class="form-check">
                        <input type="checkbox" class="form-check-input" id="chk9" name="SelectedSupervisors" value="إدارة الإسكان" />
                        <label class="form-check-label" for="chk9">إدارة الإسكان</label>
                    </div>
                    <div class="form-check">
                        <input type="checkbox" class="form-check-input" id="chk10" name="SelectedSupervisors" value="قسم الملفات" />
                        <label class="form-check-label" for="chk10">قسم الملفات</label>
                    </div>
                    <div class="form-check">
                        <input type="checkbox" class="form-check-input" id="chk11" name="SelectedSupervisors" value="العيادات الخارجية" />
                        <label class="form-check-label" for="chk11">العيادات الخارجية</label>
                    </div>
                    <div class="form-check">
                        <input type="checkbox" class="form-check-input" id="chk12" name="SelectedSupervisors" value="التأمينات الاجتماعية" />
                        <label class="form-check-label" for="chk12">التأمينات الاجتماعية</label>
                    </div>
                    <div class="form-check">
                        <input type="checkbox" class="form-check-input" id="chk13" name="SelectedSupervisors" value="وحدة مراقبة المخزون" />
                        <label class="form-check-label" for="chk13">وحدة مراقبة المخزون</label>
                    </div>
                    <div class="form-check">
                        <input type="checkbox" class="form-check-input" id="chk14" name="SelectedSupervisors" value="إدارة تنمية الإيرادات" />
                        <label class="form-check-label" for="chk14">إدارة تنمية الإيرادات</label>
                    </div>
                    <div class="form-check">
                        <input type="checkbox" class="form-check-input" id="chk15" name="SelectedSupervisors" value="إدارة الأمن و السلامة" />
                        <label class="form-check-label" for="chk15">إدارة الأمن و السلامة</label>
                    </div>
                    <div class="form-check">
                        <input type="checkbox" class="form-check-input" id="chk16" name="SelectedSupervisors" value="الطب الاتصالي" />
                        <label class="form-check-label" for="chk16">الطب الاتصالي</label>
                    </div>
                </div>
            </div>

            <!-- Form Inputs Section -->
            <div id="formInputsSection" class="form-inputs-section" style="display: none;">
                @using (Html.BeginForm())
                {
                    @Html.AntiForgeryToken()
                    @Html.HiddenFor(m => m.SelectedOrderId)

                    <div class="row mb-3">
                        <div class="col-12">
                            <label for="details" class="form-label">التفاصيل والرقم</label>
                            @Html.TextAreaFor(m => m.Details, new { @class = "form-control", @id = "details", placeholder = "التفاصيل والرقم", rows = "3" })
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-12">
                            <label for="actionRequired" class="form-label">الإجراءات المطلوبة</label>
                            @Html.TextBoxFor(m => m.ActionRequired, new { @class = "form-control", @id = "actionRequired", placeholder = "الإجراءات المطلوبة" })
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-12">
                            <label for="returnReason" class="form-label">سبب الإلغاء/الإعادة</label>
                            @Html.TextAreaFor(m => m.ReturnReason, new { @class = "form-control", @id = "returnReason", placeholder = "سبب الإلغاء/الإعادة", rows = "2" })
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="row mb-3 p-3 bg-light rounded border">
                        <div class="col-md-3 mb-2">
                            <button type="button" class="btn submit-button w-100" onclick="submitOrder()">
                                <span class="action-icon">✅</span>تحويل الطلب
                            </button>
                        </div>
                        <div class="col-md-3 mb-2">
                            <button type="button" class="btn btn-info w-100" onclick="markNeedsAction()">
                                <span class="action-icon">⚠️</span>يتطلب إجراءات
                            </button>
                        </div>
                        <div class="col-md-3 mb-2">
                            <button type="button" class="btn btn-warning w-100" onclick="returnOrder()">
                                <span class="action-icon">↩️</span>إعادة الطلب
                            </button>
                        </div>
                        <div class="col-md-3 mb-2">
                            <button type="button" class="btn btn-danger w-100" onclick="rejectOrder()">
                                <span class="action-icon">❌</span>إلغاء الطلب
                            </button>
                        </div>
                    </div>
                }
            </div>

            <div id="messageContainer" class="mt-3"></div>

            <!-- Loading -->
            <div id="loading" class="loading">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">جاري التحميل...</span>
                </div>
                <p class="mt-2">جاري تحميل تفاصيل الطلب...</p>
            </div>

            <!-- Restore Section -->
            <div id="RestoreSection" class="restore-section" style="display: none;">
                <h5 class="text-center mb-3">🔄 قسم الاستعادة</h5>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="txtRestoreSearch" class="form-label">البحث</label>
                        <input type="text" id="txtRestoreSearch" class="form-control" placeholder="ابحث عن طلب..." />
                    </div>
                    <div class="col-md-4">
                        <label for="ddlRestoreFilter" class="form-label">الفلتر</label>
                        <select id="ddlRestoreFilter" class="form-select">
                            <option value="today">اليوم</option>
                            <option value="week">هذا الأسبوع</option>
                            <option value="month">هذا الشهر</option>
                        </select>
                    </div>
                    <div class="col-md-2 d-flex align-items-end">
                        <button type="button" class="btn btn-primary w-100" onclick="searchRestorableOrders()">بحث</button>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-12">
                        <label for="ddlRestorableOrders" class="form-label">الطلبات القابلة للاستعادة</label>
                        <select id="ddlRestorableOrders" class="form-select" onchange="loadRestoreOrderDetails(this.value)">
                            <option value="">-- اختر الطلب للاستعادة --</option>
                        </select>
                    </div>
                </div>

                <div id="RestoreDetailsPanel" style="display: none;" class="mb-3">
                    <div class="alert alert-info">
                        <strong>الحالة الحالية:</strong> <span id="lblRestoreCurrentStatus"></span><br>
                        <strong>تاريخ التحويل:</strong> <span id="lblRestoreTransferDate"></span><br>
                        <strong>المشرفين المعينين:</strong> <span id="lblRestoreAssignedSupervisors"></span>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-12">
                        <label for="restoreNotes" class="form-label">ملاحظات الاستعادة</label>
                        <textarea name="RestoreNotes" class="form-control" rows="2" placeholder="ملاحظات اختيارية..."></textarea>
                    </div>
                </div>

                <div class="text-center">
                    <button type="button" class="btn btn-success" onclick="restoreOrder()">
                        <span class="action-icon">🔄</span>استعادة الطلب
                    </button>
                </div>
            </div>

            <!-- Order Details Section -->
            @await Html.PartialAsync("_OrderDetailsPartial")
        </div>
    </div>
</div>

@section Scripts {
    <script src="~/js/shared-utils.js"></script>
    <script src="~/js/orderDetailsModule.js"></script>
    <script src="~/js/hrCoordinator.js"></script>
    <script>
        // Show success/error messages from TempData
        @if (TempData["SuccessMessage"] != null)
        {
            <text>OrderDetailsModule.showMessage('@TempData["SuccessMessage"]', 'success');</text>
        }
        @if (TempData["ErrorMessage"] != null)
        {
            <text>OrderDetailsModule.showMessage('@TempData["ErrorMessage"]', 'error');</text>
        }

        // Initialize page when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize order selection change handler
            const orderSelect = document.getElementById('orderSelect');
            if (orderSelect) {
                orderSelect.addEventListener('change', function() {
                    const orderId = this.value;
                    if (orderId) {
                        document.getElementById('quickActions').style.display = 'block';
                        document.getElementById('supervisorsSection').style.display = 'block';
                        document.getElementById('formInputsSection').style.display = 'block';
                        loadOrderDetails(orderId);
                    } else {
                        document.getElementById('quickActions').style.display = 'none';
                        document.getElementById('supervisorsSection').style.display = 'none';
                        document.getElementById('formInputsSection').style.display = 'none';
                        hideOrderDetails();
                    }
                });
            }
        });

        function hideOrderDetails() {
            const orderDetailsPanel = document.getElementById('OrderDetailsPanel');
            const autoPathPanel = document.getElementById('AutoPathInfoPanel');
            const rejectionPanel = document.getElementById('RejectionAlertPanel');

            if (orderDetailsPanel) orderDetailsPanel.style.display = 'none';
            if (autoPathPanel) autoPathPanel.style.display = 'none';
            if (rejectionPanel) rejectionPanel.style.display = 'none';
        }
    </script>
}