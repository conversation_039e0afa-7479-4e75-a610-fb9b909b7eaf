@model OrderFlowCore.Web.ViewModels.HRCoordinatorViewModel
@{
    ViewData["Title"] = "منسق الموارد البشرية";
}

<style>
    /* تنسيق زر اعادة */
    .return-button {
        background-color: #ff9400;
        border-style: none;
        border-color: inherit;
        border-width: medium;
        padding: 12px;
        border-radius: 4px;
        font-size: 16px;
        cursor: pointer;
        width: 100%;
        transition: background-color 0.3s ease;
        margin-top: 15px;
        color: white;
    }

    /* تأثير عند مرور الماوس على الأزرار */
    .return-button:hover, .submit-button:hover {
        background-color: #bd6301;
    }

    /* تنسيق زر يتطلب إجراءات */
    .action-button {
        background-color: #007bff;
        border-style: none;
        border-color: inherit;
        border-width: medium;
        padding: 12px;
        border-radius: 4px;
        font-size: 16px;
        cursor: pointer;
        width: 100%;
        transition: background-color 0.3s ease;
        margin-top: 15px;
        color: white;
    }

    /* تأثير عند مرور الماوس على الأزرار */
    .action-button:hover, .submit-button:hover {
        background-color: #0056b3;
    }

    .resize-enabled {
        resize: both; /* يتيح تغيير الحجم في الاتجاهين */
        overflow: auto; /* لضمان ظهور شريط التمرير إذا لزم الأمر */
    }

    .form-container {
        max-width: 1500px;
        margin: 0 auto;
        padding: 15px;
        border: 1px solid #ddd;
        border-radius: 8px;
        background-color: #f9f9f9;
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        font-family: Arial, sans-serif;
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        justify-content: space-between;
    }

    .textbox1, .btn-container {
        flex: 1;
    }

    .form-header {
        text-align: center;
        margin-bottom: 20px;
        color: #333;
    }

    .checkbox-table {
        width: 100%;
        border-collapse: collapse;
        margin: 20px 0;
        max-width: 1200px;
    }

    .checkbox-table th, .checkbox-table td {
        padding: 12px;
        text-align: right;
        vertical-align: top;
    }

    .btn-container {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 10px;
    }

    .submit-button, .reject-button {
        width: auto;
        padding: 12px 20px
    }

    .btn {
        border: none;
        padding: 12px 20px;
        margin: 5px;
        border-radius: 4px;
        font-size: 16px;
        cursor: pointer;
        transition: background-color 0.3s ease;
        background-color: gray;
        color: white;
    }

    .btnok {
        border: none;
        padding: 12px 20px;
        margin: 5px;
        border-radius: 4px;
        font-size: 16px;
        cursor: pointer;
        transition: background-color 0.3s ease;
        background-color: dodgerblue;
        color: white;
    }

    .btn:hover {
        background-color: #45a049;
    }

    .custom-dropdown {
        padding: 10px;
        box-sizing: border-box;
        border: 1px solid #ddd;
        border-radius: 4px;
        margin-bottom: 20px;
    }

    .custom-dropdown:focus {
        border-color: #4CAF50;
        outline: none;
    }

    .checkbox-container {
        display: flex;
        align-items: center;
    }

    .checkbox-container input[type="checkbox"] {
        margin-left: 8px;
    }

    .submit-button, .reject-button {
        border-style: none;
        border-color: inherit;
        border-width: medium;
        padding: 12px;
        border-radius: 4px;
        font-size: 16px;
        cursor: pointer;
        width: 100%;
        transition: background-color 0.3s ease;
    }

    .submit-button {
        margin-bottom: 15px;
        background-color: #4CAF50;
        color: white;
    }

    .submit-button:hover {
        background-color: #45a049;
    }

    .reject-button {
        background-color: red;
        color: white;
    }

    .reject-button:hover {
        background-color: #dd0000;
    }

    .textbox1 {
        width: 100%;
        max-width: 750px;
        margin-bottom: 20px;
        padding: 10px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 16px;
        box-sizing: border-box;
    }

    .order-details {
        margin: 20px 0;
        padding: 10px;
        border: 1px solid #ddd;
        background-color: #f9f9f9;
        border-radius: 4px;
    }

    .order-details h3 {
        margin-top: 0;
    }

    .order-details p {
        margin: 5px 0;
    }

    /* تنظيم واحد لخصائص الجدول */
    .details-table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
        margin-bottom: 20px;
        max-width: 1500px; /* يمكنك زيادة الحد الأقصى للعرض حسب الحاجة */
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        border-radius: 12px;
        overflow: hidden;
        border: 2px solid #007bff;
    }

    .details-table th {
        background-color: #f2f2f2;
        color: #333;
        font-weight: bold;
        font-size: 18px;
        border-right: 1px solid #ddd;
    }

    .details-table th,
    .details-table td {
        padding: 16px;
        text-align: center; /* توسيط النص داخل الخلايا */
        vertical-align: middle; /* توسيط النص عموديًا */
        border: 1px solid #ddd; /* إضافة حواف للخلايا */
    }

    .details-table th {
        background-color: #f2f2f2;
        color: #333;
        font-weight: bold;
        border-right: 1px solid #ddd;
    }

    .details-table td {
        font-size: 16px;
        color: #333;
        background-color: #ffffff;
    }

    .details-table tr:nth-child(even) td {
        background-color: #ffffff;
    }

    .details-table tr:hover td {
        background-color: #e9e9e9;
    }

    .details-table td:first-child {
        border-left: 1px solid #ddd; /* تأكد من وجود الحافة اليسرى */
    }

    .details-table td:last-child {
        border-right: 1px solid #ddd; /* تأكد من وجود الحافة اليمنى */
    }

    /* الحواف المستديرة لأول وآخر صف */
    .details-table tr:first-child th:first-child {
        border-top-left-radius: 12px;
    }

    .details-table tr:first-child th:last-child {
        border-top-right-radius: 12px;
    }

    .details-table tr:last-child td:first-child {
        border-bottom-left-radius: 12px;
    }

    .details-table tr:last-child td:last-child {
        border-bottom-right-radius: 12px;
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
        .details-table {
            font-size: 14px;
        }

        .details-table td {
            font-size: 12px;
            padding: 8px;
        }
    }

    .full-table-container {
        border: 3px solid #007bff; /* إطار خارجي أزرق */
        padding: 20px; /* مسافة داخلية بين الإطار والمحتوى */
        border-radius: 15px; /* حواف مستديرة للإطار الخارجي */
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2); /* ظل الإطار الخارجي */
        margin-top: 20px; /* مسافة بين الإطار العلوي والمحتوى */
    }

    /* لون زر التوجيه التلقائي */
    .auto-path-btn {
        background-color: #0056b3;
        color: white;
        padding: 12px 20px;
        border-radius: 4px;
        border: none;
        cursor: pointer;
        margin: 5px;
        transition: all 0.3s ease;
    }

    .auto-path-btn:hover {
        background-color: #003d82;
    }

    .auto-path-active {
        background-color: #28a745;
    }

    .btn-group {
        display: flex;
        gap: 5px;
        margin-bottom: 15px;
    }

    .supervisors-container {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 10px;
        margin: 15px 0;
        padding: 15px;
        background-color: #f8f9fa;
        border-radius: 8px;
        border: 1px solid #dee2e6;
    }

    .checkbox-container {
        padding: 8px;
        border-radius: 4px;
        transition: background-color 0.2s;
    }

    .checkbox-container:hover {
        background-color: #e9ecef;
    }

    .action-buttons {
        display: flex;
        gap: 10px;
        margin-top: 15px;
    }

    .action-buttons .submit-button,
    .action-buttons .reject-button,
    .action-buttons .return-button {
        flex: 1;
    }

    .spinner {
        width: 40px;
        height: 40px;
        border: 4px solid #f3f3f3;
        border-top: 4px solid #3498db;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .auto-path-btn.disabled {
        background-color: #cccccc;
        cursor: not-allowed;
    }

    .rtl-alert {
        direction: rtl !important;
    }

    .rtl-text {
        text-align: right !important;
    }

    .swal2-popup {
        font-family: 'Arial', sans-serif !important;
    }

    /* تحسين مظهر التنبيهات */
    .swal2-popup {
        font-size: 1.2em !important;
        border-radius: 10px !important;
    }

    .swal2-title {
        font-weight: bold !important;
    }

    .swal2-toast {
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
    }

    /* أنماط خاصة لكل نوع تنبيه */
    .swal2-success {
        background-color: #d4edda !important;
    }

    .swal2-warning {
        background-color: #fff3cd !important;
    }

    .swal2-error {
        background-color: #f8d7da !important;
    }

    .border-warning {
        border-color: #ffc107 !important;
        box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.25) !important;
    }

    .form-control:focus {
        border-color: #80bdff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    .available-path {
        background-color: #28a745 !important; /* اللون الأخضر */
        color: white !important;
        position: relative;
        border: 2px solid #1c7430; /* إطار داكن حول الزر */
        box-shadow: 0px 0px 10px rgba(40, 167, 69, 0.5); /* ظل أخضر لزيادة الوضوح */
        font-weight: bold; /* زيادة سمك النص */
        transition: all 0.1s ease; /* تأثير انتقالي لإبراز التفعيل */
    }

    .available-path:hover {
        background-color: #218838; /* لون أغمق عند المرور فوق الزر */
        border-color: #1e7e34; /* تغيير لون الإطار عند التمرير */
        box-shadow: 0px 0px 15px rgba(40, 167, 69, 0.75); /* زيادة الظل عند التمرير */
    }

    .available-path::after {
        content: '✓';
        position: absolute;
        right: 10px;
        top: 50%;
        transform: translateY(-50%);
        font-size: 1.5em;
    }

    .disabled-path {
        background-color: #6c757d !important;
        color: white !important;
        opacity: 0.65;
        cursor: not-allowed;
    }

    .alert {
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .alert ul {
        margin-right: 1.5rem;
    }

    .alert li {
        margin: 5px 0;
    }

    .alert-info {
        background-color: #e9f7fd;
        border-right: 4px solid #007bff; /* لون أزرق */
    }

    .alert-secondary {
        background-color: #f8f9fa;
        border-right: 4px solid #6c757d;
    }

    /* Restore section styling */
    .restore-section {
        border: 2px solid #17a2b8;
        border-radius: 10px;
        padding: 20px;
        background-color: #f8f9fa;
        margin-top: 20px;
    }

    .path-buttons {
        display: flex;
        gap: 10px;
        margin-bottom: 15px;
        flex-wrap: wrap;
    }

    .path-btn {
        background-color: #6c757d;
        color: white;
        padding: 8px 16px;
        border-radius: 4px;
        border: none;
        cursor: pointer;
        transition: background-color 0.3s ease;
    }

    .path-btn:hover {
        background-color: #5a6268;
    }

    .select-btn {
        background-color: #17a2b8;
        color: white;
    }

    .select-btn:hover {
        background-color: #138496;
    }

    .select-btn.unselect {
        background-color: #dc3545;
    }

    .select-btn.unselect:hover {
        background-color: #c82333;
    }

    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        flex-direction: column;
    }

    .loading-text {
        color: white;
        margin-top: 20px;
        font-size: 18px;
    }

    /* Responsive design */
    @media (max-width: 768px) {
        .supervisors-container {
            grid-template-columns: repeat(2, 1fr);
        }

        .path-buttons {
            flex-direction: column;
        }

        .action-buttons {
            flex-direction: column;
        }

        .details-table {
            font-size: 12px;
        }

        .details-table th,
        .details-table td {
            padding: 8px;
        }
    }

    @media (max-width: 480px) {
        .supervisors-container {
            grid-template-columns: 1fr;
        }

        .form-container {
            padding: 10px;
        }
    }
</style>

<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script type="text/javascript">
    // تأكيد التحويل العام
    function confirmSubmit() {
        return Swal.fire({
            title: 'تأكيد التحويل',
            text: 'هل أنت متأكد من تحويل الطلب؟',
            icon: 'question',
            showCancelButton: true,
            confirmButtonText: 'نعم، تحويل',
            cancelButtonText: 'إلغاء'
        }).then((result) => {
            if (result.isConfirmed) {
                showLoading(); // إظهار مؤشر التحميل
            }
            return result.isConfirmed;
        });
    }

    // تأكيد التوجيه التلقائي
    function confirmAutoPath() {
        return Swal.fire({
            title: 'تأكيد التوجيه التلقائي',
            text: 'هل أنت متأكد من تطبيق التوجيه التلقائي؟',
            icon: 'question',
            showCancelButton: true,
            confirmButtonText: 'نعم',
            cancelButtonText: 'لا'
        }).then((result) => {
            if (result.isConfirmed) {
                showLoading(); // إظهار مؤشر التحميل
            }
            return result.isConfirmed;
        });
    }

    // رسالة النجاح
    function showSuccess(message) {
        hideLoading(); // إخفاء مؤشر التحميل
        Swal.fire({
            title: 'تم بنجاح',
            text: message,
            icon: 'success',
            confirmButtonText: 'حسناً'
        });
    }

    // رسالة الخطأ
    function showError(message) {
        hideLoading(); // إخفاء مؤشر التحميل
        Swal.fire({
            title: 'خطأ',
            text: message,
            icon: 'error',
            confirmButtonText: 'حسناً'
        });
    }

    // إظهار مؤشر التحميل
    function showLoading() {
        document.getElementById('loadingIndicator').style.display = 'flex';
    }

    // إخفاء مؤشر التحميل
    function hideLoading() {
        document.getElementById('loadingIndicator').style.display = 'none';
    }

    // التحقق من اختيار طلب
    function validateSelection() {
        var ddl = document.getElementById('ddlOrderNumbers');
        if (ddl.value === '') {
            showError('الرجاء اختيار رقم الطلب أولاً');
            return false;
        }
        return true;
    }

    function confirmDirectToManager() {
        return Swal.fire({
            title: 'تأكيد التحويل المباشر',
            html: '<div class="text-right">' +
                '<strong class="text-danger">تنبيه هام جداً:</strong><br>' +
                'هذا الإجراء سيقوم بتحويل الطلب مباشرة إلى مدير الموارد البشرية،<br>' +
                'متجاوزاً جميع المشرفين.<br><br>' +
                'هل أنت متأكد من المتابعة؟</div>',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'نعم، متابعة',
            cancelButtonText: 'إلغاء',
            customClass: {
                title: 'text-right',
                htmlContainer: 'text-right',
                popup: 'swal2-rtl'
            }
        }).then((result) => {
            return result.isConfirmed;
        });
    }

    // Load order details when dropdown changes
    function loadOrderDetails(orderId) {
        if (!orderId || orderId === '') {
            document.getElementById('OrderDetailsPanel').style.display = 'none';
            document.getElementById('AutoPathInfoPanel').style.display = 'none';
            document.getElementById('RejectionAlertPanel').style.display = 'none';
            return;
        }

        fetch('@Url.Action("GetOrderDetails", "HRCoordinator")', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]').value
            },
            body: JSON.stringify({ orderId: parseInt(orderId) })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                populateOrderDetails(data.data);
                document.getElementById('OrderDetailsPanel').style.display = 'block';

                // Update auto-routing info
                if (data.data.autoRoutingInfo) {
                    updateAutoRoutingInfo(data.data.autoRoutingInfo);
                }

                // Show supervisor rejections if any
                if (data.data.supervisorRejections && data.data.supervisorRejections.length > 0) {
                    showSupervisorRejections(data.data.supervisorRejections);
                }
            } else {
                showError(data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showError('حدث خطأ أثناء جلب تفاصيل الطلب');
        });
    }

    // Populate order details in the UI
    function populateOrderDetails(data) {
        document.getElementById('lblOrderNumber').textContent = data.orderNumber || '';
        document.getElementById('lblOrderDate').textContent = data.orderDate || '';
        document.getElementById('lblOrderStatus').textContent = data.orderStatus || '';
        document.getElementById('lblOrderType').textContent = data.orderType || '';
        document.getElementById('lblEmployeeName').textContent = data.employeeName || '';
        document.getElementById('lblDepartment').textContent = data.department || '';
        document.getElementById('lblJobTitle').textContent = data.jobTitle || '';
        document.getElementById('lblEmploymentType').textContent = data.employmentType || '';
        document.getElementById('lblQualification').textContent = data.qualification || '';
        document.getElementById('lblEmployeeNumber').textContent = data.employeeNumber || '';
        document.getElementById('lblCivilRegistry').textContent = data.civilRegistry || '';
        document.getElementById('lblNationality').textContent = data.nationality || '';
        document.getElementById('lblMobileNumber').textContent = data.mobileNumber || '';
        document.getElementById('lblNotes').textContent = data.notes || '';
        document.getElementById('lblManagerApproval').textContent = data.managerApproval || '';
        document.getElementById('lblSupervisorApproval').textContent = data.supervisorApproval || '';
        document.getElementById('lblCoordinatorApproval').textContent = data.coordinatorApproval || '';
        document.getElementById('lblCancellationReason').textContent = data.cancellationReason || '';
        document.getElementById('lblCoordinatorDetails').textContent = data.coordinatorDetails || '';
        document.getElementById('lblHRManagerApproval').textContent = data.hrManagerApproval || '';

        // Supervisor permissions
        document.getElementById('lblMedicalServicesPermission').textContent = data.medicalServicesPermission || '/';
        document.getElementById('lblHRPlanningPermission').textContent = data.hrPlanningPermission || '/';
        document.getElementById('lblITPermission').textContent = data.itPermission || '/';
        document.getElementById('lblAttendanceControlPermission').textContent = data.attendanceControlPermission || '/';
        document.getElementById('lblMedicalRecordsPermission').textContent = data.medicalRecordsPermission || '/';
        document.getElementById('lblPayrollPermission').textContent = data.payrollPermission || '/';
        document.getElementById('lblLegalCompliancePermission').textContent = data.legalCompliancePermission || '/';
        document.getElementById('lblHRServicesPermission').textContent = data.hrServicesPermission || '/';
        document.getElementById('lblHousingPermission').textContent = data.housingPermission || '/';
        document.getElementById('lblFilesSectionPermission').textContent = data.filesSectionPermission || '/';
        document.getElementById('lblOutpatientPermission').textContent = data.outpatientPermission || '/';
        document.getElementById('lblSocialInsurancePermission').textContent = data.socialInsurancePermission || '/';
        document.getElementById('lblInventoryControlPermission').textContent = data.inventoryControlPermission || '/';
        document.getElementById('lblSelfResourcesPermission').textContent = data.selfResourcesPermission || '/';
        document.getElementById('lblNursingPermission').textContent = data.nursingPermission || '/';
        document.getElementById('lblEmployeeServicesPermission').textContent = data.employeeServicesPermission || '/';
    }

    // Update auto-routing information
    function updateAutoRoutingInfo(autoRoutingInfo) {
        const autoPathBtn = document.getElementById('btnAutoPath');
        const autoPathPanel = document.getElementById('AutoPathInfoPanel');

        if (autoRoutingInfo.isAvailable) {
            autoPathBtn.className = 'btn auto-path-btn available-path';
            autoPathBtn.disabled = false;
        } else {
            autoPathBtn.className = 'btn auto-path-btn disabled-path';
            autoPathBtn.disabled = true;
        }

        autoPathPanel.innerHTML = autoRoutingInfo.message;
        autoPathPanel.style.display = 'block';
    }

    // Show supervisor rejections
    function showSupervisorRejections(rejections) {
        let html = '<div class="alert alert-danger text-right" role="alert">';
        html += '<h5 class="alert-heading">⚠️ تحذير شديد!</h5><hr>';
        html += '<strong>هذا الطلب تمت إعادته من قبل المشرفين:</strong>';
        html += '<ul class="mt-2 list-unstyled">';

        rejections.forEach(rejection => {
            html += `<li class="text-danger mb-2"><i class="fas fa-exclamation-triangle"></i> ${rejection.rejectionDetails}</li>`;
        });

        html += '</ul><hr>';
        html += '<small class="text-danger">يرجى مراجعة سبب الإعادة قبل تحويل الطلب للمدير</small>';
        html += '</div>';

        document.getElementById('RejectionAlertPanel').innerHTML = html;
        document.getElementById('RejectionAlertPanel').style.display = 'block';
    }

    // Select all supervisors
    function selectAllSupervisors() {
        if (!validateSelection()) return;

        const checkboxes = document.querySelectorAll('input[name="SelectedSupervisors"]');
        checkboxes.forEach(checkbox => {
            checkbox.checked = true;
        });
    }

    // Unselect all supervisors
    function unselectAllSupervisors() {
        if (!validateSelection()) return;

        const checkboxes = document.querySelectorAll('input[name="SelectedSupervisors"]');
        checkboxes.forEach(checkbox => {
            checkbox.checked = false;
        });
    }

    // Submit order to selected supervisors
    function submitOrder() {
        if (!validateSelection()) return;

        const selectedSupervisors = getSelectedSupervisors();
        if (selectedSupervisors.length === 0) {
            showError('يجب اختيار قسم على الأقل للاعتماد');
            return;
        }

        const details = document.querySelector('textarea[name="Details"]').value.trim();
        if (!details) {
            showError('يرجى كتابة التفاصيل/الرقم');
            return;
        }

        if (!confirmSubmit()) return;

        const formData = new FormData();
        formData.append('orderId', document.getElementById('ddlOrderNumbers').value);
        formData.append('details', details);
        selectedSupervisors.forEach(supervisor => {
            formData.append('selectedSupervisors', supervisor);
        });
        formData.append('__RequestVerificationToken', document.querySelector('input[name="__RequestVerificationToken"]').value);

        fetch('@Url.Action("SubmitOrder", "HRCoordinator")', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            if (response.ok) {
                window.location.reload();
            } else {
                showError('حدث خطأ أثناء تحويل الطلب');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showError('حدث خطأ أثناء تحويل الطلب');
        });
    }

    // Get selected supervisors
    function getSelectedSupervisors() {
        const checkboxes = document.querySelectorAll('input[name="SelectedSupervisors"]:checked');
        return Array.from(checkboxes).map(checkbox => checkbox.value);
    }

    // Auto route order
    function autoRouteOrder() {
        if (!validateSelection()) return;
        if (!confirmAutoPath()) return;

        const formData = new FormData();
        formData.append('orderId', document.getElementById('ddlOrderNumbers').value);
        formData.append('__RequestVerificationToken', document.querySelector('input[name="__RequestVerificationToken"]').value);

        fetch('@Url.Action("AutoRouteOrder", "HRCoordinator")', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            if (response.ok) {
                window.location.reload();
            } else {
                showError('حدث خطأ أثناء التوجيه التلقائي');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showError('حدث خطأ أثناء التوجيه التلقائي');
        });
    }

    // Apply predefined path
    function applyPath(pathNumber) {
        if (!validateSelection()) return;

        const formData = new FormData();
        formData.append('orderId', document.getElementById('ddlOrderNumbers').value);
        formData.append('pathNumber', pathNumber);
        formData.append('__RequestVerificationToken', document.querySelector('input[name="__RequestVerificationToken"]').value);

        fetch('@Url.Action("ApplyPath", "HRCoordinator")', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            if (response.ok) {
                window.location.reload();
            } else {
                showError(`حدث خطأ أثناء تطبيق المسار ${pathNumber}`);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showError(`حدث خطأ أثناء تطبيق المسار ${pathNumber}`);
        });
    }

    // Direct to manager
    function directToManager() {
        if (!validateSelection()) return;
        if (!confirmDirectToManager()) return;

        const details = document.querySelector('textarea[name="Details"]').value.trim();

        const formData = new FormData();
        formData.append('orderId', document.getElementById('ddlOrderNumbers').value);
        formData.append('details', details);
        formData.append('__RequestVerificationToken', document.querySelector('input[name="__RequestVerificationToken"]').value);

        fetch('@Url.Action("DirectToManager", "HRCoordinator")', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            if (response.ok) {
                window.location.reload();
            } else {
                showError('حدث خطأ أثناء تحويل الطلب للمدير');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showError('حدث خطأ أثناء تحويل الطلب للمدير');
        });
    }

    // Mark order as needs action
    function markNeedsAction() {
        if (!validateSelection()) return;

        const actionRequired = document.querySelector('input[name="ActionRequired"]').value.trim();
        if (!actionRequired) {
            showError('يرجى إدخال الإجراءات المطلوبة');
            return;
        }

        const formData = new FormData();
        formData.append('orderId', document.getElementById('ddlOrderNumbers').value);
        formData.append('actionRequired', actionRequired);
        formData.append('__RequestVerificationToken', document.querySelector('input[name="__RequestVerificationToken"]').value);

        fetch('@Url.Action("MarkOrderNeedsAction", "HRCoordinator")', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            if (response.ok) {
                window.location.reload();
            } else {
                showError('حدث خطأ أثناء حفظ الإجراءات');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showError('حدث خطأ أثناء حفظ الإجراءات');
        });
    }

    // Return order
    function returnOrder() {
        if (!validateSelection()) return;

        const returnReason = document.querySelector('textarea[name="ReturnReason"]').value.trim();
        if (!returnReason) {
            showError('يرجى إدخال سبب الإعادة');
            return;
        }

        const formData = new FormData();
        formData.append('orderId', document.getElementById('ddlOrderNumbers').value);
        formData.append('returnReason', returnReason);
        formData.append('__RequestVerificationToken', document.querySelector('input[name="__RequestVerificationToken"]').value);

        fetch('@Url.Action("ReturnOrder", "HRCoordinator")', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            if (response.ok) {
                window.location.reload();
            } else {
                showError('حدث خطأ أثناء إعادة الطلب');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showError('حدث خطأ أثناء إعادة الطلب');
        });
    }

    // Reject order
    function rejectOrder() {
        if (!validateSelection()) return;

        const rejectReason = document.querySelector('textarea[name="ReturnReason"]').value.trim();
        if (!rejectReason) {
            showError('يرجى إدخال سبب الإلغاء');
            return;
        }

        const formData = new FormData();
        formData.append('orderId', document.getElementById('ddlOrderNumbers').value);
        formData.append('rejectReason', rejectReason);
        formData.append('__RequestVerificationToken', document.querySelector('input[name="__RequestVerificationToken"]').value);

        fetch('@Url.Action("RejectOrder", "HRCoordinator")', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            if (response.ok) {
                window.location.reload();
            } else {
                showError('حدث خطأ أثناء إلغاء الطلب');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showError('حدث خطأ أثناء إلغاء الطلب');
        });
    }

    // Download attachments
    function downloadAttachments() {
        if (!validateSelection()) return;

        const orderId = document.getElementById('ddlOrderNumbers').value;
        window.open('@Url.Action("DownloadAttachments", "HRCoordinator")?orderId=' + orderId, '_blank');
    }

    // Toggle restore section
    function toggleRestoreSection() {
        const restoreSection = document.getElementById('RestoreSection');
        const toggleBtn = document.getElementById('toggleRestoreBtn');

        if (restoreSection.style.display === 'none' || restoreSection.style.display === '') {
            restoreSection.style.display = 'block';
            toggleBtn.textContent = 'إخفاء قسم الاستعادة';
            loadRestorableOrders();
        } else {
            restoreSection.style.display = 'none';
            toggleBtn.textContent = 'إظهار قسم الاستعادة';
        }
    }

    // Load restorable orders
    function loadRestorableOrders(searchTerm = '', filter = 'today') {
        fetch('@Url.Action("GetRestorableOrders", "HRCoordinator")', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]').value
            },
            body: JSON.stringify({ searchTerm: searchTerm, filter: filter })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const dropdown = document.getElementById('ddlRestorableOrders');
                dropdown.innerHTML = '<option value="">-- اختر الطلب للاستعادة --</option>';

                data.data.forEach(order => {
                    const option = document.createElement('option');
                    option.value = order.value;
                    option.textContent = order.text;
                    dropdown.appendChild(option);
                });
            } else {
                showError(data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showError('حدث خطأ أثناء جلب الطلبات');
        });
    }

    // Load restore order details
    function loadRestoreOrderDetails(orderId) {
        if (!orderId || orderId === '') {
            document.getElementById('RestoreDetailsPanel').style.display = 'none';
            return;
        }

        fetch('@Url.Action("GetRestoreOrderDetails", "HRCoordinator")', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]').value
            },
            body: JSON.stringify({ orderId: parseInt(orderId) })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('lblRestoreCurrentStatus').textContent = data.data.currentStatus || '';
                document.getElementById('lblRestoreTransferDate').textContent = data.data.transferDate || '';
                document.getElementById('lblRestoreAssignedSupervisors').textContent = data.data.assignedSupervisors || '';
                document.getElementById('RestoreDetailsPanel').style.display = 'block';
            } else {
                showError(data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showError('حدث خطأ أثناء جلب تفاصيل الطلب');
        });
    }

    // Restore order
    function restoreOrder() {
        const orderId = document.getElementById('ddlRestorableOrders').value;
        if (!orderId) {
            showError('يرجى اختيار طلب للاستعادة');
            return;
        }

        const restoreNotes = document.querySelector('textarea[name="RestoreNotes"]').value.trim();

        const formData = new FormData();
        formData.append('orderId', orderId);
        formData.append('restoreNotes', restoreNotes);
        formData.append('__RequestVerificationToken', document.querySelector('input[name="__RequestVerificationToken"]').value);

        fetch('@Url.Action("RestoreOrder", "HRCoordinator")', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            if (response.ok) {
                window.location.reload();
            } else {
                showError('حدث خطأ أثناء استعادة الطلب');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showError('حدث خطأ أثناء استعادة الطلب');
        });
    }

    // Search restorable orders
    function searchRestorableOrders() {
        const searchTerm = document.getElementById('txtRestoreSearch').value.trim();
        const filter = document.getElementById('ddlRestoreFilter').value;
        loadRestorableOrders(searchTerm, filter);
    }

    // Initialize page
    document.addEventListener('DOMContentLoaded', function() {
        // Add enter key support for search
        const searchInput = document.getElementById('txtRestoreSearch');
        if (searchInput) {
            searchInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    searchRestorableOrders();
                }
            });
        }

        // Auto-hide alerts after 5 seconds
        const alerts = document.querySelectorAll('.alert');
        alerts.forEach(alert => {
            if (alert.classList.contains('alert-success')) {
                setTimeout(() => {
                    alert.style.display = 'none';
                }, 5000);
            }
        });

        // Add confirmation to dangerous actions
        const dangerousButtons = document.querySelectorAll('.reject-button, .return-button');
        dangerousButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                if (!confirm('هل أنت متأكد من هذا الإجراء؟')) {
                    e.preventDefault();
                    return false;
                }
            });
        });
    });

    // Utility function to format dates
    function formatDate(dateString) {
        if (!dateString) return '';
        const date = new Date(dateString);
        return date.toLocaleDateString('ar-SA');
    }

    // Utility function to validate form inputs
    function validateForm() {
        const requiredFields = document.querySelectorAll('[required]');
        let isValid = true;

        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                field.classList.add('border-warning');
                isValid = false;
            } else {
                field.classList.remove('border-warning');
            }
        });

        return isValid;
    }

    // Clear form function
    function clearForm() {
        document.querySelectorAll('input[type="text"], textarea').forEach(input => {
            input.value = '';
        });

        document.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
            checkbox.checked = false;
        });

        document.querySelectorAll('select').forEach(select => {
            select.selectedIndex = 0;
        });

        // Hide panels
        document.getElementById('OrderDetailsPanel').style.display = 'none';
        document.getElementById('AutoPathInfoPanel').style.display = 'none';
        document.getElementById('RejectionAlertPanel').style.display = 'none';
        document.getElementById('RestoreDetailsPanel').style.display = 'none';
    }
</script>

<div class="form-container">
    <h2 class="form-header">
        تحويل لاعتماد:
        @if (TempData["SuccessMessage"] != null)
        {
            <div class="alert alert-success">@TempData["SuccessMessage"]</div>
        }
        @if (TempData["ErrorMessage"] != null)
        {
            <div class="alert alert-danger">@TempData["ErrorMessage"]</div>
        }
    </h2>

    <!-- القائمة المنسدلة -->
    <div class="dropdown-container">
        @Html.DropDownListFor(m => m.SelectedOrderId, Model.OrderNumbers, "-- اختر رقم الطلب --", 
            new { @class = "custom-dropdown", @id = "ddlOrderNumbers", onchange = "loadOrderDetails(this.value)" })
    </div>

    <!-- الأزرار -->
    <div class="buttons-container">
        <!-- الصف الأول - أزرار المسار -->
        <div class="path-buttons">
            <button type="button" class="btn path-btn" onclick="applyPath(1)">مسار1</button>
            <button type="button" class="btn path-btn" onclick="applyPath(2)">مسار2</button>
            <button type="button" class="btn path-btn" onclick="applyPath(3)">مسار3</button>
            <button type="button" class="btn path-btn" onclick="applyPath(4)">مسار4</button>
            <button type="button" class="btn path-btn" onclick="applyPath(5)">مسار5</button>
            <button type="button" class="btn path-btn" onclick="applyPath(6)">مسار6</button>
        </div>

        <!-- الصف الثاني - أزرار التحديد والتوجيه -->
        <div class="action-buttons">
            <button type="button" id="btnAutoPath" class="btn auto-path-btn disabled-path" onclick="autoRouteOrder()" disabled>التوجيه التلقائي</button>
            <button type="button" class="btn select-btn" onclick="selectAllSupervisors()">تحديد الكل</button>
            <button type="button" class="btn select-btn unselect" onclick="unselectAllSupervisors()">إلغاء الكل</button>
            <button type="button" class="btn btn-danger" onclick="directToManager()">تحويل للمدير</button>
        </div>
    </div>

    <!-- مؤشر التحميل -->
    <div id="loadingIndicator" class="loading-overlay" style="display: none;">
        <!-- <div class="spinner"></div>
        <div class="loading-text">جاري التحميل...</div> -->
    </div>
</div>

<!-- لوحة معلومات المسار التلقائي -->
<div id="AutoPathInfoPanel" style="display: none;" class="mb-4">
    <!-- سيتم تعبئة المحتوى ديناميكيًا -->
</div>

<div id="RejectionAlertPanel" style="display: none;" class="mb-3"></div>

<div id="checkboxContainer" class="checkbox-section">
    <table class="checkbox-table">
        <!-- صف 1: المساعد الطبي والإدارات المرتبطة -->
        <tr class="header-row">
            <td class="department-cell">
                <div class="checkbox-container">
                    <input type="checkbox" id="chk1" name="SelectedSupervisors" value="خدمات الموظفين" />
                    <label for="chk1">خدمات الموظفين</label>
                </div>
            </td>
            <td class="department-cell">
                <div class="checkbox-container">
                    <input type="checkbox" id="chk7" name="SelectedSupervisors" value="إدارة القانونية والالتزام" />
                    <label for="chk7">إدارة القانونية والالتزام</label>
                </div>
            </td>
            <td class="department-cell">
                <div class="checkbox-container">
                    <input type="checkbox" id="chk11" name="SelectedSupervisors" value="العيادات الخارجية" />
                    <label for="chk11">العيادات الخارجية</label>
                </div>
            </td>
            <td class="department-cell">
                <div class="checkbox-container">
                    <input type="checkbox" id="chk15" name="SelectedSupervisors" value="إدارة الأمن و السلامة" />
                    <label for="chk15">إدارة الأمن و السلامة</label>
                </div>
            </td>
        </tr>

        <!-- صف 2: إدارة الموارد البشرية والإدارات المرتبطة -->
        <tr>
            <td class="department-cell">
                <div class="checkbox-container">
                    <input type="checkbox" id="chk2" name="SelectedSupervisors" value="إدارة تخطيط الموارد البشرية" />
                    <label for="chk2">إدارة تخطيط الموارد البشرية</label>
                </div>
            </td>
            <td class="department-cell">
                <div class="checkbox-container">
                    <input type="checkbox" id="chk6" name="SelectedSupervisors" value="إدارة الرواتب والاستحقاقات" />
                    <label for="chk6">إدارة الرواتب والاستحقاقات</label>
                </div>
            </td>
            <td class="department-cell">
                <div class="checkbox-container">
                    <input type="checkbox" id="chk10" name="SelectedSupervisors" value="قسم الملفات" />
                    <label for="chk10">قسم الملفات</label>
                </div>
            </td>
            <td class="department-cell">
                <div class="checkbox-container">
                    <input type="checkbox" id="chk14" name="SelectedSupervisors" value="إدارة تنمية الإيرادات" />
                    <label for="chk14">إدارة تنمية الإيرادات</label>
                </div>
            </td>
        </tr>

        <!-- صف 3: إدارة المعلومات والمخزون -->
        <tr>
            <td class="department-cell">
                <div class="checkbox-container">
                    <input type="checkbox" id="chk3" name="SelectedSupervisors" value="إدارة تقنية المعلومات" />
                    <label for="chk3">إدارة تقنية المعلومات</label>
                </div>
            </td>
            <td class="department-cell">
                <div class="checkbox-container">
                    <input type="checkbox" id="chk5" name="SelectedSupervisors" value="السجلات الطبية" />
                    <label for="chk5">السجلات الطبية</label>
                </div>
            </td>
            <td class="department-cell">
                <div class="checkbox-container">
                    <input type="checkbox" id="chk9" name="SelectedSupervisors" value="إدارة الإسكان" />
                    <label for="chk9">إدارة الإسكان</label>
                </div>
            </td>
            <td class="department-cell">
                <div class="checkbox-container">
                    <input type="checkbox" id="chk13" name="SelectedSupervisors" value="وحدة مراقبة المخزون" />
                    <label for="chk13">وحدة مراقبة المخزون</label>
                </div>
            </td>
        </tr>

        <!-- صف 4: الخدمات المساندة -->
        <tr>
            <td class="department-cell">
                <div class="checkbox-container">
                    <input type="checkbox" id="chk4" name="SelectedSupervisors" value="مراقبة الدوام" />
                    <label for="chk4">مراقبة الدوام</label>
                </div>
            </td>
            <td class="department-cell">
                <div class="checkbox-container">
                    <input type="checkbox" id="chk8" name="SelectedSupervisors" value="خدمات الموارد البشرية" />
                    <label for="chk8">خدمات الموارد البشرية</label>
                </div>
            </td>
            <td class="department-cell">
                <div class="checkbox-container">
                    <input type="checkbox" id="chk12" name="SelectedSupervisors" value="التأمينات الاجتماعية" />
                    <label for="chk12">التأمينات الاجتماعية</label>
                </div>
            </td>
            <td class="department-cell">
                <div class="checkbox-container">
                    <input type="checkbox" id="chk16" name="SelectedSupervisors" value="الطب الاتصالي" />
                    <label for="chk16">الطب الاتصالي</label>
                </div>
            </td>
        </tr>
    </table>
</div>

<div id="txtContainer" class="txt-container">
    @using (Html.BeginForm())
    {
        @Html.AntiForgeryToken()
        @Html.HiddenFor(m => m.SelectedOrderId)

        @Html.TextAreaFor(m => m.Details, new { @class = "textbox1", placeholder = "التفاصيل والرقم", rows = "3" })
        @Html.TextBoxFor(m => m.ActionRequired, new { @class = "textbox1", placeholder = "الإجراءات المطلوبة" })
        @Html.TextAreaFor(m => m.ReturnReason, new { @class = "textbox1", placeholder = "سبب الإلغاء/الإعادة", rows = "2" })

        <br />
        <br />

        <button type="button" class="submit-button" onclick="submitOrder()">تحويل الطلب</button>
        <button type="button" class="action-button" onclick="markNeedsAction()">يتطلب إجراءات</button>
        <button type="button" class="return-button" onclick="returnOrder()">إعادة الطلب</button>
        <button type="button" class="reject-button" onclick="rejectOrder()">إلغاء الطلب</button>
    }
</div>

<br />
<br />

<!-- Order Details Panel -->
<div id="OrderDetailsPanel" style="display: none;">
    <div class="full-table-container">
        <!-- Table 1 -->
        <table class="details-table">
            <tr>
                <th>رقم الطلب</th>
                <td id="lblOrderNumber"></td>
                <th>تاريخ الطلب</th>
                <td id="lblOrderDate"></td>
            </tr>
            <tr>
                <th>حالة الطلب</th>
                <td colspan="3" id="lblOrderStatus"></td>
            </tr>
        </table>

        <!-- Table 2 -->
        <table class="details-table">
            <tr>
                <th>نوع الطلب</th>
                <td colspan="3" id="lblOrderType"></td>
            </tr>
            <tr>
                <th>اسم الموظف</th>
                <td id="lblEmployeeName"></td>
                <th>القسم</th>
                <td id="lblDepartment"></td>
            </tr>
            <tr>
                <th>الوظيفة</th>
                <td id="lblJobTitle"></td>
                <th>نوع التوظيف</th>
                <td id="lblEmploymentType"></td>
            </tr>
            <tr>
                <th>المؤهل</th>
                <td id="lblQualification"></td>
                <th>رقم الموظف</th>
                <td id="lblEmployeeNumber"></td>
            </tr>
            <tr>
                <th>السجل المدني</th>
                <td id="lblCivilRegistry"></td>
                <th>الجنسية</th>
                <td id="lblNationality"></td>
            </tr>
            <tr>
                <th>رقم الجوال</th>
                <td id="lblMobileNumber"></td>
                <th colspan="2"></th>
            </tr>
            <tr>
                <td colspan="4">
                    <strong>تفاصيل مقدم الطلب:</strong><br />
                    <span id="lblNotes"></span>
                </td>
            </tr>
        </table>

        <!-- Table 3 -->
        <table class="details-table">
            <tr>
                <th>تم التأكيد/الإلغاء من مدير القسم</th>
                <th>تم التأكيد/الإلغاء من قبل مساعد المدير</th>
                <th>تم التحويل/الإلغاء من قبل المنسق</th>
            </tr>
            <tr>
                <td id="lblManagerApproval"></td>
                <td id="lblSupervisorApproval"></td>
                <td id="lblCoordinatorApproval"></td>
            </tr>
            <tr>
                <th>سبب الإلغاء/الإعادة</th>
                <th colspan="2">تفاصيل المنسق</th>
            </tr>
            <tr>
                <td id="lblCancellationReason"></td>
                <td colspan="2" id="lblCoordinatorDetails"></td>
            </tr>
        </table>

        <button type="button" class="submit-button" onclick="downloadAttachments()">تحميل مرفقات الطلب</button>

        <!-- Table 4 - Supervisor Permissions -->
        <table class="details-table">
            <tr>
                <th>مشرف خدمات الموظفين</th>
                <td id="lblMedicalServicesPermission"></td>
                <th>مشرف إدارة تخطيط الموارد البشرية</th>
                <td id="lblHRPlanningPermission"></td>
            </tr>
            <tr>
                <th>مشرف إدارة تقنية المعلومات</th>
                <td id="lblITPermission"></td>
                <th>مشرف مراقبة الدوام</th>
                <td id="lblAttendanceControlPermission"></td>
            </tr>
            <tr>
                <th>مشرف السجلات الطبية</th>
                <td id="lblMedicalRecordsPermission"></td>
                <th>مشرف إدارة الرواتب والاستحقاقات</th>
                <td id="lblPayrollPermission"></td>
            </tr>
            <tr>
                <th>مشرف إدارة القانونية والالتزام</th>
                <td id="lblLegalCompliancePermission"></td>
                <th>مشرف خدمات الموارد البشرية</th>
                <td id="lblHRServicesPermission"></td>
            </tr>
            <tr>
                <th>مشرف إدارة الإسكان</th>
                <td id="lblHousingPermission"></td>
                <th>مشرف قسم الملفات</th>
                <td id="lblFilesSectionPermission"></td>
            </tr>
            <tr>
                <th>مشرف العيادات الخارجية</th>
                <td id="lblOutpatientPermission"></td>
                <th>مشرف التأمينات الاجتماعية</th>
                <td id="lblSocialInsurancePermission"></td>
            </tr>
            <tr>
                <th>مشرف وحدة مراقبة المخزون</th>
                <td id="lblInventoryControlPermission"></td>
                <th>مشرف إدارة تنمية الإيرادات</th>
                <td id="lblSelfResourcesPermission"></td>
            </tr>
            <tr>
                <th>مشرف إدارة الأمن و السلامة</th>
                <td id="lblNursingPermission"></td>
                <th>مشرف الطب الاتصالي</th>
                <td id="lblEmployeeServicesPermission"></td>
            </tr>
        </table>

        <!-- Table 5 -->
        <table class="details-table">
            <tr>
                <th>مدير الموارد البشرية</th>
                <td colspan="3" id="lblHRManagerApproval"></td>
            </tr>
        </table>
    </div>
</div>

<!-- Restore Section -->
<div class="form-container" style="margin-top: 30px;">
    <button type="button" id="toggleRestoreBtn" class="btn btnok" onclick="toggleRestoreSection()">إظهار قسم الاستعادة</button>

    <div id="RestoreSection" style="display: none; margin-top: 20px;">
        <h3 class="form-header">استعادة الطلبات من المشرفين</h3>

        <!-- Search and Filter -->
        <div class="row mb-3">
            <div class="col-md-6">
                <input type="text" id="txtRestoreSearch" class="form-control" placeholder="البحث برقم الطلب أو اسم الموظف" />
            </div>
            <div class="col-md-3">
                <select id="ddlRestoreFilter" class="form-control" onchange="searchRestorableOrders()">
                    <option value="today">اليوم</option>
                    <option value="week">هذا الأسبوع</option>
                    <option value="month">هذا الشهر</option>
                    <option value="all">جميع الطلبات</option>
                </select>
            </div>
            <div class="col-md-3">
                <button type="button" class="btn btnok" onclick="searchRestorableOrders()">بحث</button>
            </div>
        </div>

        <!-- Restorable Orders Dropdown -->
        <div class="mb-3">
            <select id="ddlRestorableOrders" class="custom-dropdown" onchange="loadRestoreOrderDetails(this.value)">
                <option value="">-- اختر الطلب للاستعادة --</option>
                @foreach (var order in Model.RestorableOrders)
                {
                    <option value="@order.Value">@order.Text</option>
                }
            </select>
        </div>

        <!-- Restore Details Panel -->
        <div id="RestoreDetailsPanel" style="display: none;">
            <table class="details-table">
                <tr>
                    <th>الحالة الحالية</th>
                    <td id="lblRestoreCurrentStatus"></td>
                    <th>تاريخ التحويل</th>
                    <td id="lblRestoreTransferDate"></td>
                </tr>
                <tr>
                    <th>المشرفين المكلفين</th>
                    <td colspan="3" id="lblRestoreAssignedSupervisors"></td>
                </tr>
            </table>
        </div>

        <!-- Restore Form -->
        @using (Html.BeginForm("RestoreOrder", "HRCoordinator", FormMethod.Post))
        {
            @Html.AntiForgeryToken()

            <div class="mb-3">
                @Html.TextAreaFor(m => m.RestoreNotes, new { @class = "textbox1", placeholder = "ملاحظات الاستعادة (اختياري)", rows = "3" })
            </div>

            <button type="button" class="submit-button" onclick="restoreOrder()">استعادة الطلب</button>
        }
    </div>
</div>
