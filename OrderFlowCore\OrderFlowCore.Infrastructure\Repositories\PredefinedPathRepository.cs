using OrderFlowCore.Application.Interfaces.Repositories;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace OrderFlowCore.Infrastructure.Repositories
{
    public class PredefinedPathRepository : IPredefinedPathRepository
    {
        // Predefined paths based on the original B.aspx functionality
        private readonly Dictionary<int, List<string>> _predefinedPaths = new Dictionary<int, List<string>>
        {
            {
                1, new List<string>
                {
                    "خدمات الموظفين",
                    "إدارة تخطيط الموارد البشرية",
                    "إدارة تقنية المعلومات"
                }
            },
            {
                2, new List<string>
                {
                    "مراقبة الدوام",
                    "السجلات الطبية",
                    "إدارة الرواتب والاستحقاقات"
                }
            },
            {
                3, new List<string>
                {
                    "إدارة القانونية والالتزام",
                    "خدمات الموارد البشرية",
                    "إدارة الإسكان"
                }
            },
            {
                4, new List<string>
                {
                    "قسم الملفات",
                    "العيادات الخارجية",
                    "التأمينات الاجتماعية"
                }
            },
            {
                5, new List<string>
                {
                    "وحدة مراقبة المخزون",
                    "إدارة تنمية الإيرادات",
                    "إدارة الأمن و السلامة"
                }
            },
            {
                6, new List<string>
                {
                    "الطب الاتصالي",
                    "خدمات الموظفين",
                    "إدارة تخطيط الموارد البشرية"
                }
            }
        };

        public async Task<List<string>> GetPathSupervisorsAsync(int pathNumber)
        {
            await Task.CompletedTask; // Make it async for consistency
            
            if (_predefinedPaths.ContainsKey(pathNumber))
            {
                return _predefinedPaths[pathNumber].ToList();
            }
            
            return new List<string>();
        }

        public async Task<bool> PathExistsAsync(int pathNumber)
        {
            await Task.CompletedTask; // Make it async for consistency
            return _predefinedPaths.ContainsKey(pathNumber);
        }
    }
}
