using System.Collections.Generic;
using System.Threading.Tasks;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Application.Common;

namespace OrderFlowCore.Application.Interfaces.Services;

public interface IJobTypeService
{
    Task<ServiceResult<IEnumerable<JobTypeDto>>> GetAllAsync();
    Task<ServiceResult<JobTypeDto>> GetByIdAsync(int id);
    Task<ServiceResult> CreateAsync(JobTypeDto dto);
    Task<ServiceResult> UpdateAsync(JobTypeDto dto);
    Task<ServiceResult> DeleteAsync(int id);
}
