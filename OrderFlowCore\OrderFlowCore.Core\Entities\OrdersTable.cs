﻿#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using OrderFlowCore.Core.Entities;

namespace OrderFlowCore.Core.Models;

[Table("ordersTable")]
public partial class OrdersTable
{
    [Key]
    public int Id { get; set; }
    
    public DateTime CreatedAt { get; set; }
    
    public string OrderType { get; set; }
    
    [StringLength(100)]
    public string EmployeeName { get; set; }

    [StringLength(100)]
    public string JobTitle { get; set; }
    
    [StringLength(100)]
    public string EmployeeNumber { get; set; }
    
    [StringLength(100)]
    public string CivilRecord { get; set; }

    [StringLength(100)]
    public string Nationality { get; set; }
    
    [StringLength(100)]
    public string MobileNumber { get; set; }
    
    [StringLength(100)]
    public string EmploymentType { get; set; }
    
    [StringLength(100)]
    public string Qualification { get; set; }
    
    [StringLength(255)]
    public string Department { get; set; }
    
    public string Details { get; set; }
    
    public OrderStatus OrderStatus { get; set; }
    
    [StringLength(255)]
    public string ConfirmedByDepartmentManager { get; set; }
    
    [StringLength(255)]
    public string ConfirmedByAssistantManager { get; set; }
    
    [StringLength(255)]
    public string ConfirmedByCoordinator { get; set; }
    
    [StringLength(255)]
    public string ReasonForCancellation { get; set; }
    
    public string CoordinatorDetails { get; set; }
    
    [StringLength(255)]
    public string SupervisorOfEmployeeServices { get; set; }
    
    [StringLength(255)]
    public string SupervisorOfHumanResourcesPlanning { get; set; }
    
    [StringLength(255)]
    public string SupervisorOfInformationTechnology { get; set; }
    
    [StringLength(255)]
    public string SupervisorOfAttendance { get; set; }
    
    [StringLength(255)]
    public string SupervisorOfMedicalRecords { get; set; }
    
    [StringLength(255)]
    public string SupervisorOfPayrollAndBenefits { get; set; }
    
    [StringLength(255)]
    public string SupervisorOfLegalAndCompliance { get; set; }
    
    [StringLength(255)]
    public string SupervisorOfHumanResourcesServices { get; set; }
    
    [StringLength(255)]
    public string SupervisorOfHousing { get; set; }
    
    [StringLength(255)]
    public string SupervisorOfFiles { get; set; }
    
    [StringLength(255)]
    public string SupervisorOfOutpatientClinics { get; set; }
    
    [StringLength(255)]
    public string SupervisorOfSocialSecurity { get; set; }
    
    [StringLength(255)]
    public string SupervisorOfInventoryControl { get; set; }
    
    [StringLength(255)]
    public string SupervisorOfRevenueDevelopment { get; set; }
    
    [StringLength(255)]
    public string SupervisorOfSecurity { get; set; }
    
    [StringLength(255)]
    public string SupervisorOfMedicalConsultation { get; set; }
    
    [StringLength(255)]
    public string HumanResourcesManager { get; set; }
    
    [StringLength(255)]
    public string TransferType { get; set; }
    
    public string SupervisorNotes { get; set; }

    public string File1Url { get; set; }

    public string File2Url { get; set; }

    public string File3Url { get; set; }

    public string File4Url { get; set; }
}