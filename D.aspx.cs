﻿using System;
using System.Configuration;
using System.Data.SqlClient;
using System.IO.Compression;
using System.IO;
using System.Web.UI.WebControls;
using abozyad.Helpers;
using System.Web.Configuration;
using System.Collections.Generic;

namespace abozyad
{
    public partial class WebForm9 : System.Web.UI.Page
    {
        private string connectionString = ConfigurationManager.ConnectionStrings["MyConnectionString"].ConnectionString;
        protected void Page_Load(object sender, EventArgs e)
        {

            Session["UserPermission"] = "مدير الموارد البشرية";

            if (Session["UserPermission"] == null || Session["UserPermission"].ToString() != "مدير الموارد البشرية")
            {
                // Redirect to an access denied page or display an error message
                Response.Redirect("AccessDenied.aspx");
            }



            if (!IsPostBack)
            {
                InitializeStatusDropDown();
                PopulateOrderNumbers(); 
                PopulateStatusChangeList();
                bool autoDelete = bool.Parse(ConfigurationManager.AppSettings["AutoDeleteAttachments"]);
                chkAutoDeleteAttachments.Checked = autoDelete;
                UpdateAutoDeleteStatus(autoDelete);



            }


        }
        private string CleanFileName(string fileName)
        {
            // إزالة الأحرف غير المسموح بها في أسماء الملفات
            char[] invalidChars = System.IO.Path.GetInvalidFileNameChars();
            foreach (char c in invalidChars)
            {
                fileName = fileName.Replace(c, '_');
            }

            // التأكد من أن الاسم لا يتجاوز طولاً معيناً
            const int maxLength = 100;
            if (fileName.Length > maxLength)
            {
                string extension = System.IO.Path.GetExtension(fileName);
                fileName = fileName.Substring(0, maxLength - extension.Length) + extension;
            }

            return fileName;
        }

        protected void btnDownload_Click(object sender, EventArgs e)
        {
            try
            {
                string selectedOrderNumber = ddlOrderNumbers.SelectedValue;
                string connectionString = ConfigurationManager.ConnectionStrings["MyConnectionString"].ConnectionString;

                using (SqlConnection con = new SqlConnection(connectionString))
                {
                    string query = @"SELECT ملف1, ملف2, ملف3, ملف4, [اسم الموظف] 
                           FROM ordersTable 
                           WHERE [رقم الطلب] = @OrderNumber";

                    using (SqlCommand cmd = new SqlCommand(query, con))
                    {
                        cmd.Parameters.AddWithValue("@OrderNumber", selectedOrderNumber);
                        con.Open();

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                string employeeName = reader["اسم الموظف"].ToString().Trim();
                                bool filesFound = false;

                                using (MemoryStream zipStream = new MemoryStream())
                                {
                                    using (ZipArchive zipArchive = new ZipArchive(zipStream, ZipArchiveMode.Create, true))
                                    {
                                        for (int i = 0; i < 4; i++)
                                        {
                                            byte[] compressedData = reader[i] as byte[];
                                            if (compressedData != null && compressedData.Length > 0)
                                            {
                                                try
                                                {
                                                    // فك ضغط البيانات
                                                    byte[] pdfData = FileCompressor.ExtractFile(compressedData);

                                                    if (pdfData != null && IsPdfFile(pdfData))
                                                    {
                                                        filesFound = true;
                                                        string fileName = CleanFileName($"مرفق_{i + 1}_طلب_{selectedOrderNumber}_{employeeName}.pdf");

                                                        ZipArchiveEntry zipEntry = zipArchive.CreateEntry(fileName);
                                                        using (Stream entryStream = zipEntry.Open())
                                                        {
                                                            entryStream.Write(pdfData, 0, pdfData.Length);
                                                        }

                                                        System.Diagnostics.Debug.WriteLine($"✅ تم إضافة الملف: {fileName}");
                                                    }
                                                    else
                                                    {
                                                        System.Diagnostics.Debug.WriteLine($"❌ الملف {i + 1} ليس PDF صالح");
                                                    }
                                                }
                                                catch (Exception ex)
                                                {
                                                    System.Diagnostics.Debug.WriteLine($"❌ خطأ في معالجة الملف {i + 1}: {ex.Message}");
                                                    continue;  // استمر مع الملف التالي
                                                }
                                            }
                                        }
                                    }

                                    if (filesFound)
                                    {
                                        string zipFileName = CleanFileName($"مرفقات_طلب_{selectedOrderNumber}_{employeeName}.zip");

                                        Response.Clear();
                                        Response.ContentType = "application/zip";
                                        Response.AddHeader("Content-Disposition", $"attachment; filename={zipFileName}");
                                        Response.BinaryWrite(zipStream.ToArray());
                                        Response.Flush();
                                        Response.End();
                                    }
                                    else
                                    {
                                        LabelError.Text = "لا توجد ملفات صالحة لتحميلها.";
                                        LabelError.Visible = true;
                                    }
                                }
                            }
                            else
                            {
                                LabelError.Text = "لم يتم العثور على الطلب.";
                                LabelError.Visible = true;
                            }
                        }
                    }
                }
            }
            catch (System.Threading.ThreadAbortException)
            {
                // تجاهل
            }
            catch (Exception ex)
            {
                LabelError.Text = "حدث خطأ أثناء تحميل الملفات: " + ex.Message;
                LabelError.Visible = true;
                System.Diagnostics.Debug.WriteLine($"❌ خطأ عام: {ex.Message}");
            }
        }

        private bool IsPdfFile(byte[] data)
        {
            if (data == null || data.Length < 4)
                return false;

            // التحقق من PDF header
            return data[0] == 0x25 && // %
                   data[1] == 0x50 && // P
                   data[2] == 0x44 && // D
                   data[3] == 0x46;   // F
        }



        /// <summary>
        /// تحديد امتداد الملف بناءً على محتواه - تم تحسينها للتعامل مع PDF فقط
        /// </summary>
        private string DetermineFileExtension(byte[] fileData)
        {
            return ValidatePdfFormat(fileData) ? ".pdf" : null;
        }

        /// <summary>
        /// التحقق من أن الملف هو PDF صالح
        /// </summary>
        private bool ValidatePdfFormat(byte[] data)
        {
            try
            {
                if (data == null || data.Length < 4)
                {
                    System.Diagnostics.Debug.WriteLine("❌ البيانات فارغة أو قصيرة جداً");
                    return false;
                }

                // فحص توقيع PDF (PDF Signature)
                bool isPdf = data[0] == 0x25 && // %
                            data[1] == 0x50 && // P
                            data[2] == 0x44 && // D
                            data[3] == 0x46;   // F

                if (!isPdf)
                {
                    System.Diagnostics.Debug.WriteLine("❌ الملف ليس بصيغة PDF صالحة");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("✅ تم التحقق من صحة ملف PDF");
                }

                return isPdf;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في فحص نوع الملف: {ex.Message}");
                return false;
            }
        }



        protected void ddlOrderNumbers_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (ddlOrderNumbers.SelectedValue != "0")
            {
                LoadOrderDetails(ddlOrderNumbers.SelectedValue);
                string transferType = GetTransferType(ddlOrderNumbers.SelectedValue);
                bool hasRejectionFromSupervisor = CheckForSupervisorRejections(ddlOrderNumbers.SelectedValue);

                string message = "";

                // تحذير للتحويل المباشر
                if (!string.IsNullOrEmpty(transferType) && transferType.Trim() == "مباشر")
                {
                    string details = GetCoordinatorDetails(ddlOrderNumbers.SelectedValue);
                    message += $@"<div class='alert alert-warning text-right' role='alert'>
                <h5 class='alert-heading'>⚠️ تنبيه: تم تحويل الطلب مباشرة من منسق الموارد البشرية</h5>
                <hr>
                <p class='mb-0'>تفاصيل التحويل: {details}</p>
                <small class='text-muted'>تم تجاوز مرحلة المشرفين في هذا الطلب</small>
            </div>";
                }

                // تحذير شديد في حالة وجود إعادة من المشرفين
                if (hasRejectionFromSupervisor)
                {
                    message += $@"<div class='alert alert-danger text-right' role='alert'>
                <h5 class='alert-heading'>⛔ تحذير شديد!</h5>
                <hr>
                <strong>تم تحويل هذا الطلب للمدير رغم وجود إعادة من أحد المشرفين:</strong>
                <ul class='mt-2'>
                    {GetSupervisorRejectionDetails(ddlOrderNumbers.SelectedValue)}
                </ul>
                <hr>
                <small class='text-muted font-weight-bold'>يرجى مراجعة سبب الإعادة قبل اتخاذ أي إجراء</small>
            </div>";
                }

                TransferAlertPanel.InnerHtml = message;
                TransferAlertPanel.Visible = !string.IsNullOrEmpty(message);
            }
        }
        private bool CheckForSupervisorRejections(string orderNumber)
        {
            string connectionString = ConfigurationManager.ConnectionStrings["MyConnectionString"].ConnectionString;
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                string query = @"SELECT * FROM ordersTable WHERE [رقم الطلب] = @OrderNumber AND (
            [مشرف خدمات الموظفين] LIKE N'%تم الإعادة%' OR
            [مشرف إدارة تخطيط الموارد البشرية] LIKE N'%تم الإعادة%' OR
            [مشرف إدارة تقنية المعلومات] LIKE N'%تم الإعادة%' OR
            [مشرف مراقبة الدوام] LIKE N'%تم الإعادة%' OR
            [مشرف السجلات الطبية] LIKE N'%تم الإعادة%' OR
            [مشرف إدارة الرواتب والاستحقاقات] LIKE N'%تم الإعادة%' OR
            [مشرف إدارة القانونية والالتزام] LIKE N'%تم الإعادة%' OR
            [مشرف خدمات الموارد البشرية] LIKE N'%تم الإعادة%' OR
            [مشرف إدارة الإسكان] LIKE N'%تم الإعادة%' OR
            [مشرف قسم الملفات] LIKE N'%تم الإعادة%' OR
            [مشرف العيادات الخارجية] LIKE N'%تم الإعادة%' OR
            [مشرف التأمينات الاجتماعية] LIKE N'%تم الإعادة%' OR
            [مشرف وحدة مراقبة المخزون] LIKE N'%تم الإعادة%' OR
            [مشرف إدارة تنمية الإيرادات] LIKE N'%تم الإعادة%' OR
            [مشرف إدارة الأمن و السلامة] LIKE N'%تم الإعادة%' OR
            [مشرف الطب الاتصالي] LIKE N'%تم الإعادة%')";

                using (SqlCommand cmd = new SqlCommand(query, con))
                {
                    cmd.Parameters.AddWithValue("@OrderNumber", orderNumber);
                    con.Open();
                    return cmd.ExecuteScalar() != null;
                }
            }
        }

        private string GetSupervisorRejectionDetails(string orderNumber)
        {
            string connectionString = ConfigurationManager.ConnectionStrings["MyConnectionString"].ConnectionString;
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                con.Open();
                List<string> rejections = new List<string>();

                // قائمة أعمدة المشرفين
                string[] supervisorColumns = {
            "مشرف خدمات الموظفين",
            "مشرف إدارة تخطيط الموارد البشرية",
            "مشرف إدارة تقنية المعلومات",
            "مشرف مراقبة الدوام",
            "مشرف السجلات الطبية",
            "مشرف إدارة الرواتب والاستحقاقات",
            "مشرف إدارة القانونية والالتزام",
            "مشرف خدمات الموارد البشرية",
            "مشرف إدارة الإسكان",
            "مشرف قسم الملفات",
            "مشرف العيادات الخارجية",
            "مشرف التأمينات الاجتماعية",
            "مشرف وحدة مراقبة المخزون",
            "مشرف إدارة تنمية الإيرادات",
            "مشرف إدارة الأمن و السلامة",
            "مشرف الطب الاتصالي"
        };

                foreach (string column in supervisorColumns)
                {
                    string query = $@"
                SELECT [{column}] 
                FROM ordersTable 
                WHERE [رقم الطلب] = @OrderNumber 
                AND [{column}] LIKE N'%تم الإعادة%'";

                    using (SqlCommand cmd = new SqlCommand(query, con))
                    {
                        cmd.Parameters.AddWithValue("@OrderNumber", orderNumber);
                        var result = cmd.ExecuteScalar()?.ToString();

                        if (!string.IsNullOrEmpty(result))
                        {
                            // إضافة النص إلى القائمة بتنسيق HTML
                            rejections.Add($"<li class='text-danger'>{column}: {result}</li>");
                        }
                    }
                }

                // دمج العناصر وإرجاعها كـ HTML
                return string.Join("", rejections);
            }
        }

        private string GetCoordinatorDetails(string orderNumber)
        {
            string connectionString = ConfigurationManager.ConnectionStrings["MyConnectionString"].ConnectionString;
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                string query = "SELECT [تفاصيل المنسق] FROM ordersTable WHERE [رقم الطلب] = @OrderNumber";
                using (SqlCommand cmd = new SqlCommand(query, con))
                {
                    cmd.Parameters.AddWithValue("@OrderNumber", orderNumber);
                    con.Open();
                    return cmd.ExecuteScalar()?.ToString() ?? "";
                }
            }
        }
        private string GetTransferType(string orderNumber)
        {
            string connectionString = ConfigurationManager.ConnectionStrings["MyConnectionString"].ConnectionString;
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                // تعديل الاستعلام للتأكد من أنه يرجع القيمة الصحيحة
                string query = "SELECT COALESCE([نوع التحويل], '') FROM ordersTable WHERE [رقم الطلب] = @OrderNumber";
                using (SqlCommand cmd = new SqlCommand(query, con))
                {
                    cmd.Parameters.AddWithValue("@OrderNumber", orderNumber);
                    con.Open();
                    object result = cmd.ExecuteScalar();
                    string transferType = result?.ToString() ?? string.Empty;

                    // تسجيل للتأكد من القيمة المسترجعة
                    System.Diagnostics.Debug.WriteLine($"نوع التحويل للطلب {orderNumber}: {transferType}");

                    return transferType;
                }
            }
        }




        private void LoadOrderDetails(string orderNumber, string source = "main")
        {
            string connectionString = ConfigurationManager.ConnectionStrings["MyConnectionString"].ConnectionString;

            using (SqlConnection con = new SqlConnection(connectionString))
            {
                string query = @"SELECT [رقم الطلب], [تاريخ الطلب], [حالة الطلب], [نوع الطلب], [اسم الموظف],
                [القسم], [تفاصيل مقدم الطلب], [الوظيفة], [رقم الموظف], [السجل المدني], 
                [الجنسية], [رقم الجوال], [نوع التوظيف], [المؤهل], 
                [تم التأكيد/الإلغاء من مدير القسم], [تم التأكيد/الإلغاء من قبل المشرف], 
                [تم التحويل/الإلغاء من قبل المنسق], [سبب الإلغاء/الإعادة], [تفاصيل المنسق], 
                [مشرف خدمات الموظفين], [مشرف إدارة تخطيط الموارد البشرية], 
                [مشرف إدارة تقنية المعلومات], [مشرف مراقبة الدوام], [مشرف السجلات الطبية], 
                [مشرف إدارة الرواتب والاستحقاقات], [مشرف إدارة القانونية والالتزام], 
                [مشرف خدمات الموارد البشرية], [مشرف إدارة الإسكان], [مشرف قسم الملفات], 
                [مشرف العيادات الخارجية], [مشرف التأمينات الاجتماعية], [مشرف وحدة مراقبة المخزون], 
                [مشرف إدارة تنمية الإيرادات], [مشرف إدارة الأمن و السلامة], [مشرف الطب الاتصالي], 
                [مدير الموارد البشرية], [نوع التحويل], [ملاحظات المشرفين], 
                [ملف1], [ملف2], [ملف3], [ملف4] 
                        FROM OrdersTable WHERE [رقم الطلب] = @OrderNumber";

                using (SqlCommand cmd = new SqlCommand(query, con))
                {
                    cmd.Parameters.AddWithValue("@OrderNumber", orderNumber);
                    con.Open();
                    SqlDataReader reader = cmd.ExecuteReader();

                    if (reader.Read())
                    {
                        SetLabelVisibilityAndText(LabelOrderNumber, reader["رقم الطلب"].ToString());
                        SetLabelVisibilityAndText(LabelOrderDate, reader["تاريخ الطلب"].ToString());
                        SetLabelVisibilityAndText(LabelOrderStatus, reader["حالة الطلب"].ToString());
                        SetLabelVisibilityAndText(LabelOrderType, reader["نوع الطلب"].ToString());
                        SetLabelVisibilityAndText(LabelEmployeeName, reader["اسم الموظف"].ToString());
                        SetLabelVisibilityAndText(LabelDepartment, reader["القسم"].ToString());
                        SetLabelVisibilityAndText(LabelNotes, reader["تفاصيل مقدم الطلب"].ToString());
                        SetLabelVisibilityAndText(LabelJobTitle, reader["الوظيفة"].ToString());
                        SetLabelVisibilityAndText(LabelEmployeeNumber, reader["رقم الموظف"].ToString());
                        SetLabelVisibilityAndText(LabelCivilRegistry, reader["السجل المدني"].ToString());
                        SetLabelVisibilityAndText(LabelNationality, reader["الجنسية"].ToString());
                        SetLabelVisibilityAndText(LabelMobileNumber, reader["رقم الجوال"].ToString());
                        SetLabelVisibilityAndText(LabelEmploymentType, reader["نوع التوظيف"].ToString());
                        SetLabelVisibilityAndText(LabelQualification, reader["المؤهل"].ToString());
                        SetLabelVisibilityAndText(LabelManagerApproval, reader["تم التأكيد/الإلغاء من مدير القسم"].ToString());
                        SetLabelVisibilityAndText(LabelSupervisorApproval, reader["تم التأكيد/الإلغاء من قبل المشرف"].ToString());
                        SetLabelVisibilityAndText(LabelCoordinatorApproval, reader["تم التحويل/الإلغاء من قبل المنسق"].ToString());
                        SetLabelVisibilityAndText(LabelCancellationReason, reader["سبب الإلغاء/الإعادة"].ToString());
                        SetLabelVisibilityAndText(LabelCoordinatorDetails, reader["تفاصيل المنسق"].ToString());
                        SetLabelVisibilityAndText1(LabelMedicalServicesPermission, reader["مشرف خدمات الموظفين"].ToString());
                        SetLabelVisibilityAndText1(LabelHRPlanningPermission, reader["مشرف إدارة تخطيط الموارد البشرية"].ToString());
                        SetLabelVisibilityAndText1(LabelITPermission, reader["مشرف إدارة تقنية المعلومات"].ToString());
                        SetLabelVisibilityAndText1(LabelAttendanceControlPermission, reader["مشرف مراقبة الدوام"].ToString());
                        SetLabelVisibilityAndText1(LabelMedicalRecordsPermission, reader["مشرف السجلات الطبية"].ToString());
                        SetLabelVisibilityAndText1(LabelPayrollPermission, reader["مشرف إدارة الرواتب والاستحقاقات"].ToString());
                        SetLabelVisibilityAndText1(LabelLegalCompliancePermission, reader["مشرف إدارة القانونية والالتزام"].ToString());
                        SetLabelVisibilityAndText1(LabelHRServicesPermission, reader["مشرف خدمات الموارد البشرية"].ToString());
                        SetLabelVisibilityAndText1(LabelHousingPermission, reader["مشرف إدارة الإسكان"].ToString());
                        SetLabelVisibilityAndText1(LabelFilesSectionPermission, reader["مشرف قسم الملفات"].ToString());
                        SetLabelVisibilityAndText1(LabelOutpatientPermission, reader["مشرف العيادات الخارجية"].ToString());
                        SetLabelVisibilityAndText1(LabelSocialInsurancePermission, reader["مشرف التأمينات الاجتماعية"].ToString());
                        SetLabelVisibilityAndText1(LabelInventoryControlPermission, reader["مشرف وحدة مراقبة المخزون"].ToString());
                        SetLabelVisibilityAndText1(LabelSelfResourcesPermission, reader["مشرف إدارة تنمية الإيرادات"].ToString());
                        SetLabelVisibilityAndText1(LabelNursingPermission, reader["مشرف إدارة الأمن و السلامة"].ToString());
                        SetLabelVisibilityAndText1(LabelEmployeeServicesPermission, reader["مشرف الطب الاتصالي"].ToString());
                        SetLabelVisibilityAndText(LabelHRManagerApproval, reader["مدير الموارد البشرية"].ToString());
                        OrderDetailsPanel.Visible = true;
                        detailsPanel.Visible = false;
                    }
                    else
                    {
                        OrderDetailsPanel.Visible = false;
                        detailsPanel.Visible = false;
                        LabelError.Text = "لم يتم العثور على بيانات الطلب.";
                        LabelError.Visible = true;
                    }
                }
            }
        }

        private void SetLabelVisibilityAndText(Label label, string text)
        {
            if (!string.IsNullOrWhiteSpace(text))
            {


                DateTime dateValue;
                if (DateTime.TryParse(text, out dateValue))
                {
                    label.Text = dateValue.ToString("yyyy-MM-dd");
                    label.Visible = true;
                }
                else
                {

                    label.Text = text;
                    label.Visible = true;
                }
            }
            else
            {
                label.Visible = false;
            }


        }


        private void SetLabelVisibilityAndText1(Label label, string text)
        {
            if (text.Contains("اعتماد بواسطة"))
            {
                text = text.Replace("اعتماد بواسطة", "").Trim();
            }

            if (string.IsNullOrEmpty(text))
            {
                text = "/";
            }

            label.Visible = true; // Make sure the label is visible
            label.Text = text; // Set the text of the label

        }
        private void PopulateOrderNumbers()
        {
            string department = Session["UserPermission"]?.ToString().Trim();
            if (string.IsNullOrEmpty(department))
            {
                LabelError.Text = "صلاحية المستخدم غير محددة.";
                LabelError.Visible = true;
                return;
            }

            try
            {
                using (var con = new SqlConnection(ConfigurationManager.ConnectionStrings["MyConnectionString"].ConnectionString))
                {
                    string query = @"
                SELECT DISTINCT
                    [رقم الطلب],
                    [اسم الموظف],
                    CASE 
                        WHEN [اسم الموظف] IS NULL OR [اسم الموظف] = '' 
                        THEN CAST([رقم الطلب] AS NVARCHAR(50))
                        ELSE CAST([رقم الطلب] AS NVARCHAR(50)) + ' | ' + [اسم الموظف]
                    END AS DisplayText
                FROM ordersTable 
                WHERE [حالة الطلب] = '(D)'
                ORDER BY [رقم الطلب] DESC";

                    using (var cmd = new SqlCommand(query, con))
                    {
                        con.Open();
                        using (var reader = cmd.ExecuteReader())
                        {
                            ddlOrderNumbers.Items.Clear();
                            ddlOrderNumbers.DataSource = reader;
                            ddlOrderNumbers.DataTextField = "DisplayText";
                            ddlOrderNumbers.DataValueField = "رقم الطلب";
                            ddlOrderNumbers.DataBind();
                        }
                    }
                }

                ddlOrderNumbers.Items.Insert(0, new ListItem("-- اختر رقم الطلب --", ""));
            }
            catch (Exception ex)
            {
                LabelError.Text = "حدث خطأ أثناء تحميل الطلبات: " + ex.Message;
                LabelError.Visible = true;
            }
        }

        protected void chkAutoDeleteAttachments_CheckedChanged(object sender, EventArgs e)
        {
            bool autoDelete = chkAutoDeleteAttachments.Checked;

            // تحديث الإعداد في Web.config
            Configuration config = WebConfigurationManager.OpenWebConfiguration("~");
            config.AppSettings.Settings["AutoDeleteAttachments"].Value = autoDelete.ToString();
            config.Save();

            UpdateAutoDeleteStatus(autoDelete);
        }

        private void UpdateAutoDeleteStatus(bool autoDelete)
        {
            lblAutoDeleteStatus.Text = autoDelete ?
                "✓ سيتم حذف المرفقات تلقائياً عند اعتماد الطلب" :
                "✗ لن يتم حذف المرفقات تلقائياً عند اعتماد الطلب";
            lblAutoDeleteStatus.ForeColor = autoDelete ?
                System.Drawing.Color.Green :
                System.Drawing.Color.Red;
        }

        protected void btnConfirmOrder_Click(object sender, EventArgs e)
        {
            // 1. التحقق من المدخلات
            if (string.IsNullOrEmpty(ddlOrderNumbers.SelectedValue))
            {
                LabelError.Text = "يرجى اختيار رقم الطلب";
                LabelError.Visible = true;
                return;
            }

            string hrManagerName = Session["Username"]?.ToString();
            if (string.IsNullOrEmpty(hrManagerName))
            {
                LabelError.Text = "لا يمكن تحديد مدير الموارد البشرية. يرجى تسجيل الدخول مرة أخرى.";
                LabelError.Visible = true;
                return;
            }

            try
            {
                // 2. تحضير البيانات
                string selectedOrderNumber = ddlOrderNumbers.SelectedValue;
                string currentDate = DateTime.Now.ToString("yyyy-MM-dd");

                // تنسيق النص مع ضمان ترتيب صحيح للتاريخ (التاريخ في نهاية النص)
                string managerNameWithDate = $" {hrManagerName.Trim()} {currentDate}";

                bool autoDelete = bool.Parse(ConfigurationManager.AppSettings["AutoDeleteAttachments"]);
                string connectionString = ConfigurationManager.ConnectionStrings["MyConnectionString"].ConnectionString;

                // 3. تنفيذ العملية في قاعدة البيانات
                using (SqlConnection con = new SqlConnection(connectionString))
                {
                    con.Open();
                    using (SqlTransaction transaction = con.BeginTransaction())
                    {
                        try
                        {
                            // تحديث مع أو بدون حذف المرفقات
                            string updateQuery = autoDelete ?
                                @"UPDATE ordersTable 
                          SET [حالة الطلب] = 'مقبول',
                              [مدير الموارد البشرية] = @ManagerNameWithDate,
                              [ملف1] = NULL,
                              [ملف2] = NULL,
                              [ملف3] = NULL,
                              [ملف4] = NULL
                          WHERE [رقم الطلب] = @OrderNumber 
                          AND [حالة الطلب] = '(D)'" : // التأكد من أن حالة الطلب هي (D)
                                @"UPDATE ordersTable 
                          SET [حالة الطلب] = 'مقبول',
                              [مدير الموارد البشرية] = @ManagerNameWithDate
                          WHERE [رقم الطلب] = @OrderNumber 
                          AND [حالة الطلب] = '(D)'";

                            using (SqlCommand cmd = new SqlCommand(updateQuery, con, transaction))
                            {
                                // إضافة المعاملات
                                cmd.Parameters.AddWithValue("@OrderNumber", selectedOrderNumber);
                                cmd.Parameters.AddWithValue("@ManagerNameWithDate", managerNameWithDate);

                                // تنفيذ الأمر
                                int rowsAffected = cmd.ExecuteNonQuery();
                                if (rowsAffected > 0)
                                {
                                    // إتمام العملية بنجاح
                                    transaction.Commit();

                                    // تحديث واجهة المستخدم
                                    string successMessage = autoDelete ?
                                        "تم قبول الطلب بنجاح وحذف المرفقات" :
                                        "تم قبول الطلب بنجاح";

                                    LabelMessage.Text = successMessage;
                                    OrderDetailsPanel.Visible = false;
                                    LabelMessage.Visible = true;
                                    LabelError.Visible = false;

                                    // تحديث القوائم
                                    PopulateOrderNumbers();
                                    if (Master is SiteMaster master)
                                    {
                                        master.UpdateNewOrdersCount();
                                    }


                                }
                                else
                                {
                                    throw new Exception("لم يتم العثور على الطلب المحدد أو الطلب ليس في الحالة المناسبة");
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            transaction.Rollback();
                            throw new Exception("حدث خطأ أثناء تحديث الطلب: " + ex.Message);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LabelError.Text = "حدث خطأ أثناء معالجة الطلب: " + ex.Message;
                LabelError.Visible = true;
                LabelMessage.Visible = false;

                // تسجيل الخطأ للمتابعة
                System.Diagnostics.Debug.WriteLine($"خطأ في btnConfirmOrder_Click: {ex.Message}");
            }
        }




        protected void btnRejectOrder_Click(object sender, EventArgs e)
        {
            // التحقق من اختيار رقم طلب
            if (ddlOrderNumbers.SelectedIndex == 0)
            {
                LabelError.Text = "يرجى اختيار رقم الطلب.";
                LabelError.Visible = true;
                LabelMessage.Visible = false;
                return;
            }

            // التحقق من إدخال سبب الإلغاء
            string rejectReason = txtRejectReason.Text.Trim();
            if (string.IsNullOrEmpty(rejectReason))
            {
                LabelError.Text = "يرجى إدخال سبب الإلغاء.";
                LabelError.Visible = true;
                return;
            }

            string selectedOrderNumber = ddlOrderNumbers.SelectedValue;
            string confirmedBy = Session["Username"]?.ToString();

            // التحقق من وجود اسم المدير
            if (string.IsNullOrEmpty(confirmedBy))
            {
                LabelError.Text = "لم يتم العثور على اسم المدير. يرجى تسجيل الدخول مرة أخرى.";
                LabelError.Visible = true;
                return;
            }

            try
            {
                string connectionString = ConfigurationManager.ConnectionStrings["MyConnectionString"].ConnectionString;
                using (SqlConnection con = new SqlConnection(connectionString))
                {
                    // تحضير معلومات الإجراء
                    string currentDate = DateTime.Now.ToString("yyyy-MM-dd");
                    string managerActionInfo = $"{currentDate} - تم الإلغاء بواسطة {confirmedBy}";

                    string query = @"
           UPDATE ordersTable 
           SET [حالة الطلب] = N'تم الإلغاء من قبل المدير',
               [سبب الإلغاء/الإعادة] = @RejectReason,
               [مدير الموارد البشرية] = @ManagerAction
           WHERE [رقم الطلب] = @OrderNumber";

                    using (SqlCommand cmd = new SqlCommand(query, con))
                    {
                        cmd.Parameters.AddWithValue("@OrderNumber", selectedOrderNumber);
                        cmd.Parameters.AddWithValue("@RejectReason", rejectReason);
                        cmd.Parameters.AddWithValue("@ManagerAction", managerActionInfo);

                        con.Open();
                        int rowsAffected = cmd.ExecuteNonQuery();

                        if (rowsAffected > 0)
                        {
                            // نجاح العملية
                            LabelMessage.Text = "تم إلغاء الطلب بنجاح.";
                            OrderDetailsPanel.Visible = false;
                            LabelMessage.Visible = true;
                            LabelError.Visible = false;

                            // تحديث القوائم
                            PopulateOrderNumbers();
                            if (Master is SiteMaster master)
                            {
                                master.UpdateNewOrdersCount();
                            }
                        }
                        else
                        {
                            LabelError.Text = "حدث خطأ أثناء تأكيد الطلب.";
                            LabelError.Visible = true;
                            LabelMessage.Visible = false;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في btnRejectOrder_Click: {ex.Message}");
                LabelError.Text = "حدث خطأ أثناء معالجة الطلب.";
                LabelError.Visible = true;
                LabelMessage.Visible = false;
            }
        }

        protected void btnReturnOrder_Click(object sender, EventArgs e)
        {
            // التحقق من اختيار رقم طلب
            if (ddlOrderNumbers.SelectedIndex == 0)
            {
                LabelError.Text = "يرجى اختيار رقم الطلب.";
                LabelError.Visible = true;
                LabelMessage.Visible = false;
                return;
            }

            // التحقق من إدخال سبب الإعادة
            string rejectReason = txtRejectReason.Text.Trim();
            if (string.IsNullOrEmpty(rejectReason))
            {
                LabelError.Text = "يرجى إدخال سبب الإعادة.";
                LabelError.Visible = true;
                return;
            }

            string selectedOrderNumber = ddlOrderNumbers.SelectedValue;
            string confirmedBy = Session["Username"]?.ToString();

            // التحقق من وجود اسم المدير
            if (string.IsNullOrEmpty(confirmedBy))
            {
                LabelError.Text = "لم يتم العثور على اسم المدير. يرجى تسجيل الدخول مرة أخرى.";
                LabelError.Visible = true;
                return;
            }

            try
            {
                string connectionString = ConfigurationManager.ConnectionStrings["MyConnectionString"].ConnectionString;
                using (SqlConnection con = new SqlConnection(connectionString))
                {
                    // تحضير معلومات الإجراء
                    string currentDate = DateTime.Now.ToString("yyyy-MM-dd");
                    string managerActionInfo = $"{currentDate} - تم الإعادة بواسطة {confirmedBy}";

                    string query = @"
           UPDATE ordersTable 
           SET [حالة الطلب] = N'أُعيد بواسطة المدير',
               [سبب الإلغاء/الإعادة] = @RejectReason,
               [مدير الموارد البشرية] = @ManagerAction
           WHERE [رقم الطلب] = @OrderNumber";

                    using (SqlCommand cmd = new SqlCommand(query, con))
                    {
                        cmd.Parameters.AddWithValue("@OrderNumber", selectedOrderNumber);
                        cmd.Parameters.AddWithValue("@RejectReason", rejectReason);
                        cmd.Parameters.AddWithValue("@ManagerAction", managerActionInfo);

                        con.Open();
                        int rowsAffected = cmd.ExecuteNonQuery();

                        if (rowsAffected > 0)
                        {
                            // نجاح العملية
                            LabelMessage.Text = "تم إعادة الطلب إلى المنسق بنجاح.";
                            OrderDetailsPanel.Visible = false;
                            LabelMessage.Visible = true;
                            LabelError.Visible = false;

                            // تحديث القوائم
                            PopulateOrderNumbers();
                            if (Master is SiteMaster master)
                            {
                                master.UpdateNewOrdersCount();
                            }
                        }
                        else
                        {
                            LabelError.Text = "حدث خطأ أثناء إعادة الطلب.";
                            LabelError.Visible = true;
                            LabelMessage.Visible = false;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في btnReturnOrder_Click: {ex.Message}");
                LabelError.Text = "حدث خطأ أثناء معالجة الطلب.";
                LabelError.Visible = true;
                LabelMessage.Visible = false;
            }
        }
        protected void btnChangeStatus_Click(object sender, EventArgs e)
        {
            if (ddlChangeStatusOrders.SelectedIndex == 0)  // تم تغيير اسم عنصر التحكم
            {
                LabelError.Text = "يرجى اختيار رقم الطلب.";
                LabelError.Visible = true;
                return;
            }

            if (ddlNewStatus.SelectedIndex == 0)
            {
                LabelError.Text = "يرجى اختيار الحالة الجديدة.";
                LabelError.Visible = true;
                return;
            }

            string selectedOrderNumber = ddlChangeStatusOrders.SelectedValue;  // تم تغيير اسم عنصر التحكم
            string newStatus = ddlNewStatus.SelectedValue;
            string notes = txtNotes.Text.Trim();
            string hrManagerUsername = Session["Username"]?.ToString();

            try
            {
                using (SqlConnection con = new SqlConnection(connectionString))
                {
                    string updateQuery = @"
                UPDATE ordersTable 
                SET [حالة الطلب] = @NewStatus,
                    [مدير الموارد البشرية] = @HRManagerAction,
                    [ملاحظات المشرفين] = ISNULL([ملاحظات المشرفين], '') + @Notes
                WHERE [رقم الطلب] = @OrderNumber";

                    // Then, clean supervisor fields while preserving approvals
                    string cleanSupervisorsQuery = @"
                UPDATE ordersTable 
                SET 
                    [مشرف خدمات الموظفين] = CASE 
                        WHEN [مشرف خدمات الموظفين] LIKE N'%اعتماد بواسطة%' THEN [مشرف خدمات الموظفين]
                        ELSE NULL 
                    END,
                    [مشرف إدارة تخطيط الموارد البشرية] = CASE 
                        WHEN [مشرف إدارة تخطيط الموارد البشرية] LIKE N'%اعتماد بواسطة%' THEN [مشرف إدارة تخطيط الموارد البشرية]
                        ELSE NULL 
                    END,
                    [مشرف إدارة تقنية المعلومات] = CASE 
                        WHEN [مشرف إدارة تقنية المعلومات] LIKE N'%اعتماد بواسطة%' THEN [مشرف إدارة تقنية المعلومات]
                        ELSE NULL 
                    END,
                    [مشرف مراقبة الدوام] = CASE 
                        WHEN [مشرف مراقبة الدوام] LIKE N'%اعتماد بواسطة%' THEN [مشرف مراقبة الدوام]
                        ELSE NULL 
                    END,
                    [مشرف السجلات الطبية] = CASE 
                        WHEN [مشرف السجلات الطبية] LIKE N'%اعتماد بواسطة%' THEN [مشرف السجلات الطبية]
                        ELSE NULL 
                    END,
                    [مشرف إدارة الرواتب والاستحقاقات] = CASE 
                        WHEN [مشرف إدارة الرواتب والاستحقاقات] LIKE N'%اعتماد بواسطة%' THEN [مشرف إدارة الرواتب والاستحقاقات]
                        ELSE NULL 
                    END,
                    [مشرف إدارة القانونية والالتزام] = CASE 
                        WHEN [مشرف إدارة القانونية والالتزام] LIKE N'%اعتماد بواسطة%' THEN [مشرف إدارة القانونية والالتزام]
                        ELSE NULL 
                    END,
                    [مشرف خدمات الموارد البشرية] = CASE 
                        WHEN [مشرف خدمات الموارد البشرية] LIKE N'%اعتماد بواسطة%' THEN [مشرف خدمات الموارد البشرية]
                        ELSE NULL 
                    END,
                    [مشرف إدارة الإسكان] = CASE 
                        WHEN [مشرف إدارة الإسكان] LIKE N'%اعتماد بواسطة%' THEN [مشرف إدارة الإسكان]
                        ELSE NULL 
                    END,
                    [مشرف قسم الملفات] = CASE 
                        WHEN [مشرف قسم الملفات] LIKE N'%اعتماد بواسطة%' THEN [مشرف قسم الملفات]
                        ELSE NULL 
                    END,
                    [مشرف العيادات الخارجية] = CASE 
                        WHEN [مشرف العيادات الخارجية] LIKE N'%اعتماد بواسطة%' THEN [مشرف العيادات الخارجية]
                        ELSE NULL 
                    END,
                    [مشرف التأمينات الاجتماعية] = CASE 
                        WHEN [مشرف التأمينات الاجتماعية] LIKE N'%اعتماد بواسطة%' THEN [مشرف التأمينات الاجتماعية]
                        ELSE NULL 
                    END,
                    [مشرف وحدة مراقبة المخزون] = CASE 
                        WHEN [مشرف وحدة مراقبة المخزون] LIKE N'%اعتماد بواسطة%' THEN [مشرف وحدة مراقبة المخزون]
                        ELSE NULL 
                    END,
                    [مشرف إدارة تنمية الإيرادات] = CASE 
                        WHEN [مشرف إدارة تنمية الإيرادات] LIKE N'%اعتماد بواسطة%' THEN [مشرف إدارة تنمية الإيرادات]
                        ELSE NULL 
                    END,
                    [مشرف إدارة الأمن و السلامة] = CASE 
                        WHEN [مشرف إدارة الأمن و السلامة] LIKE N'%اعتماد بواسطة%' THEN [مشرف إدارة الأمن و السلامة]
                        ELSE NULL 
                    END,
                    [مشرف الطب الاتصالي] = CASE 
                        WHEN [مشرف الطب الاتصالي] LIKE N'%اعتماد بواسطة%' THEN [مشرف الطب الاتصالي]
                        ELSE NULL 
                    END
                WHERE [رقم الطلب] = @OrderNumber";

                    using (SqlCommand cmd = new SqlCommand(updateQuery + cleanSupervisorsQuery, con))
                    {
                        string hrManagerAction = $"{DateTime.Now:yyyy-MM-dd} - تم تغيير الحالة إلى {newStatus} بواسطة {hrManagerUsername}";
                        string notesWithDate = $"\n{DateTime.Now:yyyy-MM-dd} - {notes}";

                        cmd.Parameters.AddWithValue("@OrderNumber", selectedOrderNumber);
                        cmd.Parameters.AddWithValue("@NewStatus", newStatus);
                        cmd.Parameters.AddWithValue("@HRManagerAction", hrManagerAction);
                        cmd.Parameters.AddWithValue("@Notes", string.IsNullOrEmpty(notes) ? "" : notesWithDate);

                        con.Open();
                        int rowsAffected = cmd.ExecuteNonQuery();

                        if (rowsAffected > 0)
                        {
                            LabelMessage.Text = "تم تحديث حالة الطلب بنجاح.";
                            LabelMessage.Visible = true;
                            LabelError.Visible = false;

                            // تحديث القوائم والحقول
                            PopulateOrderNumbers();
                            PopulateStatusChangeList();
                            ddlNewStatus.SelectedIndex = 0;
                            txtNotes.Text = "";
                        }
                        else
                        {
                            LabelError.Text = "لم يتم العثور على الطلب المحدد.";
                            LabelError.Visible = true;
                            LabelMessage.Visible = false;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LabelError.Text = "حدث خطأ أثناء تحديث حالة الطلب: " + ex.Message;
                LabelError.Visible = true;
                LabelMessage.Visible = false;
            }
        }
        protected void ddlChangeStatusOrders_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (ddlChangeStatusOrders.SelectedIndex == 0)
            {
                detailsPanel.Visible = false;
                return;
            }
            try
            {
                LoadOrderDetails(ddlChangeStatusOrders.SelectedValue, "change");
            }
            catch (Exception ex)
            {
                LabelError.Text = "حدث خطأ أثناء تحميل تفاصيل الطلب: " + ex.Message;
                LabelError.Visible = true;
                detailsPanel.Visible = false;
            }

            try
            {
                using (SqlConnection con = new SqlConnection(connectionString))
                {
                    string query = @"
                SELECT [حالة الطلب], [نوع الطلب], [اسم الموظف], [القسم],
                       [تفاصيل مقدم الطلب], [مدير الموارد البشرية], [ملاحظات المشرفين]
                FROM ordersTable 
                WHERE [رقم الطلب] = @OrderNumber";

                    using (SqlCommand cmd = new SqlCommand(query, con))
                    {
                        cmd.Parameters.AddWithValue("@OrderNumber", ddlChangeStatusOrders.SelectedValue);
                        con.Open();
                        SqlDataReader reader = cmd.ExecuteReader();

                        if (reader.Read())
                        {
                            // عرض التفاصيل الأساسية
                            lblCurrentStatus.Text = reader["حالة الطلب"].ToString();
                            lblOrderType.Text = reader["نوع الطلب"].ToString();

                            // يمكنك إضافة المزيد من التفاصيل هنا إذا كنت ترغب في ذلك
                            // مثال:
                            // lblEmployeeName.Text = reader["اسم الموظف"].ToString();
                            // lblDepartment.Text = reader["القسم"].ToString();

                            detailsPanel.Visible = true;
                        }
                        else
                        {
                            detailsPanel.Visible = false;
                            LabelError.Text = "لم يتم العثور على الطلب المحدد.";
                            LabelError.Visible = true;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LabelError.Text = "حدث خطأ أثناء تحميل تفاصيل الطلب: " + ex.Message;
                LabelError.Visible = true;
                detailsPanel.Visible = false;
            }
        }
        // إضافة دالة جديدة لقائمة تغيير الحالة
        private void PopulateStatusChangeList(string searchTerm = "", string filter = "today")
        {
            try
            {
                using (SqlConnection con = new SqlConnection(connectionString))
                {
                    string query = @"
                SELECT DISTINCT 
                    [رقم الطلب], 
                    [اسم الموظف],
                    [تاريخ الطلب],
                    CASE 
                        WHEN [اسم الموظف] IS NULL OR [اسم الموظف] = '' 
                        THEN CAST([رقم الطلب] AS NVARCHAR(50))
                        ELSE CAST([رقم الطلب] AS NVARCHAR(50)) + ' | ' + [اسم الموظف]
                    END AS DisplayText
                FROM ordersTable 
                WHERE [حالة الطلب] <> 'مقبول'
                AND (
                    @SearchTerm = '' 
                    OR CAST([رقم الطلب] AS NVARCHAR) LIKE @SearchTerm + '%'
                    OR [اسم الموظف] LIKE N'%' + @SearchTerm + '%'
                )
                AND (
                    @Filter = 'all'
                    OR (@Filter = 'today' AND CONVERT(date, [تاريخ الطلب]) = CONVERT(date, GETDATE()))
                    OR (@Filter = 'week' AND DATEDIFF(day, [تاريخ الطلب], GETDATE()) <= 7)
                    OR (@Filter = 'month' AND DATEDIFF(month, [تاريخ الطلب], GETDATE()) = 0)
                )
                ORDER BY [رقم الطلب] DESC";

                    using (SqlCommand cmd = new SqlCommand(query, con))
                    {
                        cmd.Parameters.AddWithValue("@SearchTerm", searchTerm ?? "");
                        cmd.Parameters.AddWithValue("@Filter", filter);

                        con.Open();
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            ddlChangeStatusOrders.Items.Clear();
                            ddlChangeStatusOrders.DataSource = reader;
                            ddlChangeStatusOrders.DataTextField = "DisplayText";
                            ddlChangeStatusOrders.DataValueField = "رقم الطلب";
                            ddlChangeStatusOrders.DataBind();
                        }
                    }
                }

                // إضافة العنصر الافتراضي مع إحصائيات
                int resultCount = ddlChangeStatusOrders.Items.Count;
                string filterText;
                switch (filter)
                {
                    case "today":
                        filterText = "اليوم";
                        break;
                    case "week":
                        filterText = "الأسبوع";
                        break;
                    case "month":
                        filterText = "الشهر";
                        break;
                    case "two_months":
                        filterText = "الشهرين";
                        break;
                    case "all":
                        filterText = "الكل";
                        break;
                    default:
                        filterText = "";
                        break;
                }

                string defaultText = resultCount > 0
                    ? filter == "all"
                        ? $"-- اختر من {resultCount} طلب --"
                        : $"-- اختر من {resultCount} طلب ({filterText}) --"
                    : "-- لا توجد طلبات --";

                ddlChangeStatusOrders.Items.Insert(0, new ListItem(defaultText, ""));
            }
            catch (Exception ex)
            {
                LabelError.Text = "حدث خطأ أثناء تحميل قائمة تغيير الحالة: " + ex.Message;
                LabelError.Visible = true;
            }
        }
        private void InitializeStatusDropDown()
        {
            ddlNewStatus.Items.Clear();
            ddlNewStatus.Items.Add(new ListItem("-- اختر الحالة الجديدة --", ""));

            // حالات مدير القسم والمساعدين
            ddlNewStatus.Items.Add(new ListItem("(DM) - مدير القسم", "(DM)"));
            ddlNewStatus.Items.Add(new ListItem("(A1) - مساعد المدير للخدمات الطبية", "(A1)"));
            ddlNewStatus.Items.Add(new ListItem("(A2) - مساعد المدير لخدمات التمريض", "(A2)"));
            ddlNewStatus.Items.Add(new ListItem("(A3) - مساعد المدير للخدمات الإدارية والتشغيل", "(A3)"));
            ddlNewStatus.Items.Add(new ListItem("(A4) - مساعد المدير للموارد البشرية", "(A4)"));

            // حالات المعالجة
            ddlNewStatus.Items.Add(new ListItem("(B) - منسق الموارد البشرية", "(B)"));
            ddlNewStatus.Items.Add(new ListItem("(C) - المشرفون", "(C)"));
            ddlNewStatus.Items.Add(new ListItem("(D) - مدير الموارد البشرية", "(D)"));

            // الحالات الخاصة
            ddlNewStatus.Items.Add(new ListItem("مقبول", "مقبول"));
            ddlNewStatus.Items.Add(new ListItem("يتطلب إجراءات من المشرف", "يتطلب إجراءات من المشرف"));
            ddlNewStatus.Items.Add(new ListItem("يتطلب إجراءات", "يتطلب إجراءات"));
            ddlNewStatus.Items.Add(new ListItem("تم الإلغاء من مدير القسم", "تم الإلغاء من مدير القسم"));
            ddlNewStatus.Items.Add(new ListItem("تم الإلغاء من قبل المشرف", "تم الإلغاء من قبل المشرف"));
        }
        // دالة مساعدة لتنظيف التفاصيل
        private void ClearDetails()
        {
            lblCurrentStatus.Text = "";
            lblOrderType.Text = "";
            detailsPanel.Visible = false;
            LabelError.Visible = false;
            LabelMessage.Visible = false;
        }
        protected void btnToggleChangeStatusSection_Click(object sender, EventArgs e)
        {
            changeStatusSectionPanel.Visible = !changeStatusSectionPanel.Visible;
        }
        protected void txtSearch_TextChanged(object sender, EventArgs e)
        {
            PopulateStatusChangeList(txtSearch.Text.Trim(), ViewState["CurrentFilter"]?.ToString() ?? "all");
        }

        protected void btnFilter_Click(object sender, EventArgs e)
        {
            LinkButton btn = (LinkButton)sender;
            string filter = btn.CommandArgument;

            // تحديث حالة الأزرار
            btnFilterToday.CssClass = "btn btn-outline-success" + (filter == "today" ? " active" : "");
            btnFilterWeek.CssClass = "btn btn-outline-success" + (filter == "week" ? " active" : "");
            btnFilterMonth.CssClass = "btn btn-outline-success" + (filter == "month" ? " active" : "");
            btnFilterTwoMonths.CssClass = "btn btn-outline-success" + (filter == "two_months" ? " active" : "");
            btnFilterAll.CssClass = "btn btn-outline-success" + (filter == "all" ? " active" : "");

            ViewState["CurrentFilter"] = filter;
            PopulateStatusChangeList(txtSearch.Text.Trim(), filter);
        }





    }
}