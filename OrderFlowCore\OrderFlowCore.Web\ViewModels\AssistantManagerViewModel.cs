using OrderFlowCore.Application.DTOs;
using Microsoft.AspNetCore.Mvc.Rendering;
using System.Collections.Generic;
using OrderFlowCore.Core.Entities;

namespace OrderFlowCore.Web.ViewModels
{
    public class AssistantManagerViewModel
    {
        public int SelectedOrderId { get; set; }
        public List<SelectListItem> OrderNumbers { get; set; } = new List<SelectListItem>();
        public string ReturnReason { get; set; }
        public string RejectReason { get; set; }
        
        // Order Details
        public int Id { get; set; }
        public string OrderNumber { get; set; }
        public string OrderDate { get; set; }
        public OrderStatus OrderStatus { get; set; }
        public string OrderType { get; set; }
        public string EmployeeName { get; set; }
        public string Department { get; set; }
        public string JobTitle { get; set; }
        public string EmploymentType { get; set; }
        public string Qualification { get; set; }
        public string EmployeeNumber { get; set; }
        public string CivilRegistry { get; set; }
        public string Nationality { get; set; }
        public string MobileNumber { get; set; }
        public string Notes { get; set; }
        
        // Approval Status
        public string ManagerApproval { get; set; }
        public string SupervisorApproval { get; set; }
        public string CoordinatorApproval { get; set; }
        public string CancellationReason { get; set; }
        public string CoordinatorDetails { get; set; }
        
        // Supervisors Permissions
        public string MedicalServicesPermission { get; set; }
        public string HRPlanningPermission { get; set; }
        public string ITPermission { get; set; }
        public string AttendanceControlPermission { get; set; }
        public string MedicalRecordsPermission { get; set; }
        public string PayrollPermission { get; set; }
        public string LegalCompliancePermission { get; set; }
        public string HRServicesPermission { get; set; }
        public string HousingPermission { get; set; }
        public string FilesSectionPermission { get; set; }
        public string OutpatientPermission { get; set; }
        public string SocialInsurancePermission { get; set; }
        public string InventoryControlPermission { get; set; }
        public string SelfResourcesPermission { get; set; }
        public string NursingPermission { get; set; }
        public string EmployeeServicesPermission { get; set; }
        
        // HR Manager
        public string HRManagerApproval { get; set; }

        public static AssistantManagerViewModel FromOrderDetails(OrderDetailsDto orderDto)
        {
            return new AssistantManagerViewModel
            {
                Id = orderDto.Id,
                OrderNumber = orderDto.Id.ToString(),
                OrderDate = orderDto.CreatedAt.ToString("yyyy-MM-dd"),
                OrderStatus = orderDto.OrderStatus,
                OrderType = orderDto.OrderType,
                EmployeeName = orderDto.EmployeeName,
                Department = orderDto.Department,
                JobTitle = orderDto.JobTitle,
                EmploymentType = orderDto.EmploymentType,
                Qualification = orderDto.Qualification,
                EmployeeNumber = orderDto.EmployeeNumber,
                CivilRegistry = orderDto.CivilRecord,
                Nationality = orderDto.Nationality,
                MobileNumber = orderDto.MobileNumber,
                Notes = orderDto.Details,
                
                // Approval Status
                ManagerApproval = orderDto.ConfirmedByDepartmentManager,
                SupervisorApproval = orderDto.ConfirmedByAssistantManager,
                CoordinatorApproval = orderDto.ConfirmedByCoordinator,
                CancellationReason = orderDto.ReasonForCancellation,
                CoordinatorDetails = orderDto.CoordinatorDetails,
                
                // Supervisors Permissions
                MedicalServicesPermission = orderDto.SupervisorOfEmployeeServices,
                HRPlanningPermission = orderDto.SupervisorOfHumanResourcesPlanning,
                ITPermission = orderDto.SupervisorOfInformationTechnology,
                AttendanceControlPermission = orderDto.SupervisorOfAttendance,
                MedicalRecordsPermission = orderDto.SupervisorOfMedicalRecords,
                PayrollPermission = orderDto.SupervisorOfPayrollAndBenefits,
                LegalCompliancePermission = orderDto.SupervisorOfLegalAndCompliance,
                HRServicesPermission = orderDto.SupervisorOfHumanResourcesServices,
                HousingPermission = orderDto.SupervisorOfHousing,
                FilesSectionPermission = orderDto.SupervisorOfFiles,
                OutpatientPermission = orderDto.SupervisorOfOutpatientClinics,
                SocialInsurancePermission = orderDto.SupervisorOfSocialSecurity,
                InventoryControlPermission = orderDto.SupervisorOfInventoryControl,
                SelfResourcesPermission = orderDto.SupervisorOfRevenueDevelopment,
                NursingPermission = orderDto.SupervisorOfSecurity,
                EmployeeServicesPermission = orderDto.SupervisorOfMedicalConsultation,
                
                // HR Manager
                HRManagerApproval = orderDto.HumanResourcesManager
            };
        }
    }
} 