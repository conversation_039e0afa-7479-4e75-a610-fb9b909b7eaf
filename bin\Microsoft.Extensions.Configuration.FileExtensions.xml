<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.Extensions.Configuration.FileExtensions</name>
    </assembly>
    <members>
        <member name="T:Microsoft.Extensions.Configuration.FileConfigurationExtensions">
            <summary>
            Extension methods for <see cref="T:Microsoft.Extensions.Configuration.FileConfigurationProvider"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.FileConfigurationExtensions.SetFileProvider(Microsoft.Extensions.Configuration.IConfigurationBuilder,Microsoft.Extensions.FileProviders.IFileProvider)">
            <summary>
            Sets the default <see cref="T:Microsoft.Extensions.FileProviders.IFileProvider"/> to be used for file-based providers.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.Extensions.Configuration.IConfigurationBuilder"/> to add to.</param>
            <param name="fileProvider">The default file provider instance.</param>
            <returns>The <see cref="T:Microsoft.Extensions.Configuration.IConfigurationBuilder"/>.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.FileConfigurationExtensions.GetFileProvider(Microsoft.Extensions.Configuration.IConfigurationBuilder)">
            <summary>
            Gets the default <see cref="T:Microsoft.Extensions.FileProviders.IFileProvider"/> to be used for file-based providers.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.Extensions.Configuration.IConfigurationBuilder"/>.</param>
            <returns>The default <see cref="T:Microsoft.Extensions.FileProviders.IFileProvider"/>.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.FileConfigurationExtensions.SetBasePath(Microsoft.Extensions.Configuration.IConfigurationBuilder,System.String)">
            <summary>
            Sets the FileProvider for file-based providers to a PhysicalFileProvider with the base path.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.Extensions.Configuration.IConfigurationBuilder"/> to add to.</param>
            <param name="basePath">The absolute path of file-based providers.</param>
            <returns>The <see cref="T:Microsoft.Extensions.Configuration.IConfigurationBuilder"/>.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.FileConfigurationExtensions.SetFileLoadExceptionHandler(Microsoft.Extensions.Configuration.IConfigurationBuilder,System.Action{Microsoft.Extensions.Configuration.FileLoadExceptionContext})">
            <summary>
            Sets a default action to be invoked for file-based providers when an error occurs.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.Extensions.Configuration.IConfigurationBuilder"/> to add to.</param>
            <param name="handler">The Action to be invoked on a file load exception.</param>
            <returns>The <see cref="T:Microsoft.Extensions.Configuration.IConfigurationBuilder"/>.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.FileConfigurationExtensions.GetFileLoadExceptionHandler(Microsoft.Extensions.Configuration.IConfigurationBuilder)">
            <summary>
            Gets a default action to be invoked for file-based providers when an error occurs.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.Extensions.Configuration.IConfigurationBuilder"/>.</param>
            <returns>The The Action to be invoked on a file load exception, if set.</returns>
        </member>
        <member name="T:Microsoft.Extensions.Configuration.FileConfigurationProvider">
            <summary>
            Base class for file based <see cref="T:Microsoft.Extensions.Configuration.ConfigurationProvider"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.FileConfigurationProvider.#ctor(Microsoft.Extensions.Configuration.FileConfigurationSource)">
            <summary>
            Initializes a new instance with the specified source.
            </summary>
            <param name="source">The source settings.</param>
        </member>
        <member name="P:Microsoft.Extensions.Configuration.FileConfigurationProvider.Source">
            <summary>
            The source settings for this provider.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.FileConfigurationProvider.ToString">
            <summary>
            Generates a string representing this provider name and relevant details.
            </summary>
            <returns> The configuration name. </returns>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.FileConfigurationProvider.Load">
            <summary>
            Loads the contents of the file at <see cref="T:System.IO.Path"/>.
            </summary>
            <exception cref="T:System.IO.DirectoryNotFoundException">Optional is <c>false</c> on the source and a
            directory cannot be found at the specified Path.</exception>
            <exception cref="T:System.IO.FileNotFoundException">Optional is <c>false</c> on the source and a
            file does not exist at specified Path.</exception>
            <exception cref="T:System.IO.InvalidDataException">An exception was thrown by the concrete implementation of the
            <see cref="M:Microsoft.Extensions.Configuration.FileConfigurationProvider.Load"/> method. Use the source <see cref="P:Microsoft.Extensions.Configuration.FileConfigurationSource.OnLoadException"/> callback
            if you need more control over the exception.</exception>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.FileConfigurationProvider.Load(System.IO.Stream)">
            <summary>
            Loads this provider's data from a stream.
            </summary>
            <param name="stream">The stream to read.</param>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.FileConfigurationProvider.Dispose">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.Extensions.Configuration.FileConfigurationProvider.Dispose(System.Boolean)">
            <summary>
            Dispose the provider.
            </summary>
            <param name="disposing"><c>true</c> if invoked from <see cref="M:System.IDisposable.Dispose"/>.</param>
        </member>
        <member name="T:Microsoft.Extensions.Configuration.FileConfigurationSource">
            <summary>
            Represents a base class for file based <see cref="T:Microsoft.Extensions.Configuration.IConfigurationSource"/>.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Configuration.FileConfigurationSource.FileProvider">
            <summary>
            Used to access the contents of the file.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Configuration.FileConfigurationSource.Path">
            <summary>
            The path to the file.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Configuration.FileConfigurationSource.Optional">
            <summary>
            Determines if loading the file is optional.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Configuration.FileConfigurationSource.ReloadOnChange">
            <summary>
            Determines whether the source will be loaded if the underlying file changes.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Configuration.FileConfigurationSource.ReloadDelay">
            <summary>
            Number of milliseconds that reload will wait before calling Load.  This helps
            avoid triggering reload before a file is completely written. Default is 250.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Configuration.FileConfigurationSource.OnLoadException">
            <summary>
            Will be called if an uncaught exception occurs in FileConfigurationProvider.Load.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.FileConfigurationSource.Build(Microsoft.Extensions.Configuration.IConfigurationBuilder)">
            <summary>
            Builds the <see cref="T:Microsoft.Extensions.Configuration.IConfigurationProvider"/> for this source.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.Extensions.Configuration.IConfigurationBuilder"/>.</param>
            <returns>A <see cref="T:Microsoft.Extensions.Configuration.IConfigurationProvider"/></returns>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.FileConfigurationSource.EnsureDefaults(Microsoft.Extensions.Configuration.IConfigurationBuilder)">
            <summary>
            Called to use any default settings on the builder like the FileProvider or FileLoadExceptionHandler.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.Extensions.Configuration.IConfigurationBuilder"/>.</param>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.FileConfigurationSource.ResolveFileProvider">
            <summary>
            If no file provider has been set, for absolute Path, this will creates a physical file provider
            for the nearest existing directory.
            </summary>
        </member>
        <member name="T:Microsoft.Extensions.Configuration.FileLoadExceptionContext">
            <summary>
            Contains information about a file load exception.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Configuration.FileLoadExceptionContext.Provider">
            <summary>
            The <see cref="T:Microsoft.Extensions.Configuration.FileConfigurationProvider"/> that caused the exception.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Configuration.FileLoadExceptionContext.Exception">
            <summary>
            The exception that occurred in Load.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Configuration.FileLoadExceptionContext.Ignore">
            <summary>
            If true, the exception will not be rethrown.
            </summary>
        </member>
        <member name="M:System.ThrowHelper.ThrowIfNull(System.Object,System.String)">
            <summary>Throws an <see cref="T:System.ArgumentNullException"/> if <paramref name="argument"/> is null.</summary>
            <param name="argument">The reference type argument to validate as non-null.</param>
            <param name="paramName">The name of the parameter with which <paramref name="argument"/> corresponds.</param>
        </member>
        <member name="M:System.ThrowHelper.IfNullOrWhitespace(System.String,System.String)">
            <summary>
            Throws either an <see cref="T:System.ArgumentNullException"/> or an <see cref="T:System.ArgumentException"/>
            if the specified string is <see langword="null"/> or whitespace respectively.
            </summary>
            <param name="argument">String to be checked for <see langword="null"/> or whitespace.</param>
            <param name="paramName">The name of the parameter being checked.</param>
            <returns>The original value of <paramref name="argument"/>.</returns>
        </member>
        <member name="T:System.Runtime.InteropServices.LibraryImportAttribute">
            <summary>
            Attribute used to indicate a source generator should create a function for marshalling
            arguments instead of relying on the runtime to generate an equivalent marshalling function at run-time.
            </summary>
            <remarks>
            This attribute is meaningless if the source generator associated with it is not enabled.
            The current built-in source generator only supports C# and only supplies an implementation when
            applied to static, partial, non-generic methods.
            </remarks>
        </member>
        <member name="M:System.Runtime.InteropServices.LibraryImportAttribute.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Runtime.InteropServices.LibraryImportAttribute"/>.
            </summary>
            <param name="libraryName">Name of the library containing the import.</param>
        </member>
        <member name="P:System.Runtime.InteropServices.LibraryImportAttribute.LibraryName">
            <summary>
            Gets the name of the library containing the import.
            </summary>
        </member>
        <member name="P:System.Runtime.InteropServices.LibraryImportAttribute.EntryPoint">
            <summary>
            Gets or sets the name of the entry point to be called.
            </summary>
        </member>
        <member name="P:System.Runtime.InteropServices.LibraryImportAttribute.StringMarshalling">
            <summary>
            Gets or sets how to marshal string arguments to the method.
            </summary>
            <remarks>
            If this field is set to a value other than <see cref="F:System.Runtime.InteropServices.StringMarshalling.Custom" />,
            <see cref="P:System.Runtime.InteropServices.LibraryImportAttribute.StringMarshallingCustomType" /> must not be specified.
            </remarks>
        </member>
        <member name="P:System.Runtime.InteropServices.LibraryImportAttribute.StringMarshallingCustomType">
            <summary>
            Gets or sets the <see cref="T:System.Type"/> used to control how string arguments to the method are marshalled.
            </summary>
            <remarks>
            If this field is specified, <see cref="P:System.Runtime.InteropServices.LibraryImportAttribute.StringMarshalling" /> must not be specified
            or must be set to <see cref="F:System.Runtime.InteropServices.StringMarshalling.Custom" />.
            </remarks>
        </member>
        <member name="P:System.Runtime.InteropServices.LibraryImportAttribute.SetLastError">
            <summary>
            Gets or sets whether the callee sets an error (SetLastError on Windows or errno
            on other platforms) before returning from the attributed method.
            </summary>
        </member>
        <member name="T:System.Runtime.InteropServices.StringMarshalling">
            <summary>
            Specifies how strings should be marshalled for generated p/invokes
            </summary>
        </member>
        <member name="F:System.Runtime.InteropServices.StringMarshalling.Custom">
            <summary>
            Indicates the user is suppling a specific marshaller in <see cref="P:System.Runtime.InteropServices.LibraryImportAttribute.StringMarshallingCustomType"/>.
            </summary>
        </member>
        <member name="F:System.Runtime.InteropServices.StringMarshalling.Utf8">
            <summary>
            Use the platform-provided UTF-8 marshaller.
            </summary>
        </member>
        <member name="F:System.Runtime.InteropServices.StringMarshalling.Utf16">
            <summary>
            Use the platform-provided UTF-16 marshaller.
            </summary>
        </member>
        <member name="P:System.SR.Error_ExpectedPhysicalPath">
            <summary>The expected physical path was '{0}'.</summary>
        </member>
        <member name="P:System.SR.Error_FileNotFound">
            <summary>The configuration file '{0}' was not found and is not optional.</summary>
        </member>
        <member name="P:System.SR.Error_FailedToLoad">
            <summary>Failed to load configuration from file '{0}'.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.AllowNullAttribute">
            <summary>Specifies that null is allowed as an input even if the corresponding type disallows it.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.DisallowNullAttribute">
            <summary>Specifies that null is disallowed as an input even if the corresponding type allows it.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.MaybeNullAttribute">
            <summary>Specifies that an output may be null even if the corresponding type disallows it.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.NotNullAttribute">
            <summary>Specifies that an output will not be null even if the corresponding type allows it. Specifies that an input argument was not null when the call returns.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.MaybeNullWhenAttribute">
            <summary>Specifies that when a method returns <see cref="P:System.Diagnostics.CodeAnalysis.MaybeNullWhenAttribute.ReturnValue"/>, the parameter may be null even if the corresponding type disallows it.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MaybeNullWhenAttribute.#ctor(System.Boolean)">
            <summary>Initializes the attribute with the specified return value condition.</summary>
            <param name="returnValue">
            The return value condition. If the method returns this value, the associated parameter may be null.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.MaybeNullWhenAttribute.ReturnValue">
            <summary>Gets the return value condition.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.NotNullWhenAttribute">
            <summary>Specifies that when a method returns <see cref="P:System.Diagnostics.CodeAnalysis.NotNullWhenAttribute.ReturnValue"/>, the parameter will not be null even if the corresponding type allows it.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.NotNullWhenAttribute.#ctor(System.Boolean)">
            <summary>Initializes the attribute with the specified return value condition.</summary>
            <param name="returnValue">
            The return value condition. If the method returns this value, the associated parameter will not be null.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.NotNullWhenAttribute.ReturnValue">
            <summary>Gets the return value condition.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.NotNullIfNotNullAttribute">
            <summary>Specifies that the output will be non-null if the named parameter is non-null.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.NotNullIfNotNullAttribute.#ctor(System.String)">
            <summary>Initializes the attribute with the associated parameter name.</summary>
            <param name="parameterName">
            The associated parameter name.  The output will be non-null if the argument to the parameter specified is non-null.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.NotNullIfNotNullAttribute.ParameterName">
            <summary>Gets the associated parameter name.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.DoesNotReturnAttribute">
            <summary>Applied to a method that will never return under any circumstance.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.DoesNotReturnIfAttribute">
            <summary>Specifies that the method will not return if the associated Boolean parameter is passed the specified value.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.DoesNotReturnIfAttribute.#ctor(System.Boolean)">
            <summary>Initializes the attribute with the specified parameter value.</summary>
            <param name="parameterValue">
            The condition parameter value. Code after the method will be considered unreachable by diagnostics if the argument to
            the associated parameter matches this value.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.DoesNotReturnIfAttribute.ParameterValue">
            <summary>Gets the condition parameter value.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.MemberNotNullAttribute">
            <summary>Specifies that the method or property will ensure that the listed field and property members have not-null values.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MemberNotNullAttribute.#ctor(System.String)">
            <summary>Initializes the attribute with a field or property member.</summary>
            <param name="member">
            The field or property member that is promised to be not-null.
            </param>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MemberNotNullAttribute.#ctor(System.String[])">
            <summary>Initializes the attribute with the list of field and property members.</summary>
            <param name="members">
            The list of field and property members that are promised to be not-null.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.MemberNotNullAttribute.Members">
            <summary>Gets field or property member names.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.MemberNotNullWhenAttribute">
            <summary>Specifies that the method or property will ensure that the listed field and property members have not-null values when returning with the specified return value condition.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MemberNotNullWhenAttribute.#ctor(System.Boolean,System.String)">
            <summary>Initializes the attribute with the specified return value condition and a field or property member.</summary>
            <param name="returnValue">
            The return value condition. If the method returns this value, the associated parameter will not be null.
            </param>
            <param name="member">
            The field or property member that is promised to be not-null.
            </param>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MemberNotNullWhenAttribute.#ctor(System.Boolean,System.String[])">
            <summary>Initializes the attribute with the specified return value condition and list of field and property members.</summary>
            <param name="returnValue">
            The return value condition. If the method returns this value, the associated parameter will not be null.
            </param>
            <param name="members">
            The list of field and property members that are promised to be not-null.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.MemberNotNullWhenAttribute.ReturnValue">
            <summary>Gets the return value condition.</summary>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.MemberNotNullWhenAttribute.Members">
            <summary>Gets field or property member names.</summary>
        </member>
    </members>
</doc>
