using OrderFlowCore.Application.DTOs;

namespace OrderFlowCore.Application.Interfaces.Repositories;

public interface INationalityRepository
{
    Task<IEnumerable<NationalityDto>> GetAllAsync();
    Task<NationalityDto?> GetByIdAsync(int id);
    Task<bool> CreateAsync(NationalityDto dto);
    Task<bool> UpdateAsync(NationalityDto dto);
    Task<bool> DeleteAsync(int id);
    Task<bool> ExistsAsync(string name, int? excludeId = null);
} 