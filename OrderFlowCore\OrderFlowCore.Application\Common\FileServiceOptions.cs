namespace OrderFlowCore.Application.Common
{
    public class FileServiceOptions
    {
        public const string SectionName = "FileService";
        
        public int MaxFileSizeInMB { get; set; } = 10;
        public string AllowedFileExtensions { get; set; } = ".pdf,.doc,.docx,.jpg,.jpeg,.png";
        public string UploadDirectory { get; set; } = "uploads";
        public int MaxFileNameLength { get; set; } = 100;
        public bool CreateDirectoryIfNotExists { get; set; } = true;
        public bool OverwriteExistingFiles { get; set; } = false;
        public string DefaultFilePrefix { get; set; } = "file";
    }
} 