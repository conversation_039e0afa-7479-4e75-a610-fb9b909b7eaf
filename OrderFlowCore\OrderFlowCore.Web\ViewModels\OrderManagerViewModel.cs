using Microsoft.AspNetCore.Mvc.Rendering;
using OrderFlowCore.Core.Entities;
using System;
using System.Collections.Generic;

namespace OrderFlowCore.Web.ViewModels
{
    public class OrderManagerViewModel
    {
        public List<OrderSummaryViewModel> Orders { get; set; } = new List<OrderSummaryViewModel>();
        public int? SelectedOrderId { get; set; }
        public OrderDetailsViewModel SelectedOrderDetails { get; set; }
        public List<SelectListItem> StatusOptions { get; set; } = new List<SelectListItem>();
        public string SuccessMessage { get; set; }
        public string ErrorMessage { get; set; }
        public bool AutoDeleteAttachments { get; set; }
        public string CurrentFilter { get; set; }
        public string SearchTerm { get; set; }
    }

    public class OrderSummaryViewModel
    {
        public int OrderId { get; set; }
        public string EmployeeName { get; set; }
        public string DisplayText { get; set; }
        public DateTime OrderDate { get; set; }
        public OrderStatus Status { get; set; }
    }
} 