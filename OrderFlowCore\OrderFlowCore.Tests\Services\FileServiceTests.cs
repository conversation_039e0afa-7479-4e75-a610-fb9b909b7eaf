using Xunit;
using Moq;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using OrderFlowCore.Application.Services;
using OrderFlowCore.Application.Interfaces.Services;
using OrderFlowCore.Application.Common;
using OrderFlowCore.Core.Models;
using OrderFlowCore.Core.Entities;
using System.Text;

namespace OrderFlowCore.Tests.Services
{
    public class FileServiceTests : IDisposable
    {
        private readonly Mock<IEnvironmentService> _mockEnvironmentService;
        private readonly Mock<ILogger<FileService>> _mockLogger;
        private readonly Mock<IOptions<FileServiceOptions>> _mockOptions;
        private readonly FileService _fileService;
        private readonly string _testDirectory;

        public FileServiceTests()
        {
            _mockEnvironmentService = new Mock<IEnvironmentService>();
            _mockLogger = new Mock<ILogger<FileService>>();
            _mockOptions = new Mock<IOptions<FileServiceOptions>>();

            // Create a temporary test directory
            _testDirectory = Path.Combine(Path.GetTempPath(), Guid.NewGuid().ToString());
            Directory.CreateDirectory(_testDirectory);

            var options = new FileServiceOptions
            {
                MaxFileSizeInMB = 10,
                AllowedFileExtensions = ".pdf,.doc,.docx,.jpg,.jpeg,.png",
                UploadDirectory = "uploads",
                MaxFileNameLength = 100,
                CreateDirectoryIfNotExists = true,
                OverwriteExistingFiles = false,
                DefaultFilePrefix = "file"
            };

            _mockOptions.Setup(x => x.Value).Returns(options);
            _mockEnvironmentService.Setup(x => x.WebRootPath).Returns(_testDirectory);

            _fileService = new FileService(
                _mockEnvironmentService.Object,
                _mockOptions.Object,
                _mockLogger.Object
            );
        }

        [Fact]
        public async Task UploadFileAsync_WithValidFile_ReturnsSuccess()
        {
            // Arrange
            var fileData = Encoding.UTF8.GetBytes("Test file content");
            var fileName = "test.pdf";
            var prefix = "test";

            // Act
            var result = await _fileService.UploadFileAsync(fileData, fileName, prefix);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeTrue();
            result.Data.Should().NotBeNullOrEmpty();
            result.Data.Should().StartWith("/uploads/");
        }

        [Fact]
        public async Task UploadFileAsync_WithEmptyFile_ReturnsFailure()
        {
            // Arrange
            var fileData = new byte[0];
            var fileName = "test.pdf";
            var prefix = "test";

            // Act
            var result = await _fileService.UploadFileAsync(fileData, fileName, prefix);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeFalse();
            result.Message.Should().Contain("File data is empty");
        }

        [Fact]
        public async Task UploadFileAsync_WithInvalidExtension_ReturnsFailure()
        {
            // Arrange
            var fileData = Encoding.UTF8.GetBytes("Test file content");
            var fileName = "test.exe";
            var prefix = "test";

            // Act
            var result = await _fileService.UploadFileAsync(fileData, fileName, prefix);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeFalse();
            result.Message.Should().Contain("File type not allowed");
        }

        [Fact]
        public async Task UploadFileAsync_WithOversizedFile_ReturnsFailure()
        {
            // Arrange
            var fileData = new byte[11 * 1024 * 1024]; // 11MB file
            var fileName = "test.pdf";
            var prefix = "test";

            // Act
            var result = await _fileService.UploadFileAsync(fileData, fileName, prefix);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeFalse();
            result.Message.Should().Contain("File size exceeds maximum allowed size");
        }

        [Fact]
        public async Task UploadFilesAsync_WithValidFiles_ReturnsSuccess()
        {
            // Arrange
            var files = new List<byte[]>
            {
                Encoding.UTF8.GetBytes("Test file 1"),
                Encoding.UTF8.GetBytes("Test file 2")
            };
            var prefix = "test";

            // Act
            var result = await _fileService.UploadFilesAsync(files, prefix);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeTrue();
            result.Data.Should().HaveCount(2);
            result.Data.Should().AllSatisfy(url => url.Should().StartWith("/uploads/"));
        }

        [Fact]
        public async Task UploadFilesAsync_WithEmptyList_ReturnsEmptySuccess()
        {
            // Arrange
            var files = new List<byte[]>();
            var prefix = "test";

            // Act
            var result = await _fileService.UploadFilesAsync(files, prefix);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeTrue();
            result.Data.Should().BeEmpty();
        }

        [Fact]
        public async Task CreateZipFromFilesAsync_WithValidFiles_ReturnsZipData()
        {
            // Arrange
            // First create some test files
            var uploadDir = Path.Combine(_testDirectory, "uploads");
            Directory.CreateDirectory(uploadDir);

            var file1Path = Path.Combine(uploadDir, "test1.txt");
            var file2Path = Path.Combine(uploadDir, "test2.txt");
            
            await File.WriteAllTextAsync(file1Path, "Test content 1");
            await File.WriteAllTextAsync(file2Path, "Test content 2");

            var fileUrls = new List<string> { "/uploads/test1.txt", "/uploads/test2.txt" };
            var zipName = "test_archive";

            // Act
            var result = await _fileService.CreateZipFromFilesAsync(fileUrls, zipName);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeTrue();
            result.Data.Should().NotBeEmpty();
            result.Data.Length.Should().BeGreaterThan(0);
        }

        [Fact]
        public async Task CreateZipFromFilesAsync_WithEmptyFileList_ReturnsFailure()
        {
            // Arrange
            var fileUrls = new List<string>();
            var zipName = "test_archive";

            // Act
            var result = await _fileService.CreateZipFromFilesAsync(fileUrls, zipName);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeFalse();
            result.Message.Should().Contain("No files provided for ZIP creation");
        }

        [Fact]
        public async Task GenerateOrderPdfAsync_WithValidOrder_ReturnsPdfData()
        {
            // Arrange
            var order = new OrdersTable
            {
                Id = 1,
                EmployeeName = "Test Employee",
                JobTitle = "Test Job",
                EmployeeNumber = "12345",
                CivilRecord = "1234567890",
                Nationality = "Saudi",
                MobileNumber = "0501234567",
                Department = "IT",
                EmploymentType = "Full Time",
                Qualification = "Bachelor",
                OrderType = "Leave Request",
                Details = "Test details",
                OrderStatus = OrderStatus.Pending,
                CreatedAt = DateTime.UtcNow
            };

            // Act
            var result = await _fileService.GenerateOrderPdfAsync(order);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeTrue();
            result.Data.Should().NotBeEmpty();
            result.Data.Length.Should().BeGreaterThan(0);
        }

        [Fact]
        public async Task GenerateOrderPdfAsync_WithNullOrder_ReturnsFailure()
        {
            // Arrange
            OrdersTable order = null;

            // Act
            var result = await _fileService.GenerateOrderPdfAsync(order);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeFalse();
            result.Message.Should().Contain("Order data is required");
        }

        [Fact]
        public async Task FileExistsAsync_WithExistingFile_ReturnsTrue()
        {
            // Arrange
            var uploadDir = Path.Combine(_testDirectory, "uploads");
            Directory.CreateDirectory(uploadDir);
            var filePath = Path.Combine(uploadDir, "test.txt");
            await File.WriteAllTextAsync(filePath, "Test content");
            var fileUrl = "/uploads/test.txt";

            // Act
            var result = await _fileService.FileExistsAsync(fileUrl);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeTrue();
            result.Data.Should().BeTrue();
        }

        [Fact]
        public async Task FileExistsAsync_WithNonExistentFile_ReturnsFalse()
        {
            // Arrange
            var fileUrl = "/uploads/nonexistent.txt";

            // Act
            var result = await _fileService.FileExistsAsync(fileUrl);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeTrue();
            result.Data.Should().BeFalse();
        }

        public void Dispose()
        {
            // Clean up test directory
            if (Directory.Exists(_testDirectory))
            {
                Directory.Delete(_testDirectory, true);
            }
        }
    }
}
