using System.Collections.Generic;
using System.Threading.Tasks;
using OrderFlowCore.Core.Models;

namespace OrderFlowCore.Application.Interfaces.Services
{
    public interface ISupervisorsFollowUpService
    {
        Task<List<SupervisorsFollowUp>> GetBySupervisorAsync(string supervisorId);
        Task<SupervisorsFollowUp> GetAsync(string supervisorId, string civilRegistry);
        Task AddAsync(SupervisorsFollowUp record);
        Task UpdateAsync(SupervisorsFollowUp record);
        Task DeleteAsync(string supervisorId, string civilRegistry);
        Task DeleteAllAsync(string supervisorId);
    }
} 