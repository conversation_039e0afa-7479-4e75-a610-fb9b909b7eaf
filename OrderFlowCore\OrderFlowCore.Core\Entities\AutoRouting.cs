﻿
#nullable disable
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;


namespace OrderFlowCore.Core.Models;

[Table("AutoRouting")]
public partial class AutoRouting
{
    [Key]
    public int Id { get; set; }
    [StringLength(200)]
    public string OrderType { get; set; }
    [StringLength(50)]
    public string Nationality { get; set; }
    [StringLength(200)]
    public string Job { get; set; }
    public string Supervisors { get; set; }
    public bool Status { get; set; }
    public DateTime? CreatedAt { get; set; }
    public DateTime? ModifiedAt { get; set; }
    [StringLength(100)]
    public string CreatedBy { get; set; }
    [StringLength(100)]
    public string ModifiedBy { get; set; }
    public string Notes { get; set; }
}