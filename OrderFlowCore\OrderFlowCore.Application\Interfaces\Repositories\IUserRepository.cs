using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Core.Models;

namespace OrderFlowCore.Application.Interfaces.Repositories;

public interface IUserRepository
{
    Task<UserDto?> GetByUsernameAsync(string username);
    Task<UserDto?> GetByIdAsync(int id);
    Task<User?> GetByEmailAsync(string email);
    Task<User?> GetUserByIdAsync(int id);
    Task<IEnumerable<UserDto>> GetAllAsync();
    Task<UserDto> CreateAsync(UserDto user);
    Task<UserDto> UpdateAsync(UserDto user);
    Task UpdateAsync(User user);
    Task DeleteAsync(int id);
    Task DeleteAsync(User user);
    Task<bool> ExistsAsync(string username);
    Task AddAsync(User user);
} 