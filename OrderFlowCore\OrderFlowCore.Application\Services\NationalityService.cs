using System.Collections.Generic;
using System.Threading.Tasks;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Application.Interfaces.Repositories;
using OrderFlowCore.Application.Interfaces.Services;
using OrderFlowCore.Application.Common;

namespace OrderFlowCore.Application.Services;

public class NationalityService : INationalityService
{
    private readonly IUnitOfWork _unitOfWork;

    public NationalityService(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork;
    }

    public async Task<ServiceResult<IEnumerable<NationalityDto>>> GetAllAsync()
    {
        try
        {
            var nationalities = await _unitOfWork.Nationalities.GetAllAsync();
            return ServiceResult<IEnumerable<NationalityDto>>.Success(nationalities);
        }
        catch (Exception ex)
        {
            return ServiceResult<IEnumerable<NationalityDto>>.Failure($"Error retrieving nationalities: {ex.Message}");
        }
    }

    public async Task<ServiceResult<NationalityDto>> GetByIdAsync(int id)
    {
        try
        {
            var nationality = await _unitOfWork.Nationalities.GetByIdAsync(id);
            if (nationality == null)
                return ServiceResult<NationalityDto>.Failure("Nationality not found");
                
            return ServiceResult<NationalityDto>.Success(nationality);
        }
        catch (Exception ex)
        {
            return ServiceResult<NationalityDto>.Failure($"Error retrieving nationality: {ex.Message}");
        }
    }

    public async Task<ServiceResult> CreateAsync(NationalityDto dto)
    {
        try
        {
            var result = await _unitOfWork.Nationalities.CreateAsync(dto);
            await _unitOfWork.SaveChangesAsync();
            
            if (result)
                return ServiceResult.Success("Nationality created successfully");
            else
                return ServiceResult.Failure("Failed to create nationality");
        }
        catch (Exception ex)
        {
            return ServiceResult.Failure($"Error creating nationality: {ex.Message}");
        }
    }

    public async Task<ServiceResult> UpdateAsync(NationalityDto dto)
    {
        try
        {
            var result = await _unitOfWork.Nationalities.UpdateAsync(dto);
            await _unitOfWork.SaveChangesAsync();
            
            if (result)
                return ServiceResult.Success("Nationality updated successfully");
            else
                return ServiceResult.Failure("Failed to update nationality");
        }
        catch (Exception ex)
        {
            return ServiceResult.Failure($"Error updating nationality: {ex.Message}");
        }
    }

    public async Task<ServiceResult> DeleteAsync(int id)
    {
        try
        {
            var result = await _unitOfWork.Nationalities.DeleteAsync(id);
            await _unitOfWork.SaveChangesAsync();
            
            if (result)
                return ServiceResult.Success("Nationality deleted successfully");
            else
                return ServiceResult.Failure("Failed to delete nationality");
        }
        catch (Exception ex)
        {
            return ServiceResult.Failure($"Error deleting nationality: {ex.Message}");
        }
    }
} 