using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using OrderFlowCore.Application.Interfaces.Services;
using OrderFlowCore.Web.ViewModels;
using Microsoft.Extensions.Logging;

namespace OrderFlowCore.Web.Controllers;

[Authorize]
public class DistributionController : Controller
{
    private readonly IDistributionService _distributionService;
    private readonly ILogger<DistributionController> _logger;

    public DistributionController(
        IDistributionService distributionService,
        ILogger<DistributionController> logger)
    {
        _distributionService = distributionService;
        _logger = logger;
    }

    public async Task<IActionResult> Index()
    {
        var result = await _distributionService.GetDistributionDataAsync();

        if (!result.IsSuccess)
        {
            TempData["ErrorMessage"] = result.Message;
            return RedirectToAction("Index", "Dashboard");
        }

        return View(result.Data);
    }



    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> UpdateDistribution(DistributionUpdateViewModel model)
    {
        if (!ModelState.IsValid)
        {
            TempData["ErrorMessage"] = "يرجى تصحيح الأخطاء في النموذج";
            return RedirectToAction(nameof(Index));
        }

        var result = await _distributionService.UpdateDepartmentDistributionAsync(model.DepartmentName, model.AssistantManagerId);

        if (result.IsSuccess)
        {
            TempData["SuccessMessage"] = result.Message;
        }
        else
        {
            TempData["ErrorMessage"] = result.Message;
        }

        return RedirectToAction(nameof(Index));
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> BulkUpdateDistribution(BulkDistributionViewModel model)
    {
        if (!ModelState.IsValid)
        {
            TempData["ErrorMessage"] = "يرجى تصحيح الأخطاء في النموذج";
            return RedirectToAction(nameof(Index));
        }

        var result = await _distributionService.BulkUpdateDistributionAsync(model.SelectedDepartments, model.AssistantManagerId);

        if (result.IsSuccess)
        {
            var bulkResult = result.Data!;

            if (bulkResult.UpdatedCount > 0)
            {
                TempData["SuccessMessage"] = $"تم تحديث توزيع {bulkResult.UpdatedCount} قسم إلى '{bulkResult.AssistantManagerName}' بنجاح";
            }

            if (bulkResult.Errors.Any())
            {
                TempData["ErrorMessage"] = string.Join(", ", bulkResult.Errors);
            }
        }
        else
        {
            TempData["ErrorMessage"] = result.Message;
        }

        return RedirectToAction(nameof(Index));
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> ClearDistribution(string departmentName)
    {
        var result = await _distributionService.ClearDepartmentDistributionAsync(departmentName);

        if (result.IsSuccess)
        {
            TempData["SuccessMessage"] = result.Message;
        }
        else
        {
            TempData["ErrorMessage"] = result.Message;
        }

        return RedirectToAction(nameof(Index));
    }

    [HttpGet]
    public async Task<IActionResult> GetDistributionReport()
    {
        var result = await _distributionService.GetDistributionReportAsync();

        if (result.IsSuccess)
        {
            var report = result.Data!;
            var responseData = new
            {
                totalDepartments = report.TotalDepartments,
                assignedDepartments = report.AssignedDepartments,
                unassignedDepartments = report.UnassignedDepartments,
                distributionByManager = report.DistributionByManager.Select(m => new
                {
                    managerId = m.ManagerId,
                    managerName = m.ManagerName,
                    departmentCount = m.DepartmentCount,
                    departments = m.Departments
                }).ToList()
            };

            return Json(new { success = true, data = responseData });
        }

        return Json(new { success = false, message = result.Message });
    }
}
