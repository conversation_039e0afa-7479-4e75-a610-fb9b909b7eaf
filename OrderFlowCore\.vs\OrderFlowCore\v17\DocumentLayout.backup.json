{"Version": 1, "WorkspaceRootPath": "E:\\Projects\\abozyad\\OrderFlowCore\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{64E0F358-387F-448D-B07D-CEE4647637BE}|OrderFlowCore.Web\\OrderFlowCore.Web.csproj|e:\\projects\\abozyad\\orderflowcore\\orderflowcore.web\\controllers\\ordermanagercontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{64E0F358-387F-448D-B07D-CEE4647637BE}|OrderFlowCore.Web\\OrderFlowCore.Web.csproj|solutionrelative:orderflowcore.web\\controllers\\ordermanagercontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{64E0F358-387F-448D-B07D-CEE4647637BE}|OrderFlowCore.Web\\OrderFlowCore.Web.csproj|e:\\projects\\abozyad\\orderflowcore\\orderflowcore.web\\controllers\\supervisorfollowupcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{64E0F358-387F-448D-B07D-CEE4647637BE}|OrderFlowCore.Web\\OrderFlowCore.Web.csproj|solutionrelative:orderflowcore.web\\controllers\\supervisorfollowupcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{64E0F358-387F-448D-B07D-CEE4647637BE}|OrderFlowCore.Web\\OrderFlowCore.Web.csproj|e:\\projects\\abozyad\\orderflowcore\\orderflowcore.web\\controllers\\ordercoordinatorcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{64E0F358-387F-448D-B07D-CEE4647637BE}|OrderFlowCore.Web\\OrderFlowCore.Web.csproj|solutionrelative:orderflowcore.web\\controllers\\ordercoordinatorcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{F10499D8-9057-407E-B19F-023A66BDC08F}|OrderFlowCore.Application\\OrderFlowCore.Application.csproj|e:\\projects\\abozyad\\orderflowcore\\orderflowcore.application\\interfaces\\repositories\\iorderrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{F10499D8-9057-407E-B19F-023A66BDC08F}|OrderFlowCore.Application\\OrderFlowCore.Application.csproj|solutionrelative:orderflowcore.application\\interfaces\\repositories\\iorderrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{F10499D8-9057-407E-B19F-023A66BDC08F}|OrderFlowCore.Application\\OrderFlowCore.Application.csproj|e:\\projects\\abozyad\\orderflowcore\\orderflowcore.application\\services\\orderservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{F10499D8-9057-407E-B19F-023A66BDC08F}|OrderFlowCore.Application\\OrderFlowCore.Application.csproj|solutionrelative:orderflowcore.application\\services\\orderservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{F10499D8-9057-407E-B19F-023A66BDC08F}|OrderFlowCore.Application\\OrderFlowCore.Application.csproj|e:\\projects\\abozyad\\orderflowcore\\orderflowcore.application\\interfaces\\services\\iorderservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{F10499D8-9057-407E-B19F-023A66BDC08F}|OrderFlowCore.Application\\OrderFlowCore.Application.csproj|solutionrelative:orderflowcore.application\\interfaces\\services\\iorderservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{750D6A94-0B0A-43DE-900B-6EF5B2F93B95}|OrderFlowCore.Infrastructure\\OrderFlowCore.Infrastructure.csproj|e:\\projects\\abozyad\\orderflowcore\\orderflowcore.infrastructure\\repositories\\orderrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{750D6A94-0B0A-43DE-900B-6EF5B2F93B95}|OrderFlowCore.Infrastructure\\OrderFlowCore.Infrastructure.csproj|solutionrelative:orderflowcore.infrastructure\\repositories\\orderrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{64E0F358-387F-448D-B07D-CEE4647637BE}|OrderFlowCore.Web\\OrderFlowCore.Web.csproj|e:\\projects\\abozyad\\orderflowcore\\orderflowcore.web\\viewmodels\\ordercoordinatorviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{64E0F358-387F-448D-B07D-CEE4647637BE}|OrderFlowCore.Web\\OrderFlowCore.Web.csproj|solutionrelative:orderflowcore.web\\viewmodels\\ordercoordinatorviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 1, "Children": [{"$type": "Document", "DocumentIndex": 1, "Title": "SupervisorFollowUpController.cs", "DocumentMoniker": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\Controllers\\SupervisorFollowUpController.cs", "RelativeDocumentMoniker": "OrderFlowCore.Web\\Controllers\\SupervisorFollowUpController.cs", "ToolTip": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\Controllers\\SupervisorFollowUpController.cs", "RelativeToolTip": "OrderFlowCore.Web\\Controllers\\SupervisorFollowUpController.cs", "ViewState": "AgIAAAkAAAAAAAAAAAAAAFUAAAA3AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-05T16:41:02.109Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "OrderManagerController.cs", "DocumentMoniker": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\Controllers\\OrderManagerController.cs", "RelativeDocumentMoniker": "OrderFlowCore.Web\\Controllers\\OrderManagerController.cs", "ToolTip": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\Controllers\\OrderManagerController.cs", "RelativeToolTip": "OrderFlowCore.Web\\Controllers\\OrderManagerController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAsAAAAdAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-05T16:40:49.647Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "OrderRepository.cs", "DocumentMoniker": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Infrastructure\\Repositories\\OrderRepository.cs", "RelativeDocumentMoniker": "OrderFlowCore.Infrastructure\\Repositories\\OrderRepository.cs", "ToolTip": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Infrastructure\\Repositories\\OrderRepository.cs", "RelativeToolTip": "OrderFlowCore.Infrastructure\\Repositories\\OrderRepository.cs", "ViewState": "AgIAADEAAAAAAAAAAAAAADoAAAA5AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-05T16:34:29.334Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "IOrderRepository.cs", "DocumentMoniker": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Application\\Interfaces\\Repositories\\IOrderRepository.cs", "RelativeDocumentMoniker": "OrderFlowCore.Application\\Interfaces\\Repositories\\IOrderRepository.cs", "ToolTip": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Application\\Interfaces\\Repositories\\IOrderRepository.cs", "RelativeToolTip": "OrderFlowCore.Application\\Interfaces\\Repositories\\IOrderRepository.cs", "ViewState": "AgIAAAEAAAAAAAAAAAAlwA8AAAAVAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-05T16:34:12.287Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "IOrderService.cs", "DocumentMoniker": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Application\\Interfaces\\Services\\IOrderService.cs", "RelativeDocumentMoniker": "OrderFlowCore.Application\\Interfaces\\Services\\IOrderService.cs", "ToolTip": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Application\\Interfaces\\Services\\IOrderService.cs", "RelativeToolTip": "OrderFlowCore.Application\\Interfaces\\Services\\IOrderService.cs", "ViewState": "AgIAAAYAAAAAAAAAAAAAACIAAAA9AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-05T16:31:22.842Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "OrderService.cs", "DocumentMoniker": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Application\\Services\\OrderService.cs", "RelativeDocumentMoniker": "OrderFlowCore.Application\\Services\\OrderService.cs", "ToolTip": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Application\\Services\\OrderService.cs", "RelativeToolTip": "OrderFlowCore.Application\\Services\\OrderService.cs", "ViewState": "AgIAAGwCAAAAAAAAAAAiwA8DAABrAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-05T16:29:55.468Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "OrderCoordinatorViewModel.cs", "DocumentMoniker": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\ViewModels\\OrderCoordinatorViewModel.cs", "RelativeDocumentMoniker": "OrderFlowCore.Web\\ViewModels\\OrderCoordinatorViewModel.cs", "ToolTip": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\ViewModels\\OrderCoordinatorViewModel.cs", "RelativeToolTip": "OrderFlowCore.Web\\ViewModels\\OrderCoordinatorViewModel.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAUAAAARAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-05T16:22:36.755Z", "EditorCaption": ""}, {"$type": "Bookmark", "Name": "ST:0:0:{aa2115a1-9712-457b-9047-dbb71ca2cdd2}"}, {"$type": "Document", "DocumentIndex": 2, "Title": "OrderCoordinatorController.cs", "DocumentMoniker": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\Controllers\\OrderCoordinatorController.cs", "RelativeDocumentMoniker": "OrderFlowCore.Web\\Controllers\\OrderCoordinatorController.cs", "ToolTip": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\Controllers\\OrderCoordinatorController.cs", "RelativeToolTip": "OrderFlowCore.Web\\Controllers\\OrderCoordinatorController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAK4AAAAPAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-05T16:21:56.311Z", "EditorCaption": ""}]}]}]}