//using System.Collections.Generic;
//using System.Threading.Tasks;
//using OrderFlowCore.Application.Interfaces.Repositories;
//using OrderFlowCore.Application.Interfaces.Services;
//using OrderFlowCore.Core.Models;

//namespace OrderFlowCore.Application.Services
//{
//    public class SupervisorsFollowUpService : ISupervisorsFollowUpService
//    {
//        private readonly IUnitOfWork _unitOfWork;
//        public SupervisorsFollowUpService(IUnitOfWork unitOfWork)
//        {
//            _unitOfWork = unitOfWork;
//        }

//        public async Task<List<SupervisorsFollowUp>> GetBySupervisorAsync(string supervisorId)
//        {
//            return await _unitOfWork.SupervisorsFollowUps
//                .Where(x => x.SupervisorId == supervisorId)
//                .OrderBy(x => x.OwnerName)
//                .ToListAsync();
//        }

//        public async Task<SupervisorsFollowUp> GetAsync(string supervisorId, string civilRegistry)
//        {
//            return await _unitOfWork.SupervisorsFollowUps
//                .FirstOrDefaultAsync(x => x.SupervisorId == supervisorId && x.CivilRegistry == civilRegistry);
//        }

//        public async Task AddAsync(SupervisorsFollowUp record)
//        {
//            _unitOfWork.SupervisorsFollowUps.Add(record);
//            await _unitOfWork.SaveChangesAsync();
//        }

//        public async Task UpdateAsync(SupervisorsFollowUp record)
//        {
//            _unitOfWork.SupervisorsFollowUps.Update(record);
//            await _unitOfWork.SaveChangesAsync();
//        }

//        public async Task DeleteAsync(string supervisorId, string civilRegistry)
//        {
//            var entity = await GetAsync(supervisorId, civilRegistry);
//            if (entity != null)
//            {
//                _unitOfWork.SupervisorsFollowUps.Remove(entity);
//                await _unitOfWork.SaveChangesAsync();
//            }
//        }

//        public async Task DeleteAllAsync(string supervisorId)
//        {
//            var records = await _unitOfWork.SupervisorsFollowUps
//                .Where(x => x.SupervisorId == supervisorId)
//                .ToListAsync();
//            _unitOfWork.SupervisorsFollowUps.RemoveRange(records);
//            await _unitOfWork.SaveChangesAsync();
//        }
//    }
//} 