using OrderFlowCore.Application.Interfaces.Services;
using OrderFlowCore.Application.Common;
using OrderFlowCore.Core.Models;
using System.IO.Compression;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.Logging;
using System.Text;
using iText.Kernel.Pdf;
using iText.Layout;
using iText.Layout.Element;
using iText.Layout.Properties;
using iText.Kernel.Font;
using iText.IO.Font.Constants;
using OrderFlowCore.Core.Entities;


namespace OrderFlowCore.Application.Services
{
    public class FileService : IFileService
    {
        private readonly IEnvironmentService _env;
        private readonly FileServiceOptions _options;
        private readonly ILogger<FileService> _logger;

        public FileService(
            IEnvironmentService env,
            IOptions<FileServiceOptions> options,
            ILogger<FileService> logger)
        {
            _env = env;
            _options = options.Value;
            _logger = logger;
        }

        public async Task<ServiceResult<List<string>>> UploadFilesAsync(List<byte[]> files, string prefix = "file")
        {
            try
            {
                _logger.LogInformation("Starting upload of {FileCount} files with prefix {Prefix}", files?.Count ?? 0, prefix);

                if (files == null || !files.Any())
                {
                    _logger.LogWarning("No files provided for upload");
                    return ServiceResult<List<string>>.Success(new List<string>());
                }

                var fileUrls = new List<string>();
                var uploadDir = GetUploadDirectory();

                for (int i = 0; i < files.Count; i++)
                {
                    var fileData = files[i];
                    if (fileData != null && fileData.Length > 0)
                    {
                        var fileName = $"{prefix}_file{i + 1}_{DateTime.Now:yyyyMMddHHmmss}.pdf";
                        var filePath = Path.Combine(uploadDir, fileName);

                        _logger.LogDebug("Uploading file {FileName} with size {FileSize} bytes", fileName, fileData.Length);

                        await File.WriteAllBytesAsync(filePath, fileData);
                        var url = $"/{_options.UploadDirectory}/{fileName}";
                        fileUrls.Add(url);

                        _logger.LogDebug("Successfully uploaded file {FileName} to {Url}", fileName, url);
                    }
                }

                _logger.LogInformation("Successfully uploaded {UploadedCount} files", fileUrls.Count);
                return ServiceResult<List<string>>.Success(fileUrls);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading files with prefix {Prefix}", prefix);
                return ServiceResult<List<string>>.Failure($"Error uploading files: {ex.Message}");
            }
        }

        public async Task<ServiceResult<string>> UploadFileAsync(byte[] fileData, string fileName, string prefix = "file")
        {
            try
            {

                if (fileData == null || fileData.Length == 0)
                {
                    return ServiceResult<string>.Failure("File data is empty");
                }

                if (string.IsNullOrWhiteSpace(fileName))
                {
                    return ServiceResult<string>.Failure("File name is required");
                }

                // Validate file
                var validationResult = ValidateFile(fileData, fileName);
                if (!validationResult.IsSuccess)
                {
                    return ServiceResult<string>.Failure(validationResult.Message);
                }

                var uploadDir = GetUploadDirectory();
                var sanitizedFileName = SanitizeFileName(fileName);
                var newFileName = $"{prefix}_{sanitizedFileName}_{DateTime.Now:yyyyMMddHHmmss}";
                var filePath = Path.Combine(uploadDir, newFileName);

                // Check if file already exists and overwrite setting
                if (File.Exists(filePath) && !_options.OverwriteExistingFiles)
                {
                    return ServiceResult<string>.Failure("File already exists and overwrite is disabled");
                }

                await File.WriteAllBytesAsync(filePath, fileData);
                var url = $"/{_options.UploadDirectory}/{newFileName}";

                return ServiceResult<string>.Success(url);
            }
            catch (Exception ex)
            {
                return ServiceResult<string>.Failure($"Error uploading file: {ex.Message}");
            }
        }

        public async Task<ServiceResult<byte[]>> DownloadFilesZipAsync(List<string> fileUrls)
        {
            try
            {

                if (fileUrls == null || !fileUrls.Any())
                {
                    return ServiceResult<byte[]>.Failure("No files provided for download");
                }

                var files = GetFilePaths(fileUrls);
                if (files.Count == 0)
                {
                    return ServiceResult<byte[]>.Failure("No valid files found");
                }

                using (var ms = new MemoryStream())
                {
                    using (var archive = new ZipArchive(ms, ZipArchiveMode.Create, true))
                    {
                        foreach (var filePath in files)
                        {
                            if (File.Exists(filePath))
                            {
                                var entry = archive.CreateEntry(Path.GetFileName(filePath));
                                using (var entryStream = entry.Open())
                                using (var fileStream = File.OpenRead(filePath))
                                {
                                    await fileStream.CopyToAsync(entryStream);
                                }
                            }
                        }
                    }

   
                    return ServiceResult<byte[]>.Success(ms.ToArray());
                }
            }
            catch (Exception ex)
            {
                return ServiceResult<byte[]>.Failure($"Error creating zip file: {ex.Message}");
            }
        }

        public async Task<ServiceResult> DeleteFileAsync(string fileUrl)
        {
            try
            {

                if (string.IsNullOrWhiteSpace(fileUrl))
                {
                    return ServiceResult.Failure("File URL is required");
                }

                var filePath = GetFilePath(fileUrl);
                if (!File.Exists(filePath))
                {
   
                    return ServiceResult.Failure("File not found");
                }

                File.Delete(filePath);
                return ServiceResult.Success("File deleted successfully");
            }
            catch (Exception ex)
            {
                return ServiceResult.Failure($"Error deleting file: {ex.Message}");
            }
        }

        public async Task<ServiceResult> DeleteFilesAsync(List<string> fileUrls)
        {
            try
            {

                if (fileUrls == null || !fileUrls.Any())
                {
                    return ServiceResult.Success("No files to delete");
                }

                var deletedCount = 0;
                var errors = new List<string>();

                foreach (var fileUrl in fileUrls)
                {
                    var result = await DeleteFileAsync(fileUrl);
                    if (result.IsSuccess)
                    {
                        deletedCount++;
                    }
                    else
                    {
                        errors.Add($"Failed to delete {fileUrl}: {result.Message}");
                    }
                }

                if (errors.Any())
                {
   
                    return ServiceResult.Failure($"Deleted {deletedCount} files, {errors.Count} failed", errors);
                }

                return ServiceResult.Success($"Successfully deleted {deletedCount} files");
            }
            catch (Exception ex)
            {
                return ServiceResult.Failure($"Error deleting files: {ex.Message}");
            }
        }

        public async Task<ServiceResult<bool>> FileExistsAsync(string fileUrl)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(fileUrl))
                {
                    return ServiceResult<bool>.Success(false);
                }

                var filePath = GetFilePath(fileUrl);
                var exists = File.Exists(filePath);

                return ServiceResult<bool>.Success(exists);
            }
            catch (Exception ex)
            {
                return ServiceResult<bool>.Failure($"Error checking file existence: {ex.Message}");
            }
        }

        public async Task<ServiceResult<long>> GetFileSizeAsync(string fileUrl)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(fileUrl))
                {
                    return ServiceResult<long>.Failure("File URL is required");
                }

                var filePath = GetFilePath(fileUrl);
                if (!File.Exists(filePath))
                {
                    return ServiceResult<long>.Failure("File not found");
                }

                var fileInfo = new FileInfo(filePath);
                return ServiceResult<long>.Success(fileInfo.Length);
            }
            catch (Exception ex)
            {
                return ServiceResult<long>.Failure($"Error getting file size: {ex.Message}");
            }
        }

        public async Task<ServiceResult<string>> GetFileContentTypeAsync(string fileUrl)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(fileUrl))
                {
                    return ServiceResult<string>.Failure("File URL is required");
                }

                var extension = Path.GetExtension(fileUrl)?.ToLowerInvariant();
                var contentType = GetContentTypeFromExtension(extension);

                return ServiceResult<string>.Success(contentType);
            }
            catch (Exception ex)
            {
                return ServiceResult<string>.Failure($"Error getting file content type: {ex.Message}");
            }
        }

        #region Private Helper Methods

        private string GetUploadDirectory()
        {
            var uploadDir = Path.Combine(_env.WebRootPath, _options.UploadDirectory);
            
            if (_options.CreateDirectoryIfNotExists && !Directory.Exists(uploadDir))
            {
                Directory.CreateDirectory(uploadDir);
            }

            return uploadDir;
        }

        private ServiceResult ValidateFile(byte[] fileData, string fileName)
        {
            // Check file size
            if (fileData.Length > _options.MaxFileSizeInMB * 1024 * 1024)
            {
                return ServiceResult.Failure($"File size exceeds maximum allowed size of {_options.MaxFileSizeInMB}MB");
            }

            // Check file extension
            var extension = Path.GetExtension(fileName)?.ToLowerInvariant();
            if (string.IsNullOrEmpty(extension) || !_options.AllowedFileExtensions.Contains(extension))
            {
                return ServiceResult.Failure($"File type not allowed. Allowed types: {_options.AllowedFileExtensions}");
            }

            // Basic file content validation
            if (fileData.Length == 0)
            {
                return ServiceResult.Failure("File is empty");
            }

            // Simulate virus scanning (in a real implementation, you'd use a proper antivirus API)
            if (ContainsSuspiciousContent(fileData))
            {
                return ServiceResult.Failure("File contains suspicious content and cannot be uploaded");
            }

            return ServiceResult.Success();
        }

        private bool ContainsSuspiciousContent(byte[] fileData)
        {
            // Simple check for suspicious patterns (this is a basic example)
            // In a real implementation, you'd use proper antivirus scanning
            var content = Encoding.UTF8.GetString(fileData, 0, Math.Min(1024, fileData.Length));
            var suspiciousPatterns = new[] { "<script", "javascript:", "vbscript:", "onload=", "onerror=" };

            return suspiciousPatterns.Any(pattern => content.Contains(pattern, StringComparison.OrdinalIgnoreCase));
        }

        private string SanitizeFileName(string fileName)
        {
            // Remove or replace invalid characters
            var invalidChars = Path.GetInvalidFileNameChars();
            var sanitized = invalidChars.Aggregate(fileName, (current, invalidChar) => current.Replace(invalidChar, '_'));
            
            // Limit length
            if (sanitized.Length > _options.MaxFileNameLength)
            {
                var extension = Path.GetExtension(sanitized);
                sanitized = sanitized.Substring(0, _options.MaxFileNameLength - extension.Length) + extension;
            }
            
            return sanitized;
        }

        private string GetFilePath(string fileUrl)
        {
            return Path.Combine(_env.WebRootPath, fileUrl.TrimStart('/').Replace("/", Path.DirectorySeparatorChar.ToString()));
        }

        private List<string> GetFilePaths(List<string> fileUrls)
        {
            var files = new List<string>();
            
            foreach (var fileUrl in fileUrls)
            {
                if (!string.IsNullOrWhiteSpace(fileUrl))
                {
                    files.Add(GetFilePath(fileUrl));
                }
            }

            return files;
        }

        private string GetContentTypeFromExtension(string extension)
        {
            return extension?.ToLowerInvariant() switch
            {
                ".pdf" => "application/pdf",
                ".doc" => "application/msword",
                ".docx" => "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                ".jpg" => "image/jpeg",
                ".jpeg" => "image/jpeg",
                ".png" => "image/png",
                _ => "application/octet-stream"
            };
        }

        public async Task<ServiceResult<byte[]>> CreateZipFromFilesAsync(List<string> fileUrls, string zipName)
        {
            try
            {
                if (fileUrls == null || !fileUrls.Any())
                {
                    return ServiceResult<byte[]>.Failure("No files provided for ZIP creation");
                }

                var files = GetFilePaths(fileUrls);
                if (files.Count == 0)
                {
                    return ServiceResult<byte[]>.Failure("No valid files found");
                }

                using (var ms = new MemoryStream())
                {
                    using (var archive = new ZipArchive(ms, ZipArchiveMode.Create, true))
                    {
                        int processedFiles = 0;
                        foreach (var filePath in files)
                        {
                            if (File.Exists(filePath))
                            {
                                var fileName = Path.GetFileName(filePath);
                                var entry = archive.CreateEntry(fileName, CompressionLevel.Optimal);

                                using (var entryStream = entry.Open())
                                using (var fileStream = File.OpenRead(filePath))
                                {
                                    await fileStream.CopyToAsync(entryStream);
                                }

                                processedFiles++;
                            }
                        }

                        if (processedFiles == 0)
                        {
                            return ServiceResult<byte[]>.Failure("No files were successfully added to the ZIP archive");
                        }
                    }

                    return ServiceResult<byte[]>.Success(ms.ToArray());
                }
            }
            catch (Exception ex)
            {
                return ServiceResult<byte[]>.Failure($"Error creating ZIP file: {ex.Message}");
            }
        }

        public async Task<ServiceResult<byte[]>> GenerateOrderPdfAsync(OrdersTable order)
        {
            try
            {
                if (order == null)
                {
                    return ServiceResult<byte[]>.Failure("Order data is required");
                }

                using (var stream = new MemoryStream())
                {
                    // Create PDF writer and document
                    var writer = new PdfWriter(stream);
                    var pdf = new PdfDocument(writer);
                    var document = new Document(pdf);

                    // Set RTL support and font
                    document.SetTextAlignment(TextAlignment.RIGHT);

                    // Add title
                    var title = new Paragraph($"طلب رقم {order.Id}")
                        .SetTextAlignment(TextAlignment.CENTER)
                        .SetFontSize(18)
                        .SetBold();
                    document.Add(title);

                    // Add date
                    var date = new Paragraph($"تاريخ الطلب: {order.CreatedAt:yyyy-MM-dd}")
                        .SetTextAlignment(TextAlignment.CENTER)
                        .SetFontSize(12);
                    document.Add(date);

                    // Add spacing
                    document.Add(new Paragraph("\n"));

                    // Create table for order details
                    var table = new Table(4).UseAllAvailableWidth();

                    // Add rows
                    AddTableRow(table, "اسم الموظف", order.EmployeeName, "المسمى الوظيفي", order.JobTitle);
                    AddTableRow(table, "رقم الموظف", order.EmployeeNumber, "السجل المدني", order.CivilRecord);
                    AddTableRow(table, "الجنسية", order.Nationality, "رقم الجوال", order.MobileNumber);
                    AddTableRow(table, "القسم", order.Department, "نوع التوظيف", order.EmploymentType);
                    AddTableRow(table, "المؤهل", order.Qualification, "نوع الطلب", order.OrderType);
                    AddTableRow(table, "حالة الطلب", order.OrderStatus.ToDisplayString(), "", "");

                    document.Add(table);

                    // Add details section if available
                    if (!string.IsNullOrEmpty(order.Details))
                    {
                        document.Add(new Paragraph("\n"));
                        document.Add(new Paragraph("التفاصيل:").SetBold());
                        document.Add(new Paragraph(order.Details));
                    }

                    // Add approval sections if available
                    if (!string.IsNullOrEmpty(order.ConfirmedByDepartmentManager))
                    {
                        document.Add(new Paragraph("\n"));
                        document.Add(new Paragraph("اعتماد المدير:").SetBold());
                        document.Add(new Paragraph(order.ConfirmedByDepartmentManager));
                    }

                    document.Close();
                    return ServiceResult<byte[]>.Success(stream.ToArray());
                }
            }
            catch (Exception ex)
            {
                return ServiceResult<byte[]>.Failure($"Error generating PDF: {ex.Message}");
            }
        }

        private void AddTableRow(Table table, string label1, string value1, string label2, string value2)
        {
            table.AddCell(new Cell().Add(new Paragraph(label1).SetBold()).SetTextAlignment(TextAlignment.RIGHT));
            table.AddCell(new Cell().Add(new Paragraph(value1 ?? "")).SetTextAlignment(TextAlignment.RIGHT));
            table.AddCell(new Cell().Add(new Paragraph(label2).SetBold()).SetTextAlignment(TextAlignment.RIGHT));
            table.AddCell(new Cell().Add(new Paragraph(value2 ?? "")).SetTextAlignment(TextAlignment.RIGHT));
        }

        #endregion
    }
}