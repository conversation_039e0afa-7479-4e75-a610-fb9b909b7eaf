<?xml version="1.0"?>
<doc>
    <assembly>
        <name>SevenZipSharp</name>
    </assembly>
    <members>
        <member name="T:SevenZip.ArchiveEmulationStreamProxy">
            <summary>
            The Stream extension class to emulate the archive part of a stream.
            </summary>
        </member>
        <member name="M:SevenZip.ArchiveEmulationStreamProxy.#ctor(System.IO.Stream,System.Int32,System.Boolean)">
            <summary>
            Initializes a new instance of the ArchiveEmulationStream class.
            </summary>
            <param name="stream">The stream to wrap.</param>
            <param name="offset">The stream offset.</param>
            <param name="leaveOpen">Whether or not the stream should be closed after operation completes.</param>
        </member>
        <member name="P:SevenZip.ArchiveEmulationStreamProxy.Offset">
            <summary>
            Gets the file offset.
            </summary>
        </member>
        <member name="P:SevenZip.ArchiveEmulationStreamProxy.Source">
            <summary>
            The source wrapped stream.
            </summary>
        </member>
        <member name="P:SevenZip.ArchiveEmulationStreamProxy.CanRead">
            <inheritdoc />
        </member>
        <member name="P:SevenZip.ArchiveEmulationStreamProxy.CanSeek">
            <inheritdoc />
        </member>
        <member name="P:SevenZip.ArchiveEmulationStreamProxy.CanWrite">
            <inheritdoc />
        </member>
        <member name="M:SevenZip.ArchiveEmulationStreamProxy.Flush">
            <inheritdoc />
        </member>
        <member name="P:SevenZip.ArchiveEmulationStreamProxy.Length">
            <inheritdoc />
        </member>
        <member name="P:SevenZip.ArchiveEmulationStreamProxy.Position">
            <inheritdoc />
        </member>
        <member name="M:SevenZip.ArchiveEmulationStreamProxy.Read(System.Byte[],System.Int32,System.Int32)">
            <inheritdoc />
        </member>
        <member name="M:SevenZip.ArchiveEmulationStreamProxy.Seek(System.Int64,System.IO.SeekOrigin)">
            <inheritdoc />
        </member>
        <member name="M:SevenZip.ArchiveEmulationStreamProxy.SetLength(System.Int64)">
            <inheritdoc />
        </member>
        <member name="M:SevenZip.ArchiveEmulationStreamProxy.Write(System.Byte[],System.Int32,System.Int32)">
            <inheritdoc />
        </member>
        <member name="M:SevenZip.ArchiveEmulationStreamProxy.Dispose">
            <inheritdoc />
        </member>
        <member name="M:SevenZip.ArchiveEmulationStreamProxy.Close">
            <inheritdoc />
        </member>
        <member name="T:SevenZip.ArchiveExtractCallback">
            <summary>
            Archive extraction callback to handle the process of unpacking files
            </summary>
        </member>
        <member name="F:SevenZip.ArchiveExtractCallback._bytesCount">
            <summary>
            For Compressing event.
            </summary>
        </member>
        <member name="F:SevenZip.ArchiveExtractCallback._doneRate">
            <summary>
            Rate of the done work from [0, 1].
            </summary>
        </member>
        <member name="M:SevenZip.ArchiveExtractCallback.#ctor(SevenZip.IInArchive,System.String,System.Int32,System.Boolean,System.Collections.Generic.List{System.UInt32},SevenZip.SevenZipExtractor)">
            <summary>
            Initializes a new instance of the ArchiveExtractCallback class
            </summary>
            <param name="archive">IInArchive interface for the archive</param>
            <param name="directory">Directory where files are to be unpacked to</param>
            <param name="filesCount">The archive files count</param>'
            <param name="extractor">The owner of the callback</param>
            <param name="actualIndexes">The list of actual indexes (solid archives support)</param>
            <param name="directoryStructure">The value indicating whether to preserve directory structure of extracted files.</param>
        </member>
        <member name="M:SevenZip.ArchiveExtractCallback.#ctor(SevenZip.IInArchive,System.String,System.Int32,System.Boolean,System.Collections.Generic.List{System.UInt32},System.String,SevenZip.SevenZipExtractor)">
            <summary>
            Initializes a new instance of the ArchiveExtractCallback class
            </summary>
            <param name="archive">IInArchive interface for the archive</param>
            <param name="directory">Directory where files are to be unpacked to</param>
            <param name="filesCount">The archive files count</param>
            <param name="password">Password for the archive</param>
            <param name="extractor">The owner of the callback</param>
            <param name="actualIndexes">The list of actual indexes (solid archives support)</param>
            <param name="directoryStructure">The value indicating whether to preserve directory structure of extracted files.</param>
        </member>
        <member name="M:SevenZip.ArchiveExtractCallback.#ctor(SevenZip.IInArchive,System.IO.Stream,System.Int32,System.UInt32,SevenZip.SevenZipExtractor)">
            <summary>
            Initializes a new instance of the ArchiveExtractCallback class
            </summary>
            <param name="archive">IInArchive interface for the archive</param>
            <param name="stream">The stream where files are to be unpacked to</param>
            <param name="filesCount">The archive files count</param>
            <param name="fileIndex">The file index for the stream</param>
            <param name="extractor">The owner of the callback</param>
        </member>
        <member name="M:SevenZip.ArchiveExtractCallback.#ctor(SevenZip.IInArchive,System.IO.Stream,System.Int32,System.UInt32,System.String,SevenZip.SevenZipExtractor)">
            <summary>
            Initializes a new instance of the ArchiveExtractCallback class
            </summary>
            <param name="archive">IInArchive interface for the archive</param>
            <param name="stream">The stream where files are to be unpacked to</param>
            <param name="filesCount">The archive files count</param>
            <param name="fileIndex">The file index for the stream</param>
            <param name="password">Password for the archive</param>
            <param name="extractor">The owner of the callback</param>
        </member>
        <member name="E:SevenZip.ArchiveExtractCallback.FileExtractionStarted">
            <summary>
            Occurs when a new file is going to be unpacked
            </summary>
            <remarks>Occurs when 7-zip engine requests for an output stream for a new file to unpack in</remarks>
        </member>
        <member name="E:SevenZip.ArchiveExtractCallback.FileExtractionFinished">
            <summary>
            Occurs when a file has been successfully unpacked
            </summary>
        </member>
        <member name="E:SevenZip.ArchiveExtractCallback.Open">
            <summary>
            Occurs when the archive is opened and 7-zip sends the size of unpacked data
            </summary>
        </member>
        <member name="E:SevenZip.ArchiveExtractCallback.Extracting">
            <summary>
            Occurs when the extraction is performed
            </summary>
        </member>
        <member name="E:SevenZip.ArchiveExtractCallback.FileExists">
            <summary>
            Occurs during the extraction when a file already exists
            </summary>
        </member>
        <member name="M:SevenZip.ArchiveExtractCallback.SetTotal(System.UInt64)">
            <summary>
            Gives the size of the unpacked archive files
            </summary>
            <param name="total">Size of the unpacked archive files (in bytes)</param>
        </member>
        <member name="M:SevenZip.ArchiveExtractCallback.GetStream(System.UInt32,SevenZip.ISequentialOutStream@,SevenZip.AskMode)">
            <summary>
            Sets output stream for writing unpacked data
            </summary>
            <param name="index">Current file index</param>
            <param name="outStream">Output stream pointer</param>
            <param name="askExtractMode">Extraction mode</param>
            <returns>0 if OK</returns>
        </member>
        <member name="M:SevenZip.ArchiveExtractCallback.PrepareOperation(SevenZip.AskMode)">
            <inheritdoc />
        </member>
        <member name="M:SevenZip.ArchiveExtractCallback.SetOperationResult(SevenZip.OperationResult)">
            <inheritdoc />
        </member>
        <member name="M:SevenZip.ArchiveExtractCallback.CryptoGetTextPassword(System.String@)">
            <inheritdoc />
        </member>
        <member name="M:SevenZip.ArchiveExtractCallback.Dispose">
            <inheritdoc />
        </member>
        <member name="M:SevenZip.ArchiveExtractCallback.CreateDirectory(System.String)">
            <summary>
            Ensures that the directory to the file name is valid and creates intermediate directories if necessary
            </summary>
            <param name="fileName">File name</param>
        </member>
        <member name="M:SevenZip.ArchiveExtractCallback.RemoveIllegalCharacters(System.String,System.Boolean)">
            <summary>
            removes the invalid character in file path.
            </summary>
            <param name="str"></param>
            <param name="isDirectory"></param>
            <returns></returns>
        </member>
        <member name="T:SevenZip.ArchiveFileInfo">
            <summary>
            Struct for storing information about files in the 7-zip archive.
            </summary>
        </member>
        <member name="P:SevenZip.ArchiveFileInfo.Index">
            <summary>
            Gets or sets index of the file in the archive file table.
            </summary>
        </member>
        <member name="P:SevenZip.ArchiveFileInfo.FileName">
            <summary>
            Gets or sets file name
            </summary>
        </member>
        <member name="P:SevenZip.ArchiveFileInfo.LastWriteTime">
            <summary>
            Gets or sets the file last write time.
            </summary>
        </member>
        <member name="P:SevenZip.ArchiveFileInfo.CreationTime">
            <summary>
            Gets or sets the file creation time.
            </summary>
        </member>
        <member name="P:SevenZip.ArchiveFileInfo.LastAccessTime">
            <summary>
            Gets or sets the file creation time.
            </summary>
        </member>
        <member name="P:SevenZip.ArchiveFileInfo.Size">
            <summary>
            Gets or sets size of the file (unpacked).
            </summary>
        </member>
        <member name="P:SevenZip.ArchiveFileInfo.Crc">
            <summary>
            Gets or sets CRC checksum of the file.
            </summary>
        </member>
        <member name="P:SevenZip.ArchiveFileInfo.Attributes">
            <summary>
            Gets or sets file attributes.
            </summary>
        </member>
        <member name="P:SevenZip.ArchiveFileInfo.IsDirectory">
            <summary>
            Gets or sets being a directory.
            </summary>
        </member>
        <member name="P:SevenZip.ArchiveFileInfo.Encrypted">
            <summary>
            Gets or sets being encrypted.
            </summary>
        </member>
        <member name="P:SevenZip.ArchiveFileInfo.Comment">
            <summary>
            Gets or sets comment for the file.
            </summary>
        </member>
        <member name="P:SevenZip.ArchiveFileInfo.Method">
            <summary>
            Compression method for the file.
            </summary>
        </member>
        <member name="M:SevenZip.ArchiveFileInfo.Equals(System.Object)">
            <summary>
            Determines whether the specified System.Object is equal to the current ArchiveFileInfo.
            </summary>
            <param name="obj">The System.Object to compare with the current ArchiveFileInfo.</param>
            <returns>true if the specified System.Object is equal to the current ArchiveFileInfo; otherwise, false.</returns>
        </member>
        <member name="M:SevenZip.ArchiveFileInfo.Equals(SevenZip.ArchiveFileInfo)">
            <summary>
            Determines whether the specified ArchiveFileInfo is equal to the current ArchiveFileInfo.
            </summary>
            <param name="afi">The ArchiveFileInfo to compare with the current ArchiveFileInfo.</param>
            <returns>true if the specified ArchiveFileInfo is equal to the current ArchiveFileInfo; otherwise, false.</returns>
        </member>
        <member name="M:SevenZip.ArchiveFileInfo.GetHashCode">
            <summary>
            Serves as a hash function for a particular type.
            </summary>
            <returns> A hash code for the current ArchiveFileInfo.</returns>
        </member>
        <member name="M:SevenZip.ArchiveFileInfo.ToString">
            <summary>
            Returns a System.String that represents the current ArchiveFileInfo.
            </summary>
            <returns>A System.String that represents the current ArchiveFileInfo.</returns>
        </member>
        <member name="M:SevenZip.ArchiveFileInfo.op_Equality(SevenZip.ArchiveFileInfo,SevenZip.ArchiveFileInfo)">
            <summary>
            Determines whether the specified ArchiveFileInfo instances are considered equal.
            </summary>
            <param name="afi1">The first ArchiveFileInfo to compare.</param>
            <param name="afi2">The second ArchiveFileInfo to compare.</param>
            <returns>true if the specified ArchiveFileInfo instances are considered equal; otherwise, false.</returns>
        </member>
        <member name="M:SevenZip.ArchiveFileInfo.op_Inequality(SevenZip.ArchiveFileInfo,SevenZip.ArchiveFileInfo)">
            <summary>
            Determines whether the specified ArchiveFileInfo instances are not considered equal.
            </summary>
            <param name="afi1">The first ArchiveFileInfo to compare.</param>
            <param name="afi2">The second ArchiveFileInfo to compare.</param>
            <returns>true if the specified ArchiveFileInfo instances are not considered equal; otherwise, false.</returns>
        </member>
        <member name="T:SevenZip.ArchiveOpenCallback">
            <summary>
            Callback to handle the archive opening
            </summary>
        </member>
        <member name="P:SevenZip.ArchiveOpenCallback.VolumeFileNames">
            <summary>
            Gets the list of volume file names.
            </summary>
        </member>
        <member name="M:SevenZip.ArchiveOpenCallback.Init(System.String)">
            <summary>
            Performs the common initialization.
            </summary>
            <param name="fileName">Volume file name.</param>
        </member>
        <member name="M:SevenZip.ArchiveOpenCallback.#ctor(System.String)">
            <summary>
            Initializes a new instance of the ArchiveOpenCallback class.
            </summary>
            <param name="fileName">The archive file name.</param>
        </member>
        <member name="M:SevenZip.ArchiveOpenCallback.#ctor(System.String,System.String)">
            <summary>
            Initializes a new instance of the ArchiveOpenCallback class.
            </summary>
            <param name="fileName">The archive file name.</param>
            <param name="password">Password for the archive.</param>
        </member>
        <member name="M:SevenZip.ArchiveOpenCallback.CryptoGetTextPassword(System.String@)">
            <summary>
            Sets password for the archive
            </summary>
            <param name="password">Password for the archive</param>
            <returns>Zero if everything is OK</returns>
        </member>
        <member name="T:SevenZip.ArchiveProperty">
            <summary>
            Archive property struct.
            </summary>
        </member>
        <member name="P:SevenZip.ArchiveProperty.Name">
            <summary>
            Gets the name of the archive property.
            </summary>
        </member>
        <member name="P:SevenZip.ArchiveProperty.Value">
            <summary>
            Gets the value of the archive property.
            </summary>
        </member>
        <member name="M:SevenZip.ArchiveProperty.Equals(System.Object)">
            <summary>
            Determines whether the specified System.Object is equal to the current ArchiveProperty.
            </summary>
            <param name="obj">The System.Object to compare with the current ArchiveProperty.</param>
            <returns>true if the specified System.Object is equal to the current ArchiveProperty; otherwise, false.</returns>
        </member>
        <member name="M:SevenZip.ArchiveProperty.Equals(SevenZip.ArchiveProperty)">
            <summary>
            Determines whether the specified ArchiveProperty is equal to the current ArchiveProperty.
            </summary>
            <param name="afi">The ArchiveProperty to compare with the current ArchiveProperty.</param>
            <returns>true if the specified ArchiveProperty is equal to the current ArchiveProperty; otherwise, false.</returns>
        </member>
        <member name="M:SevenZip.ArchiveProperty.GetHashCode">
            <summary>
             Serves as a hash function for a particular type.
            </summary>
            <returns> A hash code for the current ArchiveProperty.</returns>
        </member>
        <member name="M:SevenZip.ArchiveProperty.ToString">
            <summary>
            Returns a System.String that represents the current ArchiveProperty.
            </summary>
            <returns>A System.String that represents the current ArchiveProperty.</returns>
        </member>
        <member name="M:SevenZip.ArchiveProperty.op_Equality(SevenZip.ArchiveProperty,SevenZip.ArchiveProperty)">
            <summary>
            Determines whether the specified ArchiveProperty instances are considered equal.
            </summary>
            <param name="afi1">The first ArchiveProperty to compare.</param>
            <param name="afi2">The second ArchiveProperty to compare.</param>
            <returns>true if the specified ArchiveProperty instances are considered equal; otherwise, false.</returns>
        </member>
        <member name="M:SevenZip.ArchiveProperty.op_Inequality(SevenZip.ArchiveProperty,SevenZip.ArchiveProperty)">
            <summary>
            Determines whether the specified ArchiveProperty instances are not considered equal.
            </summary>
            <param name="afi1">The first ArchiveProperty to compare.</param>
            <param name="afi2">The second ArchiveProperty to compare.</param>
            <returns>true if the specified ArchiveProperty instances are not considered equal; otherwise, false.</returns>
        </member>
        <member name="T:SevenZip.ArchiveUpdateCallback">
            <summary>
            Archive update callback to handle the process of packing files
            </summary>
        </member>
        <member name="F:SevenZip.ArchiveUpdateCallback._actualFilesCount">
            <summary>
            _files.Count if do not count directories
            </summary>
        </member>
        <member name="F:SevenZip.ArchiveUpdateCallback._bytesCount">
            <summary>
            For Compressing event.
            </summary>
        </member>
        <member name="F:SevenZip.ArchiveUpdateCallback._directoryStructure">
            <summary>
            No directories.
            </summary>
        </member>
        <member name="F:SevenZip.ArchiveUpdateCallback._doneRate">
            <summary>
            Rate of the done work from [0, 1]
            </summary>
        </member>
        <member name="F:SevenZip.ArchiveUpdateCallback._entries">
            <summary>
            The names of the archive entries
            </summary>
        </member>
        <member name="F:SevenZip.ArchiveUpdateCallback._files">
            <summary>
            Array of files to pack
            </summary>
        </member>
        <member name="F:SevenZip.ArchiveUpdateCallback._rootLength">
            <summary>
            Common root of file names length.
            </summary>
        </member>
        <member name="F:SevenZip.ArchiveUpdateCallback._streams">
            <summary>
            Input streams to be compressed.
            </summary>
        </member>
        <member name="P:SevenZip.ArchiveUpdateCallback.DefaultItemName">
            <summary>
            Gets or sets the default item name used in MemoryStream compression.
            </summary>
        </member>
        <member name="P:SevenZip.ArchiveUpdateCallback.FastCompression">
            <summary>
            Gets or sets the value indicating whether to compress as fast as possible, without calling events.
            </summary>
        </member>
        <member name="M:SevenZip.ArchiveUpdateCallback.#ctor(System.IO.FileInfo[],System.Int32,SevenZip.SevenZipCompressor,SevenZip.UpdateData,System.Boolean)">
            <summary>
            Initializes a new instance of the ArchiveUpdateCallback class
            </summary>
            <param name="files">Array of files to pack</param>
            <param name="rootLength">Common file names root length</param>
            <param name="compressor">The owner of the callback</param>
            <param name="updateData">The compression parameters.</param>
            <param name="directoryStructure">Preserve directory structure.</param>
        </member>
        <member name="M:SevenZip.ArchiveUpdateCallback.#ctor(System.IO.FileInfo[],System.Int32,System.String,SevenZip.SevenZipCompressor,SevenZip.UpdateData,System.Boolean)">
            <summary>
            Initializes a new instance of the ArchiveUpdateCallback class
            </summary>
            <param name="files">Array of files to pack</param>
            <param name="rootLength">Common file names root length</param>
            <param name="password">The archive password</param>
            <param name="compressor">The owner of the callback</param>
            <param name="updateData">The compression parameters.</param>
            <param name="directoryStructure">Preserve directory structure.</param>
        </member>
        <member name="M:SevenZip.ArchiveUpdateCallback.#ctor(System.IO.Stream,SevenZip.SevenZipCompressor,SevenZip.UpdateData,System.Boolean)">
            <summary>
            Initializes a new instance of the ArchiveUpdateCallback class
            </summary>
            <param name="stream">The input stream</param>
            <param name="compressor">The owner of the callback</param>
            <param name="updateData">The compression parameters.</param>
            <param name="directoryStructure">Preserve directory structure.</param>
        </member>
        <member name="M:SevenZip.ArchiveUpdateCallback.#ctor(System.IO.Stream,System.String,SevenZip.SevenZipCompressor,SevenZip.UpdateData,System.Boolean)">
            <summary>
            Initializes a new instance of the ArchiveUpdateCallback class
            </summary>
            <param name="stream">The input stream</param>
            <param name="password">The archive password</param>
            <param name="compressor">The owner of the callback</param>
            <param name="updateData">The compression parameters.</param>
            <param name="directoryStructure">Preserve directory structure.</param>
        </member>
        <member name="M:SevenZip.ArchiveUpdateCallback.#ctor(System.Collections.Generic.IDictionary{System.String,System.IO.Stream},SevenZip.SevenZipCompressor,SevenZip.UpdateData,System.Boolean)">
            <summary>
            Initializes a new instance of the ArchiveUpdateCallback class
            </summary>
            <param name="streamDict">Dictionary&lt;file stream, name of the archive entry&gt;</param>
            <param name="compressor">The owner of the callback</param>
            <param name="updateData">The compression parameters.</param>
            <param name="directoryStructure">Preserve directory structure.</param>
        </member>
        <member name="M:SevenZip.ArchiveUpdateCallback.#ctor(System.Collections.Generic.IDictionary{System.String,System.IO.Stream},System.String,SevenZip.SevenZipCompressor,SevenZip.UpdateData,System.Boolean)">
            <summary>
            Initializes a new instance of the ArchiveUpdateCallback class
            </summary>
            <param name="streamDict">Dictionary&lt;file stream, name of the archive entry&gt;</param>
            <param name="password">The archive password</param>
            <param name="compressor">The owner of the callback</param>
            <param name="updateData">The compression parameters.</param>
            <param name="directoryStructure">Preserve directory structure.</param>
        </member>
        <member name="P:SevenZip.ArchiveUpdateCallback.DictionarySize">
            <summary>
            Gets or sets the dictionary size.
            </summary>
        </member>
        <member name="M:SevenZip.ArchiveUpdateCallback.EventsForGetStream(System.UInt32)">
            <summary>
            Raises events for the GetStream method.
            </summary>
            <param name="index">The current item index.</param>
            <returns>True if not cancelled; otherwise, false.</returns>
        </member>
        <member name="E:SevenZip.ArchiveUpdateCallback.FileCompressionStarted">
            <summary>
            Occurs when the next file is going to be packed.
            </summary>
            <remarks>Occurs when 7-zip engine requests for an input stream for the next file to pack it</remarks>
        </member>
        <member name="E:SevenZip.ArchiveUpdateCallback.Compressing">
            <summary>
            Occurs when data are being compressed.
            </summary>
        </member>
        <member name="E:SevenZip.ArchiveUpdateCallback.FileCompressionFinished">
            <summary>
            Occurs when the current file was compressed.
            </summary>
        </member>
        <member name="M:SevenZip.ArchiveUpdateCallback.GetStream(System.UInt32,SevenZip.ISequentialInStream@)">
            <summary>
            Gets the stream for 7-zip library.
            </summary>
            <param name="index">File index</param>
            <param name="inStream">Input file stream</param>
            <returns>Zero if Ok</returns>
        </member>
        <member name="F:SevenZip.CallbackBase._exceptions">
            <summary>
            User exceptions thrown during the requested operations, for example, in events.
            </summary>
        </member>
        <member name="M:SevenZip.CallbackBase.#ctor">
            <summary>
            Initializes a new instance of the CallbackBase class.
            </summary>
        </member>
        <member name="M:SevenZip.CallbackBase.#ctor(System.String)">
            <summary>
            Initializes a new instance of the CallbackBase class.
            </summary>
            <param name="password">The archive password.</param>
        </member>
        <member name="P:SevenZip.CallbackBase.Password">
            <summary>
            Gets or sets the archive password
            </summary>
        </member>
        <member name="P:SevenZip.CallbackBase.Canceled">
            <summary>
            Gets or sets the value indicating whether the current procedure was cancelled.
            </summary>
        </member>
        <member name="P:SevenZip.CallbackBase.ReportErrors">
            <summary>
            Gets or sets throw exceptions on archive errors flag
            </summary>
        </member>
        <member name="P:SevenZip.CallbackBase.Exceptions">
            <summary>
            Gets the user exceptions thrown during the requested operations, for example, in events.
            </summary>
        </member>
        <member name="M:SevenZip.CallbackBase.ThrowException(SevenZip.CallbackBase,System.Exception[])">
            <summary>
            Throws the specified exception when is able to.
            </summary>
            <param name="e">The exception to throw.</param>
            <param name="handler">The handler responsible for the exception.</param>
        </member>
        <member name="M:SevenZip.CallbackBase.ThrowException">
            <summary>
            Throws the first exception in the list if any exists.
            </summary>
            <returns>True means no exceptions.</returns>
        </member>
        <member name="T:SevenZip.PropArray">
            <summary>
            The structure to fix x64 and x32 variant size mismatch.
            </summary>
        </member>
        <member name="T:SevenZip.PropVariant">
            <summary>
            COM VARIANT structure with special interface routines.
            </summary>
        </member>
        <member name="F:SevenZip.PropVariant._fileTime">
            <summary>
            FILETIME variant value.
            </summary>
        </member>
        <member name="F:SevenZip.PropVariant._propArray">
            <summary>
            The PropArray instance to fix the variant size on x64 bit systems.
            </summary>
        </member>
        <member name="P:SevenZip.PropVariant.VarType">
            <summary>
            Gets or sets variant type.
            </summary>
        </member>
        <member name="P:SevenZip.PropVariant.Value">
            <summary>
            Gets or sets the pointer value of the COM variant
            </summary>
        </member>
        <member name="P:SevenZip.PropVariant.UInt32Value">
            <summary>
            Gets or sets the UInt32 value of the COM variant.
            </summary>
        </member>
        <member name="P:SevenZip.PropVariant.Int32Value">
            <summary>
            Gets or sets the UInt32 value of the COM variant.
            </summary>
        </member>
        <member name="P:SevenZip.PropVariant.Int64Value">
            <summary>
            Gets or sets the Int64 value of the COM variant
            </summary>
        </member>
        <member name="P:SevenZip.PropVariant.UInt64Value">
            <summary>
            Gets or sets the UInt64 value of the COM variant
            </summary>
        </member>
        <member name="P:SevenZip.PropVariant.Object">
            <summary>
            Gets the object for this PropVariant.
            </summary>
            <returns></returns>
        </member>
        <member name="M:SevenZip.PropVariant.Equals(System.Object)">
            <summary>
            Determines whether the specified System.Object is equal to the current PropVariant.
            </summary>
            <param name="obj">The System.Object to compare with the current PropVariant.</param>
            <returns>true if the specified System.Object is equal to the current PropVariant; otherwise, false.</returns>
        </member>
        <member name="M:SevenZip.PropVariant.Equals(SevenZip.PropVariant)">
            <summary>
            Determines whether the specified PropVariant is equal to the current PropVariant.
            </summary>
            <param name="afi">The PropVariant to compare with the current PropVariant.</param>
            <returns>true if the specified PropVariant is equal to the current PropVariant; otherwise, false.</returns>
        </member>
        <member name="M:SevenZip.PropVariant.GetHashCode">
            <summary>
             Serves as a hash function for a particular type.
            </summary>
            <returns> A hash code for the current PropVariant.</returns>
        </member>
        <member name="M:SevenZip.PropVariant.ToString">
            <summary>
            Returns a System.String that represents the current PropVariant.
            </summary>
            <returns>A System.String that represents the current PropVariant.</returns>
        </member>
        <member name="M:SevenZip.PropVariant.op_Equality(SevenZip.PropVariant,SevenZip.PropVariant)">
            <summary>
            Determines whether the specified PropVariant instances are considered equal.
            </summary>
            <param name="afi1">The first PropVariant to compare.</param>
            <param name="afi2">The second PropVariant to compare.</param>
            <returns>true if the specified PropVariant instances are considered equal; otherwise, false.</returns>
        </member>
        <member name="M:SevenZip.PropVariant.op_Inequality(SevenZip.PropVariant,SevenZip.PropVariant)">
            <summary>
            Determines whether the specified PropVariant instances are not considered equal.
            </summary>
            <param name="afi1">The first PropVariant to compare.</param>
            <param name="afi2">The second PropVariant to compare.</param>
            <returns>true if the specified PropVariant instances are not considered equal; otherwise, false.</returns>
        </member>
        <member name="T:SevenZip.AskMode">
            <summary>
            Stores file extraction modes.
            </summary>
        </member>
        <member name="F:SevenZip.AskMode.Extract">
            <summary>
            Extraction mode
            </summary>
        </member>
        <member name="F:SevenZip.AskMode.Test">
            <summary>
            Test mode
            </summary>
        </member>
        <member name="F:SevenZip.AskMode.Skip">
            <summary>
            Skip mode
            </summary>
        </member>
        <member name="T:SevenZip.OperationResult">
            <summary>
            Stores operation result values
            </summary>
        </member>
        <member name="F:SevenZip.OperationResult.Ok">
            <summary>
            Success
            </summary>
        </member>
        <member name="F:SevenZip.OperationResult.UnsupportedMethod">
            <summary>
            Method is unsupported
            </summary>
        </member>
        <member name="F:SevenZip.OperationResult.DataError">
            <summary>
            Data error has occurred
            </summary>
        </member>
        <member name="F:SevenZip.OperationResult.CrcError">
            <summary>
            CrcError has occurred
            </summary>
        </member>
        <member name="F:SevenZip.OperationResult.Unavailable">
            <summary>
            File is unavailable
            </summary>
        </member>
        <member name="F:SevenZip.OperationResult.UnexpectedEnd">
            <summary>
            Unexpected end of file
            </summary>
        </member>
        <member name="F:SevenZip.OperationResult.DataAfterEnd">
            <summary>
            Data after end of archive
            </summary>
        </member>
        <member name="F:SevenZip.OperationResult.IsNotArc">
            <summary>
            File is not archive
            </summary>
        </member>
        <member name="F:SevenZip.OperationResult.HeadersError">
            <summary>
            Archive headers error
            </summary>
        </member>
        <member name="F:SevenZip.OperationResult.WrongPassword">
            <summary>
            Wrong password
            </summary>
        </member>
        <member name="T:SevenZip.ItemPropId">
            <summary>
            Codes of item properties
            </summary>
        </member>
        <member name="F:SevenZip.ItemPropId.NoProperty">
            <summary>
            No property
            </summary>
        </member>
        <member name="F:SevenZip.ItemPropId.HandlerItemIndex">
            <summary>
            Handler item index
            </summary>
        </member>
        <member name="F:SevenZip.ItemPropId.Path">
            <summary>
            Item path
            </summary>
        </member>
        <member name="F:SevenZip.ItemPropId.Name">
            <summary>
            Item name
            </summary>
        </member>
        <member name="F:SevenZip.ItemPropId.Extension">
            <summary>
            Item extension
            </summary>
        </member>
        <member name="F:SevenZip.ItemPropId.IsDirectory">
            <summary>
            true if the item is a folder; otherwise, false
            </summary>
        </member>
        <member name="F:SevenZip.ItemPropId.Size">
            <summary>
            Item size
            </summary>
        </member>
        <member name="F:SevenZip.ItemPropId.PackedSize">
            <summary>
            Item packed sise; usually absent
            </summary>
        </member>
        <member name="F:SevenZip.ItemPropId.Attributes">
            <summary>
            Item attributes; usually absent
            </summary>
        </member>
        <member name="F:SevenZip.ItemPropId.CreationTime">
            <summary>
            Item creation time; usually absent
            </summary>
        </member>
        <member name="F:SevenZip.ItemPropId.LastAccessTime">
            <summary>
            Item last access time; usually absent
            </summary>
        </member>
        <member name="F:SevenZip.ItemPropId.LastWriteTime">
            <summary>
            Item last write time
            </summary>
        </member>
        <member name="F:SevenZip.ItemPropId.Solid">
            <summary>
            true if the item is solid; otherwise, false
            </summary>
        </member>
        <member name="F:SevenZip.ItemPropId.Commented">
            <summary>
            true if the item is commented; otherwise, false
            </summary>
        </member>
        <member name="F:SevenZip.ItemPropId.Encrypted">
            <summary>
            true if the item is encrypted; otherwise, false
            </summary>
        </member>
        <member name="F:SevenZip.ItemPropId.SplitBefore">
            <summary>
            (?)
            </summary>
        </member>
        <member name="F:SevenZip.ItemPropId.SplitAfter">
            <summary>
            (?)
            </summary>
        </member>
        <member name="F:SevenZip.ItemPropId.DictionarySize">
            <summary>
            Dictionary size(?)
            </summary>
        </member>
        <member name="F:SevenZip.ItemPropId.Crc">
            <summary>
            Item CRC checksum
            </summary>
        </member>
        <member name="F:SevenZip.ItemPropId.Type">
            <summary>
            Item type(?)
            </summary>
        </member>
        <member name="F:SevenZip.ItemPropId.IsAnti">
            <summary>
            (?)
            </summary>
        </member>
        <member name="F:SevenZip.ItemPropId.Method">
            <summary>
            Compression method
            </summary>
        </member>
        <member name="F:SevenZip.ItemPropId.HostOS">
            <summary>
            (?); usually absent
            </summary>
        </member>
        <member name="F:SevenZip.ItemPropId.FileSystem">
            <summary>
            Item file system; usually absent
            </summary>
        </member>
        <member name="F:SevenZip.ItemPropId.User">
            <summary>
            Item user(?); usually absent
            </summary>
        </member>
        <member name="F:SevenZip.ItemPropId.Group">
            <summary>
            Item group(?); usually absent
            </summary>
        </member>
        <member name="F:SevenZip.ItemPropId.Block">
            <summary>
            Bloack size(?)
            </summary>
        </member>
        <member name="F:SevenZip.ItemPropId.Comment">
            <summary>
            Item comment; usually absent
            </summary>
        </member>
        <member name="F:SevenZip.ItemPropId.Position">
            <summary>
            Item position
            </summary>
        </member>
        <member name="F:SevenZip.ItemPropId.Prefix">
            <summary>
            Item prefix(?)
            </summary>
        </member>
        <member name="F:SevenZip.ItemPropId.NumSubDirs">
            <summary>
            Number of subdirectories
            </summary>
        </member>
        <member name="F:SevenZip.ItemPropId.NumSubFiles">
            <summary>
            Numbers of subfiles
            </summary>
        </member>
        <member name="F:SevenZip.ItemPropId.UnpackVersion">
            <summary>
            The archive legacy unpacker version
            </summary>
        </member>
        <member name="F:SevenZip.ItemPropId.Volume">
            <summary>
            Volume(?)
            </summary>
        </member>
        <member name="F:SevenZip.ItemPropId.IsVolume">
            <summary>
            Is a volume
            </summary>
        </member>
        <member name="F:SevenZip.ItemPropId.Offset">
            <summary>
            Offset value(?)
            </summary>
        </member>
        <member name="F:SevenZip.ItemPropId.Links">
            <summary>
            Links(?)
            </summary>
        </member>
        <member name="F:SevenZip.ItemPropId.NumBlocks">
            <summary>
            Number of blocks
            </summary>
        </member>
        <member name="F:SevenZip.ItemPropId.NumVolumes">
            <summary>
            Number of volumes(?)
            </summary>
        </member>
        <member name="F:SevenZip.ItemPropId.TimeType">
            <summary>
            Time type(?)
            </summary>
        </member>
        <member name="F:SevenZip.ItemPropId.Bit64">
            <summary>
            64-bit(?)
            </summary>
        </member>
        <member name="F:SevenZip.ItemPropId.BigEndian">
            <summary>
            BigEndian
            </summary>
        </member>
        <member name="F:SevenZip.ItemPropId.Cpu">
            <summary>
            Cpu(?)
            </summary>
        </member>
        <member name="F:SevenZip.ItemPropId.PhysicalSize">
            <summary>
            Physical archive size
            </summary>
        </member>
        <member name="F:SevenZip.ItemPropId.HeadersSize">
            <summary>
            Headers size
            </summary>
        </member>
        <member name="F:SevenZip.ItemPropId.Checksum">
            <summary>
            Archive checksum
            </summary>
        </member>
        <member name="F:SevenZip.ItemPropId.TotalSize">
            <summary>
            (?)
            </summary>
        </member>
        <member name="F:SevenZip.ItemPropId.FreeSpace">
            <summary>
            (?)
            </summary>
        </member>
        <member name="F:SevenZip.ItemPropId.ClusterSize">
            <summary>
            Cluster size(?)
            </summary>
        </member>
        <member name="F:SevenZip.ItemPropId.VolumeName">
            <summary>
            Volume name(?)
            </summary>
        </member>
        <member name="F:SevenZip.ItemPropId.LocalName">
            <summary>
            Local item name(?); usually absent
            </summary>
        </member>
        <member name="F:SevenZip.ItemPropId.Provider">
            <summary>
            (?)
            </summary>
        </member>
        <member name="F:SevenZip.ItemPropId.VolumeIndex">
            <summary>
            Index of the Volume
            </summary>
        </member>
        <member name="F:SevenZip.ItemPropId.UserDefined">
            <summary>
            User defined property; usually absent
            </summary>
        </member>
        <member name="T:SevenZip.PropIdToName">
            <summary>
            PropId string names dictionary wrapper.
            </summary>
        </member>
        <member name="F:SevenZip.PropIdToName.PropIdNames">
            <summary>
            PropId string names
            </summary>
        </member>
        <member name="T:SevenZip.IArchiveOpenCallback">
            <summary>
            7-zip IArchiveOpenCallback imported interface to handle the opening of an archive.
            </summary>
        </member>
        <member name="M:SevenZip.IArchiveOpenCallback.SetTotal(System.IntPtr,System.IntPtr)">
            <summary>
            Sets total data size
            </summary>
            <param name="files">Files pointer</param>
            <param name="bytes">Total size in bytes</param>
        </member>
        <member name="M:SevenZip.IArchiveOpenCallback.SetCompleted(System.IntPtr,System.IntPtr)">
            <summary>
            Sets completed size
            </summary>
            <param name="files">Files pointer</param>
            <param name="bytes">Completed size in bytes</param>
        </member>
        <member name="T:SevenZip.ICryptoGetTextPassword">
            <summary>
            7-zip ICryptoGetTextPassword imported interface to get the archive password.
            </summary>
        </member>
        <member name="M:SevenZip.ICryptoGetTextPassword.CryptoGetTextPassword(System.String@)">
            <summary>
            Gets password for the archive
            </summary>
            <param name="password">Password for the archive</param>
            <returns>Zero if everything is OK</returns>
        </member>
        <member name="T:SevenZip.ICryptoGetTextPassword2">
            <summary>
            7-zip ICryptoGetTextPassword2 imported interface for setting the archive password.
            </summary>
        </member>
        <member name="M:SevenZip.ICryptoGetTextPassword2.CryptoGetTextPassword2(System.Int32@,System.String@)">
            <summary>
            Sets password for the archive
            </summary>
            <param name="passwordIsDefined">Specifies whether archive has a password or not (0 if not)</param>
            <param name="password">Password for the archive</param>
            <returns>Zero if everything is OK</returns>
        </member>
        <member name="T:SevenZip.IArchiveExtractCallback">
            <summary>
            7-zip IArchiveExtractCallback imported interface.
            </summary>
        </member>
        <member name="M:SevenZip.IArchiveExtractCallback.SetTotal(System.UInt64)">
            <summary>
            Gives the size of the unpacked archive files
            </summary>
            <param name="total">Size of the unpacked archive files (in bytes)</param>
        </member>
        <member name="M:SevenZip.IArchiveExtractCallback.SetCompleted(System.UInt64@)">
            <summary>
            SetCompleted 7-zip function
            </summary>
            <param name="completeValue"></param>
        </member>
        <member name="M:SevenZip.IArchiveExtractCallback.GetStream(System.UInt32,SevenZip.ISequentialOutStream@,SevenZip.AskMode)">
            <summary>
            Gets the stream for file extraction
            </summary>
            <param name="index">File index in the archive file table</param>
            <param name="outStream">Pointer to the stream</param>
            <param name="askExtractMode">Extraction mode</param>
            <returns>S_OK - OK, S_FALSE - skip this file</returns>
        </member>
        <member name="M:SevenZip.IArchiveExtractCallback.PrepareOperation(SevenZip.AskMode)">
            <summary>
            PrepareOperation 7-zip function
            </summary>
            <param name="askExtractMode">Ask mode</param>
        </member>
        <member name="M:SevenZip.IArchiveExtractCallback.SetOperationResult(SevenZip.OperationResult)">
            <summary>
            Sets the operation result
            </summary>
            <param name="operationResult">The operation result</param>
        </member>
        <member name="T:SevenZip.IArchiveUpdateCallback">
            <summary>
            7-zip IArchiveUpdateCallback imported interface.
            </summary>
        </member>
        <member name="M:SevenZip.IArchiveUpdateCallback.SetTotal(System.UInt64)">
            <summary>
            Gives the size of the unpacked archive files.
            </summary>
            <param name="total">Size of the unpacked archive files (in bytes)</param>
        </member>
        <member name="M:SevenZip.IArchiveUpdateCallback.SetCompleted(System.UInt64@)">
            <summary>
            SetCompleted 7-zip internal function.
            </summary>
            <param name="completeValue"></param>
        </member>
        <member name="M:SevenZip.IArchiveUpdateCallback.GetUpdateItemInfo(System.UInt32,System.Int32@,System.Int32@,System.UInt32@)">
            <summary>
            Gets archive update mode.
            </summary>
            <param name="index">File index</param>
            <param name="newData">1 if new, 0 if not</param>
            <param name="newProperties">1 if new, 0 if not</param>
            <param name="indexInArchive">-1 if doesn't matter</param>
            <returns></returns>
        </member>
        <member name="M:SevenZip.IArchiveUpdateCallback.GetProperty(System.UInt32,SevenZip.ItemPropId,SevenZip.PropVariant@)">
            <summary>
            Gets the archive item property data.
            </summary>
            <param name="index">Item index</param>
            <param name="propId">Property identifier</param>
            <param name="value">Property value</param>
            <returns>Zero if Ok</returns>
        </member>
        <member name="M:SevenZip.IArchiveUpdateCallback.GetStream(System.UInt32,SevenZip.ISequentialInStream@)">
            <summary>
            Gets the stream for reading.
            </summary>
            <param name="index">The item index.</param>
            <param name="inStream">The ISequentialInStream pointer for reading.</param>
            <returns>Zero if Ok</returns>
        </member>
        <member name="M:SevenZip.IArchiveUpdateCallback.SetOperationResult(SevenZip.OperationResult)">
            <summary>
            Sets the result for currently performed operation.
            </summary>
            <param name="operationResult">The result value.</param>
        </member>
        <member name="M:SevenZip.IArchiveUpdateCallback.EnumProperties(System.IntPtr)">
            <summary>
            EnumProperties 7-zip internal function.
            </summary>
            <param name="enumerator">The enumerator pointer.</param>
            <returns></returns>
        </member>
        <member name="T:SevenZip.IArchiveOpenVolumeCallback">
            <summary>
            7-zip IArchiveOpenVolumeCallback imported interface to handle archive volumes.
            </summary>
        </member>
        <member name="M:SevenZip.IArchiveOpenVolumeCallback.GetProperty(SevenZip.ItemPropId,SevenZip.PropVariant@)">
            <summary>
            Gets the archive property data.
            </summary>
            <param name="propId">The property identificator.</param>
            <param name="value">The property value.</param>
        </member>
        <member name="M:SevenZip.IArchiveOpenVolumeCallback.GetStream(System.String,SevenZip.IInStream@)">
            <summary>
            Gets the stream for reading the volume.
            </summary>
            <param name="name">The volume file name.</param>
            <param name="inStream">The IInStream pointer for reading.</param>
            <returns>Zero if Ok</returns>
        </member>
        <member name="T:SevenZip.ISequentialInStream">
            <summary>
            7-zip ISequentialInStream imported interface
            </summary>
        </member>
        <member name="M:SevenZip.ISequentialInStream.Read(System.Byte[],System.UInt32)">
            <summary>
            Writes data to 7-zip packer
            </summary>
            <param name="data">Array of bytes available for writing</param>
            <param name="size">Array size</param>
            <returns>S_OK if success</returns>
            <remarks>If (size > 0) and there are bytes in stream, 
            this function must read at least 1 byte.
            This function is allowed to read less than "size" bytes.
            You must call Read function in loop, if you need exact amount of data.
            </remarks>
        </member>
        <member name="T:SevenZip.ISequentialOutStream">
            <summary>
            7-zip ISequentialOutStream imported interface
            </summary>
        </member>
        <member name="M:SevenZip.ISequentialOutStream.Write(System.Byte[],System.UInt32,System.IntPtr)">
            <summary>
            Writes data to unpacked file stream
            </summary>
            <param name="data">Array of bytes available for reading</param>
            <param name="size">Array size</param>
            <param name="processedSize">Processed data size</param>
            <returns>S_OK if success</returns>
            <remarks>If size != 0, return value is S_OK and (*processedSize == 0),
             then there are no more bytes in stream.
            If (size > 0) and there are bytes in stream, 
            this function must read at least 1 byte.
            This function is allowed to rwrite less than "size" bytes.
            You must call Write function in loop, if you need exact amount of data.
            </remarks>
        </member>
        <member name="T:SevenZip.IInStream">
            <summary>
            7-zip IInStream imported interface
            </summary>
        </member>
        <member name="M:SevenZip.IInStream.Read(System.Byte[],System.UInt32)">
            <summary>
            Read routine
            </summary>
            <param name="data">Array of bytes to set</param>
            <param name="size">Array size</param>
            <returns>Zero if Ok</returns>
        </member>
        <member name="M:SevenZip.IInStream.Seek(System.Int64,System.IO.SeekOrigin,System.IntPtr)">
            <summary>
            Seek routine
            </summary>
            <param name="offset">Offset value</param>
            <param name="seekOrigin">Seek origin value</param>
            <param name="newPosition">New position pointer</param>
        </member>
        <member name="T:SevenZip.IOutStream">
            <summary>
            7-zip IOutStream imported interface
            </summary>
        </member>
        <member name="M:SevenZip.IOutStream.Write(System.Byte[],System.UInt32,System.IntPtr)">
            <summary>
            Write routine
            </summary>
            <param name="data">Array of bytes to get</param>
            <param name="size">Array size</param>
            <param name="processedSize">Processed size</param>
            <returns>Zero if Ok</returns>
        </member>
        <member name="M:SevenZip.IOutStream.Seek(System.Int64,System.IO.SeekOrigin,System.IntPtr)">
            <summary>
            Seek routine
            </summary>
            <param name="offset">Offset value</param>
            <param name="seekOrigin">Seek origin value</param>
            <param name="newPosition">New position pointer</param>       
        </member>
        <member name="M:SevenZip.IOutStream.SetSize(System.Int64)">
            <summary>
            Set size routine
            </summary>
            <param name="newSize">New size value</param>
            <returns>Zero if Ok</returns>
        </member>
        <member name="T:SevenZip.IInArchive">
            <summary>
            7-zip essential in archive interface
            </summary>
        </member>
        <member name="M:SevenZip.IInArchive.Open(SevenZip.IInStream,System.UInt64@,SevenZip.IArchiveOpenCallback)">
            <summary>
            Opens archive for reading.
            </summary>
            <param name="stream">Archive file stream</param>
            <param name="maxCheckStartPosition">Maximum start position for checking</param>
            <param name="openArchiveCallback">Callback for opening archive</param>
            <returns></returns>
        </member>
        <member name="M:SevenZip.IInArchive.Close">
            <summary>
            Closes the archive.
            </summary>
        </member>
        <member name="M:SevenZip.IInArchive.GetNumberOfItems">
            <summary>
            Gets the number of files in the archive file table  .          
            </summary>
            <returns>The number of files in the archive</returns>
        </member>
        <member name="M:SevenZip.IInArchive.GetProperty(System.UInt32,SevenZip.ItemPropId,SevenZip.PropVariant@)">
            <summary>
            Retrieves specific property data.
            </summary>
            <param name="index">File index in the archive file table</param>
            <param name="propId">Property code</param>
            <param name="value">Property variant value</param>
        </member>
        <member name="M:SevenZip.IInArchive.Extract(System.UInt32[],System.UInt32,System.Int32,SevenZip.IArchiveExtractCallback)">
            <summary>
            Extracts files from the opened archive.
            </summary>
            <param name="indexes">indexes of files to be extracted (must be sorted)</param>
            <param name="numItems">0xFFFFFFFF means all files</param>
            <param name="testMode">testMode != 0 means "test files operation"</param>
            <param name="extractCallback">IArchiveExtractCallback for operations handling</param>
            <returns>0 if success</returns>
        </member>
        <member name="M:SevenZip.IInArchive.GetArchiveProperty(SevenZip.ItemPropId,SevenZip.PropVariant@)">
            <summary>
            Gets archive property data
            </summary>
            <param name="propId">Archive property identificator</param>
            <param name="value">Archive property value</param>
        </member>
        <member name="M:SevenZip.IInArchive.GetNumberOfProperties">
            <summary>
            Gets the number of properties
            </summary>
            <returns>The number of properties</returns>
        </member>
        <member name="M:SevenZip.IInArchive.GetPropertyInfo(System.UInt32,System.String@,SevenZip.ItemPropId@,System.UInt16@)">
            <summary>
            Gets property information
            </summary>
            <param name="index">Item index</param>
            <param name="name">Name</param>
            <param name="propId">Property identifier</param>
            <param name="varType">Variant type</param>
        </member>
        <member name="M:SevenZip.IInArchive.GetNumberOfArchiveProperties">
            <summary>
            Gets the number of archive properties
            </summary>
            <returns>The number of archive properties</returns>
        </member>
        <member name="M:SevenZip.IInArchive.GetArchivePropertyInfo(System.UInt32,System.String@,SevenZip.ItemPropId@,System.UInt16@)">
            <summary>
            Gets the archive property information
            </summary>
            <param name="index">Item index</param>
            <param name="name">Name</param>
            <param name="propId">Property identifier</param>
            <param name="varType">Variant type</param>
        </member>
        <member name="T:SevenZip.IOutArchive">
            <summary>
            7-zip essential out archive interface
            </summary>
        </member>
        <member name="M:SevenZip.IOutArchive.UpdateItems(SevenZip.ISequentialOutStream,System.UInt32,SevenZip.IArchiveUpdateCallback)">
            <summary>
            Updates archive items
            </summary>
            <param name="outStream">The ISequentialOutStream pointer for writing the archive data</param>
            <param name="numItems">Number of archive items</param>
            <param name="updateCallback">The IArchiveUpdateCallback pointer</param>
            <returns>Zero if Ok</returns>
        </member>
        <member name="M:SevenZip.IOutArchive.GetFileTimeType(System.IntPtr)">
            <summary>
            Gets file time type(?)
            </summary>
            <param name="type">Type pointer</param>
        </member>
        <member name="T:SevenZip.ISetProperties">
            <summary>
            7-zip ISetProperties interface for setting various archive properties
            </summary>
        </member>
        <member name="M:SevenZip.ISetProperties.SetProperties(System.IntPtr,System.IntPtr,System.Int32)">
            <summary>
            Sets the archive properties
            </summary>
            <param name="names">The names of the properties</param>
            <param name="values">The values of the properties</param>
            <param name="numProperties">The properties count</param>
            <returns></returns>        
        </member>
        <member name="T:SevenZip.CompressionMode">
            <summary>
            Archive compression mode.
            </summary>
        </member>
        <member name="F:SevenZip.CompressionMode.Create">
            <summary>
            Create a new archive; overwrite the existing one.
            </summary>
        </member>
        <member name="F:SevenZip.CompressionMode.Append">
            <summary>
            Add data to the archive.
            </summary>
        </member>
        <member name="T:SevenZip.ExtractFileCallbackArgs">
            <summary>
            The arguments passed to <see cref="T:SevenZip.ExtractFileCallback"/>.
            </summary>
            <remarks>
            For each file, <see cref="T:SevenZip.ExtractFileCallback"/> is first called with <see cref="P:SevenZip.ExtractFileCallbackArgs.Reason"/>
            set to <see cref="F:SevenZip.ExtractFileCallbackReason.Start"/>. If the callback chooses to extract the
            file data by setting <see cref="P:SevenZip.ExtractFileCallbackArgs.ExtractToFile"/> or <see cref="P:SevenZip.ExtractFileCallbackArgs.ExtractToStream"/>, the callback
            will be called a second time with <see cref="P:SevenZip.ExtractFileCallbackArgs.Reason"/> set to
            <see cref="F:SevenZip.ExtractFileCallbackReason.Done"/> or <see cref="F:SevenZip.ExtractFileCallbackReason.Failure"/>
            to allow for any cleanup task like closing the stream.
            </remarks>
        </member>
        <member name="M:SevenZip.ExtractFileCallbackArgs.#ctor(SevenZip.ArchiveFileInfo)">
            <summary>
            Initializes a new instance of the <see cref="T:SevenZip.ExtractFileCallbackArgs"/> class.
            </summary>
            <param name="archiveFileInfo">The information about file in the archive.</param>
        </member>
        <member name="P:SevenZip.ExtractFileCallbackArgs.ArchiveFileInfo">
            <summary>
            Information about file in the archive.
            </summary>
            <value>Information about file in the archive.</value>
        </member>
        <member name="P:SevenZip.ExtractFileCallbackArgs.Reason">
            <summary>
            The reason for calling <see cref="T:SevenZip.ExtractFileCallback"/>.
            </summary>
            <remarks>
            If neither <see cref="P:SevenZip.ExtractFileCallbackArgs.ExtractToFile"/> nor <see cref="P:SevenZip.ExtractFileCallbackArgs.ExtractToStream"/> is set,
             <see cref="T:SevenZip.ExtractFileCallback"/> will not be called after <see cref="F:SevenZip.ExtractFileCallbackReason.Start"/>.
            </remarks>
            <value>The reason.</value>
        </member>
        <member name="P:SevenZip.ExtractFileCallbackArgs.Exception">
            <summary>
            The exception that occurred during extraction.
            </summary>
            <value>The _Exception.</value>
            <remarks>
            If the callback is called with <see cref="P:SevenZip.ExtractFileCallbackArgs.Reason"/> set to <see cref="F:SevenZip.ExtractFileCallbackReason.Failure"/>,
            this member contains the _Exception that occurred.
            The default behavior is to rethrow the _Exception after return of the callback.
            However the callback can set <see cref="P:SevenZip.ExtractFileCallbackArgs.Exception"/> to <c>null</c> to swallow the _Exception
            and continue extraction with the next file.
            </remarks>
        </member>
        <member name="P:SevenZip.ExtractFileCallbackArgs.CancelExtraction">
            <summary>
            Gets or sets a value indicating whether to cancel the extraction.
            </summary>
            <value><c>true</c> to cancel the extraction; <c>false</c> to continue. The default is <c>false</c>.</value>
        </member>
        <member name="P:SevenZip.ExtractFileCallbackArgs.ExtractToFile">
            <summary>
            Gets or sets whether and where to extract the file.
            </summary>
            <value>The path where to extract the file to.</value>
            <remarks>
            If <see cref="P:SevenZip.ExtractFileCallbackArgs.ExtractToStream"/> is set, this mmember will be ignored.
            </remarks>
        </member>
        <member name="P:SevenZip.ExtractFileCallbackArgs.ExtractToStream">
            <summary>
            Gets or sets whether and where to extract the file.
            </summary>
            <value>The the extracted data is written to.</value>
            <remarks>
            If both this member and <see cref="P:SevenZip.ExtractFileCallbackArgs.ExtractToFile"/> are <c>null</c> (the defualt), the file
            will not be extracted and the callback will be be executed a second time with the <see cref="P:SevenZip.ExtractFileCallbackArgs.Reason"/>
            set to <see cref="F:SevenZip.ExtractFileCallbackReason.Done"/> or <see cref="F:SevenZip.ExtractFileCallbackReason.Failure"/>.
            </remarks>
        </member>
        <member name="P:SevenZip.ExtractFileCallbackArgs.ObjectData">
            <summary>
            Gets or sets any data that will be preserved between the <see cref="F:SevenZip.ExtractFileCallbackReason.Start"/> callback call
            and the <see cref="F:SevenZip.ExtractFileCallbackReason.Done"/> or <see cref="F:SevenZip.ExtractFileCallbackReason.Failure"/> calls.
            </summary>
            <value>The data.</value>
        </member>
        <member name="T:SevenZip.ExtractFileCallbackReason">
            <summary>
            The reason for calling <see cref="T:SevenZip.ExtractFileCallback"/>.
            </summary>
        </member>
        <member name="F:SevenZip.ExtractFileCallbackReason.Start">
            <summary>
            <see cref="T:SevenZip.ExtractFileCallback"/> is called the first time for a file.
            </summary>
        </member>
        <member name="F:SevenZip.ExtractFileCallbackReason.Done">
            <summary>
            All data has been written to the target without any exceptions.
            </summary>
        </member>
        <member name="F:SevenZip.ExtractFileCallbackReason.Failure">
            <summary>
            An exception occurred during extraction of the file.
            </summary>
        </member>
        <member name="T:SevenZip.FileInfoEventArgs">
            <summary>
            EventArgs used to report the file information which is going to be packed.
            </summary>
        </member>
        <member name="M:SevenZip.FileInfoEventArgs.#ctor(SevenZip.ArchiveFileInfo,System.Byte)">
            <summary>
            Initializes a new instance of the FileInfoEventArgs class.
            </summary>
            <param name="fileInfo">The current ArchiveFileInfo.</param>
            <param name="percentDone">The percent of finished work.</param>
        </member>
        <member name="P:SevenZip.FileInfoEventArgs.Cancel">
            <summary>
            Gets or sets whether to stop the current archive operation.
            </summary>
        </member>
        <member name="P:SevenZip.FileInfoEventArgs.Skip">
            <summary>
            Gets or sets whether to skip the current file.
            </summary>
        </member>
        <member name="P:SevenZip.FileInfoEventArgs.FileInfo">
            <summary>
            Gets the corresponding FileInfo to the event.
            </summary>
        </member>
        <member name="T:SevenZip.FileNameEventArgs">
            <summary>
            EventArgs class which stores the file name.
            </summary>
        </member>
        <member name="M:SevenZip.FileNameEventArgs.#ctor(System.String,System.Byte)">
            <summary>
            Initializes a new instance of the FileNameEventArgs class.
            </summary>
            <param name="fileName">The file name.</param>
            <param name="percentDone">The percent of finished work</param>
        </member>
        <member name="P:SevenZip.FileNameEventArgs.Cancel">
            <summary>
            Gets or sets whether to stop the current archive operation.
            </summary>
        </member>
        <member name="P:SevenZip.FileNameEventArgs.Skip">
            <summary>
            Gets or sets whether to stop the current archive operation.
            </summary>
        </member>
        <member name="P:SevenZip.FileNameEventArgs.FileName">
            <summary>
            Gets the file name.
            </summary>
        </member>
        <member name="T:SevenZip.FileOverwriteEventArgs">
            <summary>
            EventArgs for FileExists event, stores the file name and asks whether to overwrite it in case it already exists.
            </summary>
        </member>
        <member name="M:SevenZip.FileOverwriteEventArgs.#ctor(System.String)">
            <summary>
            Initializes a new instance of the FileOverwriteEventArgs class
            </summary>
            <param name="fileName">The file name.</param>
        </member>
        <member name="P:SevenZip.FileOverwriteEventArgs.Cancel">
            <summary>
            Gets or sets the value indicating whether to cancel the extraction.
            </summary>
        </member>
        <member name="P:SevenZip.FileOverwriteEventArgs.FileName">
            <summary>
            Gets or sets the file name to extract to. Null means skip.
            </summary>
        </member>
        <member name="T:SevenZip.IntEventArgs">
            <summary>
            Stores an int number
            </summary>
        </member>
        <member name="M:SevenZip.IntEventArgs.#ctor(System.Int32)">
            <summary>
            Initializes a new instance of the IntEventArgs class
            </summary>
            <param name="value">Useful data carried by the IntEventArgs class</param>
        </member>
        <member name="P:SevenZip.IntEventArgs.Value">
            <summary>
            Gets the value of the IntEventArgs class
            </summary>
        </member>
        <member name="T:SevenZip.OpenEventArgs">
            <summary>
            EventArgs used to report the size of unpacked archive data
            </summary>
        </member>
        <member name="M:SevenZip.OpenEventArgs.#ctor(System.UInt64)">
            <summary>
            Initializes a new instance of the OpenEventArgs class
            </summary>
            <param name="totalSize">Size of unpacked archive data</param>
        </member>
        <member name="P:SevenZip.OpenEventArgs.TotalSize">
            <summary>
            Gets the size of unpacked archive data
            </summary>
        </member>
        <member name="T:SevenZip.PercentDoneEventArgs">
            <summary>
            EventArgs for storing PercentDone property.
            </summary>
        </member>
        <member name="M:SevenZip.PercentDoneEventArgs.#ctor(System.Byte)">
            <summary>
            Initializes a new instance of the PercentDoneEventArgs class.
            </summary>
            <param name="percentDone">The percent of finished work.</param>
            <exception cref="T:System.ArgumentOutOfRangeException"/>
        </member>
        <member name="P:SevenZip.PercentDoneEventArgs.PercentDone">
            <summary>
            Gets the percent of finished work.
            </summary>
        </member>
        <member name="M:SevenZip.PercentDoneEventArgs.ProducePercentDone(System.Single)">
            <summary>
            Converts a [0, 1] rate to its percent equivalent.
            </summary>
            <param name="doneRate">The rate of the done work.</param>
            <returns>Percent integer equivalent.</returns>
            <exception cref="T:System.ArgumentException"/>
        </member>
        <member name="T:SevenZip.ProgressEventArgs">
            <summary>
            The EventArgs class for accurate progress handling.
            </summary>
        </member>
        <member name="M:SevenZip.ProgressEventArgs.#ctor(System.Byte,System.Byte)">
            <summary>
            Initializes a new instance of the ProgressEventArgs class.
            </summary>
            <param name="percentDone">The percent of finished work.</param>
            <param name="percentDelta">The percent of work done after the previous event.</param>
        </member>
        <member name="P:SevenZip.ProgressEventArgs.PercentDelta">
            <summary>
            Gets the change in done work percentage.
            </summary>
        </member>
        <member name="T:SevenZip.EventSynchronizationStrategy">
            <summary>
            The way of the event synchronization.
            </summary>
        </member>
        <member name="F:SevenZip.EventSynchronizationStrategy.Default">
            <summary>
            Events are called synchronously if user can do some action; that is, cancel the execution process for example.
            </summary>
        </member>
        <member name="F:SevenZip.EventSynchronizationStrategy.AlwaysAsynchronous">
            <summary>
            Always call events asynchronously.
            </summary>
        </member>
        <member name="F:SevenZip.EventSynchronizationStrategy.AlwaysSynchronous">
            <summary>
            Always call events synchronously.
            </summary>
        </member>
        <member name="T:SevenZip.CompressionFailedException">
            <summary>
            Exception class for ArchiveUpdateCallback.
            </summary>
        </member>
        <member name="F:SevenZip.CompressionFailedException.DEFAULT_MESSAGE">
            <summary>
            Exception default message which is displayed if no extra information is specified
            </summary>
        </member>
        <member name="M:SevenZip.CompressionFailedException.#ctor">
            <summary>
            Initializes a new instance of the CompressionFailedException class
            </summary>
        </member>
        <member name="M:SevenZip.CompressionFailedException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the CompressionFailedException class
            </summary>
            <param name="message">Additional detailed message</param>
        </member>
        <member name="M:SevenZip.CompressionFailedException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the CompressionFailedException class
            </summary>
            <param name="message">Additional detailed message</param>
            <param name="inner">Inner exception occurred</param>
        </member>
        <member name="M:SevenZip.CompressionFailedException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance of the CompressionFailedException class
            </summary>
            <param name="info">All data needed for serialization or deserialization</param>
            <param name="context">Serialized stream descriptor</param>
        </member>
        <member name="T:SevenZip.ExtractionFailedException">
            <summary>
            Exception class for ArchiveExtractCallback.
            </summary>
        </member>
        <member name="F:SevenZip.ExtractionFailedException.DEFAULT_MESSAGE">
            <summary>
            Exception default message which is displayed if no extra information is specified
            </summary>
        </member>
        <member name="M:SevenZip.ExtractionFailedException.#ctor">
            <summary>
            Initializes a new instance of the ExtractionFailedException class
            </summary>
        </member>
        <member name="M:SevenZip.ExtractionFailedException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the ExtractionFailedException class
            </summary>
            <param name="message">Additional detailed message</param>
        </member>
        <member name="M:SevenZip.ExtractionFailedException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the ExtractionFailedException class
            </summary>
            <param name="message">Additional detailed message</param>
            <param name="inner">Inner exception occurred</param>
        </member>
        <member name="M:SevenZip.ExtractionFailedException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance of the ExtractionFailedException class
            </summary>
            <param name="info">All data needed for serialization or deserialization</param>
            <param name="context">Serialized stream descriptor</param>
        </member>
        <member name="T:SevenZip.LzmaException">
            <summary>
            Exception class for LZMA operations.
            </summary>
        </member>
        <member name="F:SevenZip.LzmaException.DEFAULT_MESSAGE">
            <summary>
            Exception default message which is displayed if no extra information is specified
            </summary>
        </member>
        <member name="M:SevenZip.LzmaException.#ctor">
            <summary>
            Initializes a new instance of the LzmaException class
            </summary>
        </member>
        <member name="M:SevenZip.LzmaException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the LzmaException class
            </summary>
            <param name="message">Additional detailed message</param>
        </member>
        <member name="M:SevenZip.LzmaException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the LzmaException class
            </summary>
            <param name="message">Additional detailed message</param>
            <param name="inner">Inner exception occurred</param>
        </member>
        <member name="M:SevenZip.LzmaException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance of the LzmaException class
            </summary>
            <param name="info">All data needed for serialization or deserialization</param>
            <param name="context">Serialized stream descriptor</param>
        </member>
        <member name="T:SevenZip.SevenZipArchiveException">
            <summary>
            Exception class for 7-zip archive open or read operations.
            </summary>
        </member>
        <member name="F:SevenZip.SevenZipArchiveException.DefaultMessage">
            <summary>
            Exception default message which is displayed if no extra information is specified
            </summary>
        </member>
        <member name="M:SevenZip.SevenZipArchiveException.#ctor">
            <summary>
            Initializes a new instance of the SevenZipArchiveException class
            </summary>
        </member>
        <member name="M:SevenZip.SevenZipArchiveException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the SevenZipArchiveException class
            </summary>
            <param name="message">Additional detailed message</param>
        </member>
        <member name="M:SevenZip.SevenZipArchiveException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the SevenZipArchiveException class
            </summary>
            <param name="message">Additional detailed message</param>
            <param name="inner">Inner exception occurred</param>
        </member>
        <member name="M:SevenZip.SevenZipArchiveException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance of the SevenZipArchiveException class
            </summary>
            <param name="info">All data needed for serialization or deserialization</param>
            <param name="context">Serialized stream descriptor</param>
        </member>
        <member name="T:SevenZip.SevenZipCompressionFailedException">
            <summary>
            Exception class for fail to create an archive in SevenZipCompressor.
            </summary>
        </member>
        <member name="F:SevenZip.SevenZipCompressionFailedException.DEFAULT_MESSAGE">
            <summary>
            Exception default message which is displayed if no extra information is specified
            </summary>
        </member>
        <member name="M:SevenZip.SevenZipCompressionFailedException.#ctor">
            <summary>
            Initializes a new instance of the SevenZipCompressionFailedException class
            </summary>
        </member>
        <member name="M:SevenZip.SevenZipCompressionFailedException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the SevenZipCompressionFailedException class
            </summary>
            <param name="message">Additional detailed message</param>
        </member>
        <member name="M:SevenZip.SevenZipCompressionFailedException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the SevenZipCompressionFailedException class
            </summary>
            <param name="message">Additional detailed message</param>
            <param name="inner">Inner exception occurred</param>
        </member>
        <member name="M:SevenZip.SevenZipCompressionFailedException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance of the SevenZipCompressionFailedException class
            </summary>
            <param name="info">All data needed for serialization or deserialization</param>
            <param name="context">Serialized stream descriptor</param>
        </member>
        <member name="T:SevenZip.SevenZipException">
            <summary>
            Base SevenZip exception class.
            </summary>
        </member>
        <member name="F:SevenZip.SevenZipException.USER_EXCEPTION_MESSAGE">
            <summary>
            The message for thrown user exceptions.
            </summary>
        </member>
        <member name="M:SevenZip.SevenZipException.#ctor">
            <summary>
            Initializes a new instance of the SevenZipException class
            </summary>
        </member>
        <member name="M:SevenZip.SevenZipException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the SevenZipException class
            </summary>
            <param name="defaultMessage">Default exception message</param>
        </member>
        <member name="M:SevenZip.SevenZipException.#ctor(System.String,System.String)">
            <summary>
            Initializes a new instance of the SevenZipException class
            </summary>
            <param name="defaultMessage">Default exception message</param>
            <param name="message">Additional detailed message</param>
        </member>
        <member name="M:SevenZip.SevenZipException.#ctor(System.String,System.String,System.Exception)">
            <summary>
            Initializes a new instance of the SevenZipException class
            </summary>
            <param name="defaultMessage">Default exception message</param>
            <param name="message">Additional detailed message</param>
            <param name="inner">Inner exception occurred</param>
        </member>
        <member name="M:SevenZip.SevenZipException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the SevenZipException class
            </summary>
            <param name="defaultMessage">Default exception message</param>
            <param name="inner">Inner exception occurred</param>
        </member>
        <member name="M:SevenZip.SevenZipException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance of the SevenZipException class
            </summary>
            <param name="info">All data needed for serialization or deserialization</param>
            <param name="context">Serialized stream descriptor</param>
        </member>
        <member name="T:SevenZip.SevenZipExtractionFailedException">
            <summary>
            Exception class for fail to extract an archive in SevenZipExtractor.
            </summary>
        </member>
        <member name="F:SevenZip.SevenZipExtractionFailedException.DEFAULT_MESSAGE">
            <summary>
            Exception default message which is displayed if no extra information is specified
            </summary>
        </member>
        <member name="M:SevenZip.SevenZipExtractionFailedException.#ctor">
            <summary>
            Initializes a new instance of the SevenZipExtractionFailedException class
            </summary>
        </member>
        <member name="M:SevenZip.SevenZipExtractionFailedException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the SevenZipExtractionFailedException class
            </summary>
            <param name="message">Additional detailed message</param>
        </member>
        <member name="M:SevenZip.SevenZipExtractionFailedException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the SevenZipExtractionFailedException class
            </summary>
            <param name="message">Additional detailed message</param>
            <param name="inner">Inner exception occurred</param>
        </member>
        <member name="M:SevenZip.SevenZipExtractionFailedException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance of the SevenZipExtractionFailedException class
            </summary>
            <param name="info">All data needed for serialization or deserialization</param>
            <param name="context">Serialized stream descriptor</param>
        </member>
        <member name="T:SevenZip.SevenZipInvalidFileNamesException">
            <summary>
            Exception class for empty common root if file name array in SevenZipCompressor.
            </summary>
        </member>
        <member name="F:SevenZip.SevenZipInvalidFileNamesException.DEFAULT_MESSAGE">
            <summary>
            Exception default message which is displayed if no extra information is specified
            </summary>
        </member>
        <member name="M:SevenZip.SevenZipInvalidFileNamesException.#ctor">
            <summary>
            Initializes a new instance of the SevenZipInvalidFileNamesException class
            </summary>
        </member>
        <member name="M:SevenZip.SevenZipInvalidFileNamesException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the SevenZipInvalidFileNamesException class
            </summary>
            <param name="message">Additional detailed message</param>
        </member>
        <member name="M:SevenZip.SevenZipInvalidFileNamesException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the SevenZipInvalidFileNamesException class
            </summary>
            <param name="message">Additional detailed message</param>
            <param name="inner">Inner exception occurred</param>
        </member>
        <member name="M:SevenZip.SevenZipInvalidFileNamesException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance of the SevenZipInvalidFileNamesException class
            </summary>
            <param name="info">All data needed for serialization or deserialization</param>
            <param name="context">Serialized stream descriptor</param>
        </member>
        <member name="T:SevenZip.SevenZipLibraryException">
            <summary>
            Exception class for 7-zip library operations.
            </summary>
        </member>
        <member name="F:SevenZip.SevenZipLibraryException.DEFAULT_MESSAGE">
            <summary>
            Exception default message which is displayed if no extra information is specified
            </summary>
        </member>
        <member name="M:SevenZip.SevenZipLibraryException.#ctor">
            <summary>
            Initializes a new instance of the SevenZipLibraryException class
            </summary>
        </member>
        <member name="M:SevenZip.SevenZipLibraryException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the SevenZipLibraryException class
            </summary>
            <param name="message">Additional detailed message</param>
        </member>
        <member name="M:SevenZip.SevenZipLibraryException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the SevenZipLibraryException class
            </summary>
            <param name="message">Additional detailed message</param>
            <param name="inner">Inner exception occurred</param>
        </member>
        <member name="M:SevenZip.SevenZipLibraryException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance of the SevenZipLibraryException class
            </summary>
            <param name="info">All data needed for serialization or deserialization</param>
            <param name="context">Serialized stream descriptor</param>
        </member>
        <member name="T:SevenZip.SevenZipSfxValidationException">
            <summary>
            Exception class for 7-zip sfx settings validation.
            </summary>
        </member>
        <member name="F:SevenZip.SevenZipSfxValidationException.DefaultMessage">
            <summary>
            Exception default message which is displayed if no extra information is specified
            </summary>
        </member>
        <member name="M:SevenZip.SevenZipSfxValidationException.#ctor">
            <summary>
            Initializes a new instance of the SevenZipSfxValidationException class
            </summary>
        </member>
        <member name="M:SevenZip.SevenZipSfxValidationException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the SevenZipSfxValidationException class
            </summary>
            <param name="message">Additional detailed message</param>
        </member>
        <member name="M:SevenZip.SevenZipSfxValidationException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the SevenZipSfxValidationException class
            </summary>
            <param name="message">Additional detailed message</param>
            <param name="inner">Inner exception occurred</param>
        </member>
        <member name="M:SevenZip.SevenZipSfxValidationException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance of the SevenZipSfxValidationException class
            </summary>
            <param name="info">All data needed for serialization or deserialization</param>
            <param name="context">Serialized stream descriptor</param>
        </member>
        <member name="T:SevenZip.ExtractFileCallback">
            <summary>
            Callback delegate for <see cref="M:SevenZip.SevenZipExtractor.ExtractFiles(SevenZip.ExtractFileCallback)"/>.
            </summary>
        </member>
        <member name="T:SevenZip.FileChecker">
            <summary>
            The signature checker class. Original code by Siddharth Uppal, adapted by Markhor.
            </summary>
            <remarks>Based on the code at http://blog.somecreativity.com/2008/04/08/how-to-check-if-a-file-is-compressed-in-c/#</remarks>
        </member>
        <member name="M:SevenZip.FileChecker.CheckSignature(System.IO.Stream,System.Int32@,System.Boolean@)">
            <summary>
            Gets the InArchiveFormat for a specific extension.
            </summary>
            <param name="stream">The stream to identify.</param>
            <param name="offset">The archive beginning offset.</param>
            <param name="isExecutable">True if the original format of the stream is PE; otherwise, false.</param>
            <returns>Corresponding InArchiveFormat.</returns>
        </member>
        <member name="M:SevenZip.FileChecker.CheckSignature(System.String,System.Int32@,System.Boolean@)">
            <summary>
            Gets the InArchiveFormat for a specific file name.
            </summary>
            <param name="fileName">The archive file name.</param>
            <param name="offset">The archive beginning offset.</param>
            <param name="isExecutable">True if the original format of the file is PE; otherwise, false.</param>
            <returns>Corresponding InArchiveFormat.</returns>
            <exception cref="T:System.ArgumentException"/>
        </member>
        <member name="T:SevenZip.InArchiveFormat">
            <summary>
            Readable archive format enumeration.
            </summary>
        </member>
        <member name="F:SevenZip.InArchiveFormat.SevenZip">
            <summary>
            Open 7-zip archive format.
            </summary>  
            <remarks><a href="http://en.wikipedia.org/wiki/7-zip">Wikipedia information</a></remarks> 
        </member>
        <member name="F:SevenZip.InArchiveFormat.Arj">
            <summary>
            Proprietary Arj archive format.
            </summary>
            <remarks><a href="http://en.wikipedia.org/wiki/ARJ">Wikipedia information</a></remarks>
        </member>
        <member name="F:SevenZip.InArchiveFormat.BZip2">
            <summary>
            Open Bzip2 archive format.
            </summary>
            <remarks><a href="http://en.wikipedia.org/wiki/Bzip2">Wikipedia information</a></remarks>
        </member>
        <member name="F:SevenZip.InArchiveFormat.Cab">
            <summary>
            Microsoft cabinet archive format.
            </summary>
            <remarks><a href="http://en.wikipedia.org/wiki/Cabinet_(file_format)">Wikipedia information</a></remarks>
        </member>
        <member name="F:SevenZip.InArchiveFormat.Chm">
            <summary>
            Microsoft Compiled HTML Help file format.
            </summary>
            <remarks><a href="http://en.wikipedia.org/wiki/Microsoft_Compiled_HTML_Help">Wikipedia information</a></remarks>
        </member>
        <member name="F:SevenZip.InArchiveFormat.Compound">
            <summary>
            Microsoft Compound file format.
            </summary>
            <remarks><a href="http://en.wikipedia.org/wiki/Compound_File_Binary_Format">Wikipedia information</a></remarks>
        </member>
        <member name="F:SevenZip.InArchiveFormat.Cpio">
            <summary>
            Open Cpio archive format.
            </summary>
            <remarks><a href="http://en.wikipedia.org/wiki/Cpio">Wikipedia information</a></remarks>
        </member>
        <member name="F:SevenZip.InArchiveFormat.Deb">
            <summary>
            Open Debian software package format.
            </summary>
            <remarks><a href="http://en.wikipedia.org/wiki/Deb_(file_format)">Wikipedia information</a></remarks>
        </member>
        <member name="F:SevenZip.InArchiveFormat.GZip">
            <summary>
            Open Gzip archive format.
            </summary>
            <remarks><a href="http://en.wikipedia.org/wiki/Gzip">Wikipedia information</a></remarks>
        </member>
        <member name="F:SevenZip.InArchiveFormat.Iso">
            <summary>
            Open ISO disk image format.
            </summary>
            <remarks><a href="http://en.wikipedia.org/wiki/ISO_image">Wikipedia information</a></remarks>
        </member>
        <member name="F:SevenZip.InArchiveFormat.Lzh">
            <summary>
            Open Lzh archive format.
            </summary>
            <remarks><a href="http://en.wikipedia.org/wiki/Lzh">Wikipedia information</a></remarks>
        </member>
        <member name="F:SevenZip.InArchiveFormat.Lzma">
            <summary>
            Open core 7-zip Lzma raw archive format.
            </summary>
            <remarks><a href="http://en.wikipedia.org/wiki/Lzma">Wikipedia information</a></remarks>
        </member>
        <member name="F:SevenZip.InArchiveFormat.Nsis">
            <summary>
            Nullsoft installation package format.
            </summary>
            <remarks><a href="http://en.wikipedia.org/wiki/NSIS">Wikipedia information</a></remarks>
        </member>
        <member name="F:SevenZip.InArchiveFormat.Gpt">
            <summary>
            GUID Partition Table.
            </summary>
            <remarks><a href="https://en.wikipedia.org/wiki/GUID_Partition_Table">Wikipedia information</a></remarks>
        </member>
        <member name="F:SevenZip.InArchiveFormat.Rar">
            <summary>
            RarLab Rar archive format, version 5.
            </summary>
            <remarks><a href="http://en.wikipedia.org/wiki/Rar">Wikipedia information</a></remarks>
        </member>
        <member name="F:SevenZip.InArchiveFormat.Rar4">
            <summary>
            RarLab Rar archive format, version 4 or older.
            </summary>
            <remarks><a href="http://en.wikipedia.org/wiki/Rar">Wikipedia information</a></remarks>
        </member>
        <member name="F:SevenZip.InArchiveFormat.Rpm">
            <summary>
            Open Rpm software package format.
            </summary>
            <remarks><a href="http://en.wikipedia.org/wiki/RPM_Package_Manager">Wikipedia information</a></remarks>
        </member>
        <member name="F:SevenZip.InArchiveFormat.Split">
            <summary>
            Open split file format.
            </summary>
            <remarks><a href="?">Wikipedia information</a></remarks>
        </member>
        <member name="F:SevenZip.InArchiveFormat.Tar">
            <summary>
            Open Tar archive format.
            </summary>
            <remarks><a href="http://en.wikipedia.org/wiki/Tar_(file_format)">Wikipedia information</a></remarks>
        </member>
        <member name="F:SevenZip.InArchiveFormat.Wim">
            <summary>
            Microsoft Windows Imaging disk image format.
            </summary>
            <remarks><a href="http://en.wikipedia.org/wiki/Windows_Imaging_Format">Wikipedia information</a></remarks>
        </member>
        <member name="F:SevenZip.InArchiveFormat.Lzw">
            <summary>
            Open LZW archive format; implemented in "compress" program; also known as "Z" archive format.
            </summary>
            <remarks><a href="http://en.wikipedia.org/wiki/Compress">Wikipedia information</a></remarks>
        </member>
        <member name="F:SevenZip.InArchiveFormat.Zip">
            <summary>
            Open Zip archive format.
            </summary>
            <remarks><a href="http://en.wikipedia.org/wiki/ZIP_(file_format)">Wikipedia information</a></remarks>
        </member>
        <member name="F:SevenZip.InArchiveFormat.Udf">
            <summary>
            Open Udf disk image format.
            </summary>
        </member>
        <member name="F:SevenZip.InArchiveFormat.Xar">
            <summary>
            Xar open source archive format.
            </summary>
            <remarks><a href="http://en.wikipedia.org/wiki/Xar_(archiver)">Wikipedia information</a></remarks>
        </member>
        <member name="F:SevenZip.InArchiveFormat.Mub">
            <summary>
            Mub
            </summary>
        </member>
        <member name="F:SevenZip.InArchiveFormat.Hfs">
            <summary>
            Macintosh Disk Image on CD.
            </summary>
            <remarks><a href="http://en.wikipedia.org/wiki/HFS_Plus">Wikipedia information</a></remarks>
        </member>
        <member name="F:SevenZip.InArchiveFormat.Dmg">
            <summary>
            Apple Mac OS X Disk Copy Disk Image format.
            </summary>
        </member>
        <member name="F:SevenZip.InArchiveFormat.XZ">
            <summary>
            Open Xz archive format.
            </summary>
            <remarks><a href="http://en.wikipedia.org/wiki/Xz">Wikipedia information</a></remarks>        
        </member>
        <member name="F:SevenZip.InArchiveFormat.Mslz">
            <summary>
            MSLZ archive format.
            </summary>
        </member>
        <member name="F:SevenZip.InArchiveFormat.Flv">
            <summary>
            Flash video format.
            </summary>
            <remarks><a href="http://en.wikipedia.org/wiki/Flv">Wikipedia information</a></remarks>
        </member>
        <member name="F:SevenZip.InArchiveFormat.Swf">
            <summary>
            Shockwave Flash format.
            </summary>
            <remarks><a href="http://en.wikipedia.org/wiki/Swf">Wikipedia information</a></remarks>         
        </member>
        <member name="F:SevenZip.InArchiveFormat.PE">
            <summary>
            Windows PE executable format.
            </summary>
            <remarks><a href="http://en.wikipedia.org/wiki/Portable_Executable">Wikipedia information</a></remarks>
        </member>
        <member name="F:SevenZip.InArchiveFormat.Elf">
            <summary>
            Linux executable Elf format.
            </summary>
            <remarks><a href="http://en.wikipedia.org/wiki/Executable_and_Linkable_Format">Wikipedia information</a></remarks>
        </member>
        <member name="F:SevenZip.InArchiveFormat.Msi">
            <summary>
            Windows Installer Database.
            </summary>
            <remarks><a href="http://en.wikipedia.org/wiki/Windows_Installer">Wikipedia information</a></remarks>
        </member>
        <member name="F:SevenZip.InArchiveFormat.Vhd">
            <summary>
            Microsoft virtual hard disk file format.
            </summary>
            <remarks><a href="http://en.wikipedia.org/wiki/VHD_%28file_format%29">Wikipedia information</a></remarks>
        </member>
        <member name="F:SevenZip.InArchiveFormat.SquashFS">
            <summary>
            SquashFS file system format.
            </summary>
            <remarks><a href="https://en.wikipedia.org/wiki/SquashFS">Wikipedia information</a></remarks>
        </member>
        <member name="F:SevenZip.InArchiveFormat.Lzma86">
            <summary>
            Lzma86 file format.
            </summary>
        </member>
        <member name="F:SevenZip.InArchiveFormat.Ppmd">
            <summary>
            Prediction by Partial Matching by Dmitry algorithm.
            </summary>
            <remarks><a href="https://en.wikipedia.org/wiki/Prediction_by_partial_matching">Wikipedia information</a></remarks>
        </member>
        <member name="F:SevenZip.InArchiveFormat.TE">
            <summary>
            TE format.
            </summary>
        </member>
        <member name="F:SevenZip.InArchiveFormat.UEFIc">
            <summary>
            UEFIc format.
            </summary>
            <remarks><a href="https://en.wikipedia.org/wiki/Unified_Extensible_Firmware_Interface">Wikipedia information</a></remarks>
        </member>
        <member name="F:SevenZip.InArchiveFormat.UEFIs">
            <summary>
            UEFIs format.
            </summary>
            <remarks><a href="https://en.wikipedia.org/wiki/Unified_Extensible_Firmware_Interface">Wikipedia information</a></remarks>
        </member>
        <member name="F:SevenZip.InArchiveFormat.CramFS">
            <summary>
            Compressed ROM file system format.
            </summary>
            <remarks><a href="https://en.wikipedia.org/wiki/Cramfs">Wikipedia information</a></remarks>
        </member>
        <member name="F:SevenZip.InArchiveFormat.APM">
            <summary>
            APM format.
            </summary>
        </member>
        <member name="F:SevenZip.InArchiveFormat.Swfc">
            <summary>
            Swfc format.
            </summary>
        </member>
        <member name="F:SevenZip.InArchiveFormat.Ntfs">
            <summary>
            NTFS file system format.
            </summary>
            <remarks><a href="https://en.wikipedia.org/wiki/NTFS">Wikipedia information</a></remarks>
        </member>
        <member name="F:SevenZip.InArchiveFormat.Fat">
            <summary>
            FAT file system format.
            </summary>
            <remarks><a href="https://en.wikipedia.org/wiki/File_Allocation_Table">Wikipedia information</a></remarks>
        </member>
        <member name="F:SevenZip.InArchiveFormat.Mbr">
            <summary>
            MBR format.
            </summary>
            <remarks><a href="https://en.wikipedia.org/wiki/Master_boot_record">Wikipedia information</a></remarks>
        </member>
        <member name="F:SevenZip.InArchiveFormat.MachO">
            <summary>
            Mach-O file format.
            </summary>
            <remarks><a href="https://en.wikipedia.org/wiki/Mach-O">Wikipedia information</a></remarks>
        </member>
        <member name="F:SevenZip.InArchiveFormat.Apfs">
            <summary>
            Apple File System Format.
            </summary>
            <remarks><a href="https://en.wikipedia.org/wiki/Apple_File_System">Wikipedia information</a></remarks>
        </member>
        <member name="T:SevenZip.OutArchiveFormat">
            <summary>
            Writable archive format enumeration.
            </summary>    
        </member>
        <member name="F:SevenZip.OutArchiveFormat.SevenZip">
            <summary>
            Open 7-zip archive format.
            </summary>
            <remarks><a href="http://en.wikipedia.org/wiki/7-zip">Wikipedia information</a></remarks>
        </member>
        <member name="F:SevenZip.OutArchiveFormat.Zip">
            <summary>
            Open Zip archive format.
            </summary>
            <remarks><a href="http://en.wikipedia.org/wiki/ZIP_(file_format)">Wikipedia information</a></remarks>
        </member>
        <member name="F:SevenZip.OutArchiveFormat.GZip">
            <summary>
            Open Gzip archive format.
            </summary>
            <remarks><a href="http://en.wikipedia.org/wiki/Gzip">Wikipedia information</a></remarks>
        </member>
        <member name="F:SevenZip.OutArchiveFormat.BZip2">
            <summary>       
            Open Bzip2 archive format.
            </summary>
            <remarks><a href="http://en.wikipedia.org/wiki/Bzip2">Wikipedia information</a></remarks>
        </member>
        <member name="F:SevenZip.OutArchiveFormat.Wim">
            <summary>
            Microsoft Windows Imaging disk image format.
            </summary>
            <remarks><a href="http://en.wikipedia.org/wiki/Windows_Imaging_Format">Wikipedia information</a></remarks>
        </member>
        <member name="F:SevenZip.OutArchiveFormat.Tar">
            <summary>
            Open Tar archive format.
            </summary>
            <remarks><a href="http://en.wikipedia.org/wiki/Tar_(file_format)">Wikipedia information</a></remarks>
        </member>
        <member name="F:SevenZip.OutArchiveFormat.XZ">
            <summary>
            Open Xz archive format.
            </summary>
            <remarks><a href="http://en.wikipedia.org/wiki/Xz">Wikipedia information</a></remarks>        
        </member>
        <member name="T:SevenZip.CompressionLevel">
            <summary>
            Compression level enumeration
            </summary>
        </member>
        <member name="F:SevenZip.CompressionLevel.None">
            <summary>
            No compression
            </summary>
        </member>
        <member name="F:SevenZip.CompressionLevel.Fast">
            <summary>
            Very low compression level
            </summary>
        </member>
        <member name="F:SevenZip.CompressionLevel.Low">
            <summary>
            Low compression level
            </summary>
        </member>
        <member name="F:SevenZip.CompressionLevel.Normal">
            <summary>
            Normal compression level (default)
            </summary>
        </member>
        <member name="F:SevenZip.CompressionLevel.High">
            <summary>
            High compression level
            </summary>
        </member>
        <member name="F:SevenZip.CompressionLevel.Ultra">
            <summary>
            The best compression level (slow)
            </summary>
        </member>
        <member name="T:SevenZip.CompressionMethod">
            <summary>
            Compression method enumeration.
            </summary>
            <remarks>Some methods are applicable only to Zip format, some - only to 7-zip.</remarks>
        </member>
        <member name="F:SevenZip.CompressionMethod.Copy">
            <summary>
            Zip or 7-zip|no compression method.
            </summary>
        </member>
        <member name="F:SevenZip.CompressionMethod.Deflate">
            <summary>
            Zip|Deflate method.
            </summary>
        </member>
        <member name="F:SevenZip.CompressionMethod.Deflate64">
            <summary>
            Zip|Deflate64 method.
            </summary>
        </member>
        <member name="F:SevenZip.CompressionMethod.BZip2">
            <summary>
            Zip or 7-zip|Bzip2 method.
            </summary>
            <remarks><a href="http://en.wikipedia.org/wiki/Cabinet_(file_format)">Wikipedia information</a></remarks>
        </member>
        <member name="F:SevenZip.CompressionMethod.Lzma">
            <summary>
            Zip or 7-zip|LZMA method based on Lempel-Ziv algorithm, it is default for 7-zip.
            </summary>
        </member>
        <member name="F:SevenZip.CompressionMethod.Lzma2">
            <summary>
            7-zip|LZMA version 2, LZMA with improved multithreading and usually slight archive size decrease.
            </summary>
        </member>
        <member name="F:SevenZip.CompressionMethod.Ppmd">
            <summary>
            Zip or 7-zip|PPMd method based on Dmitry Shkarin's PPMdH source code, very efficient for compressing texts.
            </summary>
            <remarks><a href="http://en.wikipedia.org/wiki/Prediction_by_Partial_Matching">Wikipedia information</a></remarks>
        </member>
        <member name="F:SevenZip.CompressionMethod.Default">
            <summary>
            No method change.
            </summary>
        </member>
        <member name="T:SevenZip.Formats">
            <summary>
            Archive format routines
            </summary>
        </member>
        <member name="F:SevenZip.Formats.InFormatGuids">
            <summary>
            List of readable archive format interface guids for 7-zip COM interop.
            </summary>
        </member>
        <member name="F:SevenZip.Formats.OutFormatGuids">
            <summary>
            List of writable archive format interface guids for 7-zip COM interop.
            </summary>
        </member>
        <member name="F:SevenZip.Formats.InExtensionFormats">
            <summary>
            List of archive formats corresponding to specific extensions
            </summary>
        </member>
        <member name="F:SevenZip.Formats.InSignatureFormats">
            <summary>
            List of archive formats corresponding to specific signatures
            </summary>
            <remarks>Based on the information at <a href="http://www.garykessler.net/library/file_sigs.html">this site.</a></remarks>
        </member>
        <member name="M:SevenZip.Formats.FormatByFileName(System.String,System.Boolean)">
            <summary>
            Gets InArchiveFormat for specified archive file name
            </summary>
            <param name="fileName">Archive file name</param>
            <param name="reportErrors">Indicates whether to throw exceptions</param>
            <returns>InArchiveFormat recognized by the file name extension</returns>
            <exception cref="T:System.ArgumentException"/>
        </member>
        <member name="T:SevenZip.ICancellable">
            <summary>
            The definition of the interface which supports the cancellation of a process.
            </summary>
        </member>
        <member name="P:SevenZip.ICancellable.Cancel">
            <summary>
            Gets or sets whether to stop the current archive operation.
            </summary>
        </member>
        <member name="P:SevenZip.ICancellable.Skip">
            <summary>
            Gets or sets whether to skip the current file.
            </summary>
        </member>
        <member name="F:SevenZip.InternalCompressionMode.Create">
            <summary>
            Create a new archive; overwrite the existing one.
            </summary>
        </member>
        <member name="F:SevenZip.InternalCompressionMode.Append">
            <summary>
            Add data to the archive.
            </summary>
        </member>
        <member name="F:SevenZip.InternalCompressionMode.Modify">
            <summary>
            Modify archive data.
            </summary>
        </member>
        <member name="T:SevenZip.LibraryFeature">
            <summary>
            The set of features supported by the library.
            </summary>
        </member>
        <member name="F:SevenZip.LibraryFeature.None">
            <summary>
            Default feature.
            </summary>
        </member>
        <member name="F:SevenZip.LibraryFeature.Extract7z">
            <summary>
            The library can extract 7zip archives compressed with LZMA method.
            </summary>
        </member>
        <member name="F:SevenZip.LibraryFeature.Extract7zLZMA2">
            <summary>
            The library can extract 7zip archives compressed with LZMA2 method.
            </summary>
        </member>
        <member name="F:SevenZip.LibraryFeature.Extract7zAll">
            <summary>
            The library can extract 7z archives compressed with all known methods.
            </summary>
        </member>
        <member name="F:SevenZip.LibraryFeature.ExtractZip">
            <summary>
            The library can extract zip archives.
            </summary>
        </member>
        <member name="F:SevenZip.LibraryFeature.ExtractRar">
            <summary>
            The library can extract rar archives.
            </summary>
        </member>
        <member name="F:SevenZip.LibraryFeature.ExtractGzip">
            <summary>
            The library can extract gzip archives.
            </summary>
        </member>
        <member name="F:SevenZip.LibraryFeature.ExtractBzip2">
            <summary>
            The library can extract bzip2 archives.
            </summary>
        </member>
        <member name="F:SevenZip.LibraryFeature.ExtractTar">
            <summary>
            The library can extract tar archives.
            </summary>
        </member>
        <member name="F:SevenZip.LibraryFeature.ExtractXz">
            <summary>
            The library can extract xz archives.
            </summary>
        </member>
        <member name="F:SevenZip.LibraryFeature.ExtractAll">
            <summary>
            The library can extract all types of archives supported.
            </summary>
        </member>
        <member name="F:SevenZip.LibraryFeature.Compress7z">
            <summary>
            The library can compress data to 7zip archives with LZMA method.
            </summary>
        </member>
        <member name="F:SevenZip.LibraryFeature.Compress7zLZMA2">
            <summary>
            The library can compress data to 7zip archives with LZMA2 method.
            </summary>
        </member>
        <member name="F:SevenZip.LibraryFeature.Compress7zAll">
            <summary>
            The library can compress data to 7zip archives with all methods known.
            </summary>
        </member>
        <member name="F:SevenZip.LibraryFeature.CompressTar">
            <summary>
            The library can compress data to tar archives.
            </summary>
        </member>
        <member name="F:SevenZip.LibraryFeature.CompressGzip">
            <summary>
            The library can compress data to gzip archives.
            </summary>
        </member>
        <member name="F:SevenZip.LibraryFeature.CompressBzip2">
            <summary>
            The library can compress data to bzip2 archives.
            </summary>
        </member>
        <member name="F:SevenZip.LibraryFeature.CompressXz">
            <summary>
            The library can compress data to xz archives.
            </summary>
        </member>
        <member name="F:SevenZip.LibraryFeature.CompressZip">
            <summary>
            The library can compress data to zip archives.
            </summary>
        </member>
        <member name="F:SevenZip.LibraryFeature.CompressAll">
            <summary>
            The library can compress data to all types of archives supported.
            </summary>
        </member>
        <member name="F:SevenZip.LibraryFeature.Modify">
            <summary>
            The library can modify archives.
            </summary>
        </member>
        <member name="T:SevenZip.SevenZipLibraryManager">
            <summary>
            7-zip library low-level wrapper.
            </summary>
        </member>
        <member name="F:SevenZip.SevenZipLibraryManager.SyncRoot">
            <summary>
            Synchronization root for all locking.
            </summary>
        </member>
        <member name="F:SevenZip.SevenZipLibraryManager._libraryFileName">
            <summary>
            Path to the 7-zip dll.
            </summary>
            <remarks>7zxa.dll supports only decoding from .7z archives.
            Features of 7za.dll: 
                - Supporting 7z format;
                - Built encoders: LZMA, PPMD, BCJ, BCJ2, COPY, AES-256 Encryption.
                - Built decoders: LZMA, PPMD, BCJ, BCJ2, COPY, AES-256 Encryption, BZip2, Deflate.
            7z.dll (from the 7-zip distribution) supports every InArchiveFormat for encoding and decoding.
            </remarks>
        </member>
        <member name="F:SevenZip.SevenZipLibraryManager._modulePtr">
            <summary>
            7-zip library handle.
            </summary>
        </member>
        <member name="F:SevenZip.SevenZipLibraryManager._features">
            <summary>
            7-zip library features.
            </summary>
        </member>
        <member name="M:SevenZip.SevenZipLibraryManager.LoadLibrary(System.Object,System.Enum)">
            <summary>
            Loads the 7-zip library if necessary and adds user to the reference list
            </summary>
            <param name="user">Caller of the function</param>
            <param name="format">Archive format</param>
        </member>
        <member name="P:SevenZip.SevenZipLibraryManager.ModifyCapable">
            <summary>
            Gets the value indicating whether the library supports modifying archives.
            </summary>
        </member>
        <member name="M:SevenZip.SevenZipLibraryManager.FreeLibrary(System.Object,System.Enum)">
            <summary>
            Removes user from reference list and frees the 7-zip library if it becomes empty
            </summary>
            <param name="user">Caller of the function</param>
            <param name="format">Archive format</param>
        </member>
        <member name="M:SevenZip.SevenZipLibraryManager.InArchive(SevenZip.InArchiveFormat,System.Object)">
            <summary>
            Gets IInArchive interface to extract 7-zip archives.
            </summary>
            <param name="format">Archive format.</param>
            <param name="user">Archive format user.</param>
        </member>
        <member name="M:SevenZip.SevenZipLibraryManager.OutArchive(SevenZip.OutArchiveFormat,System.Object)">
            <summary>
            Gets IOutArchive interface to pack 7-zip archives.
            </summary>
            <param name="format">Archive format.</param>  
            <param name="user">Archive format user.</param>
        </member>
        <member name="T:SevenZip.LzmaDecodeStream">
            <summary>
            The stream which decompresses data with LZMA on the fly.
            </summary>
        </member>
        <member name="M:SevenZip.LzmaDecodeStream.#ctor(System.IO.Stream)">
            <summary>
            Initializes a new instance of the LzmaDecodeStream class.
            </summary>
            <param name="encodedStream">A compressed stream.</param>
        </member>
        <member name="P:SevenZip.LzmaDecodeStream.ChunkSize">
            <summary>
            Gets the chunk size.
            </summary>
        </member>
        <member name="P:SevenZip.LzmaDecodeStream.CanRead">
            <summary>
            Gets a value indicating whether the current stream supports reading.
            </summary>
        </member>
        <member name="P:SevenZip.LzmaDecodeStream.CanSeek">
            <summary>
            Gets a value indicating whether the current stream supports seeking.
            </summary>
        </member>
        <member name="P:SevenZip.LzmaDecodeStream.CanWrite">
            <summary>
            Gets a value indicating whether the current stream supports writing.
            </summary>
        </member>
        <member name="P:SevenZip.LzmaDecodeStream.Length">
            <summary>
            Gets the length in bytes of the output stream.
            </summary>
        </member>
        <member name="P:SevenZip.LzmaDecodeStream.Position">
            <summary>
            Gets or sets the position within the output stream.
            </summary>
        </member>
        <member name="M:SevenZip.LzmaDecodeStream.Flush">
            <summary>
            Does nothing.
            </summary>
        </member>
        <member name="M:SevenZip.LzmaDecodeStream.Read(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Reads a sequence of bytes from the current stream and decompresses data if necessary.
            </summary>
            <param name="buffer">An array of bytes.</param>
            <param name="offset">The zero-based byte offset in buffer at which to begin storing the data read from the current stream.</param>
            <param name="count">The maximum number of bytes to be read from the current stream.</param>
            <returns>The total number of bytes read into the buffer.</returns>        
        </member>
        <member name="M:SevenZip.LzmaDecodeStream.Seek(System.Int64,System.IO.SeekOrigin)">
            <summary>
            Sets the position within the current stream.
            </summary>
            <param name="offset">A byte offset relative to the origin parameter.</param>
            <param name="origin">A value of type System.IO.SeekOrigin indicating the reference point used to obtain the new position.</param>
            <returns>The new position within the current stream.</returns>       
        </member>
        <member name="M:SevenZip.LzmaDecodeStream.SetLength(System.Int64)">
            <summary>
            Sets the length of the current stream.
            </summary>
            <param name="value">The desired length of the current stream in bytes.</param>
        </member>
        <member name="M:SevenZip.LzmaDecodeStream.Write(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Writes a sequence of bytes to the current stream.
            </summary>
            <param name="buffer">An array of bytes.</param>
            <param name="offset">The zero-based byte offset in buffer at which to begin storing the data read from the current stream.</param>
            <param name="count">The maximum number of bytes to be read from the current stream.</param>
        </member>
        <member name="T:SevenZip.LzmaEncodeStream">
            <summary>
            The stream which compresses data with LZMA on the fly.
            </summary>
        </member>
        <member name="M:SevenZip.LzmaEncodeStream.#ctor">
            <summary>
            Initializes a new instance of the LzmaEncodeStream class.
            </summary>
        </member>
        <member name="M:SevenZip.LzmaEncodeStream.#ctor(System.Int32)">
            <summary>
            Initializes a new instance of the LzmaEncodeStream class.
            </summary>
            <param name="bufferCapacity">The buffer size. The bigger size, the better compression.</param>
        </member>
        <member name="M:SevenZip.LzmaEncodeStream.#ctor(System.IO.Stream)">
            <summary>
            Initializes a new instance of the LzmaEncodeStream class.
            </summary>
            <param name="outputStream">An output stream which supports writing.</param>
        </member>
        <member name="M:SevenZip.LzmaEncodeStream.#ctor(System.IO.Stream,System.Int32)">
            <summary>
            Initializes a new instance of the LzmaEncodeStream class.
            </summary>
            <param name="outputStream">An output stream which supports writing.</param>
            <param name="bufferCapacity">A buffer size. The bigger size, the better compression.</param>
        </member>
        <member name="P:SevenZip.LzmaEncodeStream.CanRead">
            <summary>
            Gets a value indicating whether the current stream supports reading.
            </summary>
        </member>
        <member name="P:SevenZip.LzmaEncodeStream.CanSeek">
            <summary>
            Gets a value indicating whether the current stream supports seeking.
            </summary>
        </member>
        <member name="P:SevenZip.LzmaEncodeStream.CanWrite">
            <summary>
            Gets a value indicating whether the current stream supports writing.
            </summary>
        </member>
        <member name="P:SevenZip.LzmaEncodeStream.Length">
            <summary>
            Gets the length in bytes of the output stream.
            </summary>
        </member>
        <member name="P:SevenZip.LzmaEncodeStream.Position">
            <summary>
            Gets or sets the position within the output stream.
            </summary>
        </member>
        <member name="M:SevenZip.LzmaEncodeStream.DisposedCheck">
            <summary>
            Checked whether the class was disposed.
            </summary>
            <exception cref="T:System.ObjectDisposedException" />
        </member>
        <member name="M:SevenZip.LzmaEncodeStream.ToDecodeStream">
            <summary>
            Converts the LzmaEncodeStream to the LzmaDecodeStream to read data.
            </summary>
            <returns></returns>
        </member>
        <member name="M:SevenZip.LzmaEncodeStream.Flush">
            <summary>
            Clears all buffers for this stream and causes any buffered data to be compressed and written.
            </summary>
        </member>
        <member name="M:SevenZip.LzmaEncodeStream.Dispose(System.Boolean)">
            <summary>
            Releases all unmanaged resources used by LzmaEncodeStream.
            </summary>
        </member>
        <member name="M:SevenZip.LzmaEncodeStream.Read(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Reads a sequence of bytes from the current stream and advances the position within the stream by the number of bytes read.
            </summary>
            <param name="buffer">An array of bytes.</param>
            <param name="offset">The zero-based byte offset in buffer at which to begin storing the data read from the current stream.</param>
            <param name="count">The maximum number of bytes to be read from the current stream.</param>
            <returns>The total number of bytes read into the buffer.</returns>
        </member>
        <member name="M:SevenZip.LzmaEncodeStream.Seek(System.Int64,System.IO.SeekOrigin)">
            <summary>
            Sets the position within the current stream.
            </summary>
            <param name="offset">A byte offset relative to the origin parameter.</param>
            <param name="origin">A value of type System.IO.SeekOrigin indicating the reference point used to obtain the new position.</param>
            <returns>The new position within the current stream.</returns>
        </member>
        <member name="M:SevenZip.LzmaEncodeStream.SetLength(System.Int64)">
            <summary>
            Sets the length of the current stream.
            </summary>
            <param name="value">The desired length of the current stream in bytes.</param>
        </member>
        <member name="M:SevenZip.LzmaEncodeStream.Write(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Writes a sequence of bytes to the current stream and compresses it if necessary.
            </summary>
            <param name="buffer">An array of bytes.</param>
            <param name="offset">The zero-based byte offset in buffer at which to begin storing the data read from the current stream.</param>
            <param name="count">The maximum number of bytes to be read from the current stream.</param>
        </member>
        <member name="T:SevenZip.LzmaProgressCallback">
            <summary>
            Callback to implement the ICodeProgress interface
            </summary>
        </member>
        <member name="M:SevenZip.LzmaProgressCallback.#ctor(System.Int64,System.EventHandler{SevenZip.ProgressEventArgs})">
            <summary>
            Initializes a new instance of the LzmaProgressCallback class
            </summary>
            <param name="inSize">The input size</param>
            <param name="working">Progress event handler</param>
        </member>
        <member name="M:SevenZip.LzmaProgressCallback.SetProgress(System.Int64,System.Int64)">
            <summary>
            Sets the progress
            </summary>
            <param name="inSize">The processed input size</param>
            <param name="outSize">The processed output size</param>
        </member>
        <member name="T:SevenZip.Sdk.Buffer.InBuffer">
            <summary>
            Implements the input buffer work
            </summary>
        </member>
        <member name="M:SevenZip.Sdk.Buffer.InBuffer.#ctor(System.UInt32)">
            <summary>
            Initializes the input buffer
            </summary>
            <param name="bufferSize"></param>
        </member>
        <member name="M:SevenZip.Sdk.Buffer.InBuffer.Init(System.IO.Stream)">
            <summary>
            Initializes the class
            </summary>
            <param name="stream"></param>
        </member>
        <member name="M:SevenZip.Sdk.Buffer.InBuffer.ReadBlock">
            <summary>
            Reads the whole block
            </summary>
            <returns></returns>
        </member>
        <member name="M:SevenZip.Sdk.Buffer.InBuffer.ReleaseStream">
            <summary>
            Releases the stream
            </summary>
        </member>
        <member name="M:SevenZip.Sdk.Buffer.InBuffer.ReadByte(System.Byte@)">
            <summary>
            Reads the byte to check it
            </summary>
            <param name="b"></param>
            <returns></returns>
        </member>
        <member name="M:SevenZip.Sdk.Buffer.InBuffer.ReadByte">
            <summary>
            Reads the next byte
            </summary>
            <returns></returns>
        </member>
        <member name="M:SevenZip.Sdk.Buffer.InBuffer.GetProcessedSize">
            <summary>
            Gets processed size
            </summary>
            <returns></returns>
        </member>
        <member name="M:SevenZip.Sdk.Buffer.OutBuffer.#ctor(System.UInt32)">
            <summary>
            Initializes a new instance of the OutBuffer class
            </summary>
            <param name="bufferSize"></param>
        </member>
        <member name="T:SevenZip.Sdk.Compression.Lzma.Decoder">
            <summary>
            The LZMA decoder class
            </summary>
        </member>
        <member name="M:SevenZip.Sdk.Compression.Lzma.Decoder.#ctor">
            <summary>
            Initializes the Lzma Decoder class.
            </summary>
        </member>
        <member name="M:SevenZip.Sdk.Compression.Lzma.Decoder.Code(System.IO.Stream,System.IO.Stream,System.Int64,System.Int64,SevenZip.Sdk.ICodeProgress)">
            <summary>
            Codes a stream with LZMA algorithm to an output stream
            </summary>
            <param name="inStream">The input stream</param>
            <param name="inSize">The input size</param>
            <param name="outSize">The output size</param>
            <param name="outStream">The output stream</param>
            <param name="progress">Progress interface</param>
        </member>
        <member name="M:SevenZip.Sdk.Compression.Lzma.Decoder.SetDecoderProperties(System.Byte[])">
            <summary>
            Sets decoder properties
            </summary>
            <param name="properties">Array of byte properties</param>
        </member>
        <member name="M:SevenZip.Sdk.Compression.Lzma.Decoder.Train(System.IO.Stream)">
            <summary>
            Trains a stream
            </summary>
            <param name="stream">The stream to train.</param>
            <returns>true if Ok; otherwise, false.</returns>
        </member>
        <member name="M:SevenZip.Sdk.Compression.Lzma.Decoder.LenDecoder.Decode(SevenZip.Sdk.Compression.RangeCoder.Decoder,System.UInt32)">
            <summary>
            Decodes the stream
            </summary>
            <param name="rangeDecoder">The specified RangeCoder</param>
            <param name="posState">The position state</param>
            <returns></returns>
        </member>
        <member name="T:SevenZip.Sdk.Compression.Lzma.Encoder">
            <summary>
            The LZMA encoder class
            </summary>
        </member>
        <member name="M:SevenZip.Sdk.Compression.Lzma.Encoder.#ctor">
            <summary>
            Initializes a new instance of the Encoder class
            </summary>
        </member>
        <member name="M:SevenZip.Sdk.Compression.Lzma.Encoder.Code(System.IO.Stream,System.IO.Stream,System.Int64,System.Int64,SevenZip.Sdk.ICodeProgress)">
            <summary>
            Codes the specified stream
            </summary>
            <param name="inStream">The input stream</param>
            <param name="inSize">The input size</param>
            <param name="outSize">The output size</param>
            <param name="outStream">The output stream</param>
            <param name="progress">The progress callback</param>
        </member>
        <member name="M:SevenZip.Sdk.Compression.Lzma.Encoder.SetCoderProperties(SevenZip.Sdk.CoderPropId[],System.Object[])">
            <summary>
            Sets the coder properties
            </summary>
            <param name="propIDs">The property identificators</param>
            <param name="properties">The array of properties</param>
        </member>
        <member name="M:SevenZip.Sdk.Compression.Lzma.Encoder.WriteCoderProperties(System.IO.Stream)">
            <summary>
            Writes the coder properties
            </summary>
            <param name="outStream">The output stream to write the properties to.</param>
        </member>
        <member name="T:SevenZip.Sdk.Compression.LZ.InWindow">
            <summary>
            Input window class
            </summary>
        </member>
        <member name="F:SevenZip.Sdk.Compression.LZ.InWindow._blockSize">
            <summary>
            Size of Allocated memory block
            </summary>
        </member>
        <member name="F:SevenZip.Sdk.Compression.LZ.InWindow._bufferBase">
            <summary>
            The pointer to buffer with data
            </summary>
        </member>
        <member name="F:SevenZip.Sdk.Compression.LZ.InWindow._bufferOffset">
            <summary>
            Buffer offset value
            </summary>
        </member>
        <member name="F:SevenZip.Sdk.Compression.LZ.InWindow._keepSizeAfter">
            <summary>
            How many BYTEs must be kept buffer after _pos
            </summary>
        </member>
        <member name="F:SevenZip.Sdk.Compression.LZ.InWindow._keepSizeBefore">
            <summary>
            How many BYTEs must be kept in buffer before _pos
            </summary>
        </member>
        <member name="F:SevenZip.Sdk.Compression.LZ.InWindow._pos">
            <summary>
            Offset (from _buffer) of curent byte
            </summary>
        </member>
        <member name="F:SevenZip.Sdk.Compression.LZ.InWindow._streamPos">
            <summary>
            Offset (from _buffer) of first not read byte from Stream
            </summary>
        </member>
        <member name="M:SevenZip.Sdk.Compression.LZ.InWindow.GetMatchLen(System.Int32,System.UInt32,System.UInt32)">
            <summary>
            index + limit have not to exceed _keepSizeAfter
            </summary>
            <param name="index"></param>
            <param name="distance"></param>
            <param name="limit"></param>
            <returns></returns>
        </member>
        <member name="T:SevenZip.Sdk.DataErrorException">
            <summary>
            The exception that is thrown when an error in input stream occurs during decoding.
            </summary>
        </member>
        <member name="T:SevenZip.Sdk.InvalidParamException">
            <summary>
            The exception that is thrown when the value of an argument is outside the allowable range.
            </summary>
        </member>
        <member name="T:SevenZip.Sdk.ICodeProgress">
            <summary>
            Callback progress interface.
            </summary>
        </member>
        <member name="M:SevenZip.Sdk.ICodeProgress.SetProgress(System.Int64,System.Int64)">
            <summary>
            Callback progress.
            </summary>
            <param name="inSize">
            Processed input size. -1 if unknown.
            </param>
            <param name="outSize">
            Processed output size. -1 if unknown.
            </param>
        </member>
        <member name="T:SevenZip.Sdk.ICoder">
            <summary>
            Stream coder interface
            </summary>
        </member>
        <member name="M:SevenZip.Sdk.ICoder.Code(System.IO.Stream,System.IO.Stream,System.Int64,System.Int64,SevenZip.Sdk.ICodeProgress)">
            <summary>
            Codes streams.
            </summary>
            <param name="inStream">
            input Stream.
            </param>
            <param name="outStream">
            output Stream.
            </param>
            <param name="inSize">
            input Size. -1 if unknown.
            </param>
            <param name="outSize">
            output Size. -1 if unknown.
            </param>
            <param name="progress">
            callback progress reference.
            </param>
            <exception cref="T:SevenZip.Sdk.DataErrorException">
            if input stream is not valid
            </exception>
        </member>
        <member name="T:SevenZip.Sdk.CoderPropId">
            <summary>
            Provides the fields that represent properties idenitifiers for compressing.
            </summary>
        </member>
        <member name="F:SevenZip.Sdk.CoderPropId.DefaultProp">
            <summary>
            Specifies default property.
            </summary>
        </member>
        <member name="F:SevenZip.Sdk.CoderPropId.DictionarySize">
            <summary>
            Specifies size of dictionary.
            </summary>
        </member>
        <member name="F:SevenZip.Sdk.CoderPropId.UsedMemorySize">
            <summary>
            Specifies size of memory for PPM*.
            </summary>
        </member>
        <member name="F:SevenZip.Sdk.CoderPropId.Order">
            <summary>
            Specifies order for PPM methods.
            </summary>
        </member>
        <member name="F:SevenZip.Sdk.CoderPropId.BlockSize">
            <summary>
            Specifies Block Size.
            </summary>
        </member>
        <member name="F:SevenZip.Sdk.CoderPropId.PosStateBits">
            <summary>
            Specifies number of postion state bits for LZMA (0 &lt;= x &lt;= 4).
            </summary>
        </member>
        <member name="F:SevenZip.Sdk.CoderPropId.LitContextBits">
            <summary>
            Specifies number of literal context bits for LZMA (0 &lt;= x &lt;= 8).
            </summary>
        </member>
        <member name="F:SevenZip.Sdk.CoderPropId.LitPosBits">
            <summary>
            Specifies number of literal position bits for LZMA (0 &lt;= x &lt;= 4).
            </summary>
        </member>
        <member name="F:SevenZip.Sdk.CoderPropId.NumFastBytes">
            <summary>
            Specifies number of fast bytes for LZ*.
            </summary>
        </member>
        <member name="F:SevenZip.Sdk.CoderPropId.MatchFinder">
            <summary>
            Specifies match finder. LZMA: "BT2", "BT4" or "BT4B".
            </summary>
        </member>
        <member name="F:SevenZip.Sdk.CoderPropId.MatchFinderCycles">
            <summary>
            Specifies the number of match finder cyckes.
            </summary>
        </member>
        <member name="F:SevenZip.Sdk.CoderPropId.NumPasses">
            <summary>
            Specifies number of passes.
            </summary>
        </member>
        <member name="F:SevenZip.Sdk.CoderPropId.Algorithm">
            <summary>
            Specifies number of algorithm.
            </summary>
        </member>
        <member name="F:SevenZip.Sdk.CoderPropId.NumThreads">
            <summary>
            Specifies the number of threads.
            </summary>
        </member>
        <member name="F:SevenZip.Sdk.CoderPropId.EndMarker">
            <summary>
            Specifies mode with end marker.
            </summary>
        </member>
        <member name="T:SevenZip.Sdk.ISetCoderProperties">
            <summary>
            The ISetCoderProperties interface
            </summary>
        </member>
        <member name="T:SevenZip.Sdk.IWriteCoderProperties">
            <summary>
            The IWriteCoderProperties interface
            </summary>
        </member>
        <member name="T:SevenZip.Sdk.ISetDecoderProperties">
            <summary>
            The ISetDecoderPropertiesinterface
            </summary>
        </member>
        <member name="M:SevenZip.Sdk.ISetDecoderProperties.SetDecoderProperties(System.Byte[])">
            <summary>
            Sets decoder properties
            </summary>
            <param name="properties">Array of byte properties</param>
        </member>
        <member name="T:SevenZip.SevenZipBase">
            <summary>
            SevenZip Extractor/Compressor base class. Implements Password string, ReportErrors flag.
            </summary>
        </member>
        <member name="F:SevenZip.SevenZipBase.NeedsToBeRecreated">
            <summary>
            True if the instance of the class needs to be recreated in new thread context; otherwise, false.
            </summary>
        </member>
        <member name="P:SevenZip.SevenZipBase.EventSynchronization">
            <summary>
            Gets or sets the event synchronization strategy.
            </summary>
        </member>
        <member name="P:SevenZip.SevenZipBase.UniqueID">
            <summary>
            Gets the unique identifier of this SevenZipBase instance.
            </summary>
        </member>
        <member name="F:SevenZip.SevenZipBase._exceptions">
            <summary>
            User exceptions thrown during the requested operations, for example, in events.
            </summary>
        </member>
        <member name="M:SevenZip.SevenZipBase.#ctor(System.String)">
            <summary>
            Initializes a new instance of the SevenZipBase class.
            </summary>
            <param name="password">The archive password.</param>
        </member>
        <member name="P:SevenZip.SevenZipBase.Password">
            <summary>
            Gets or sets the archive password
            </summary>
        </member>
        <member name="P:SevenZip.SevenZipBase.ReportErrors">
            <summary>
            Gets or sets throw exceptions on archive errors flag
            </summary>
        </member>
        <member name="P:SevenZip.SevenZipBase.Exceptions">
            <summary>
            Gets the user exceptions thrown during the requested operations, for example, in events.
            </summary>
        </member>
        <member name="M:SevenZip.SevenZipBase.ThrowException(SevenZip.CallbackBase,System.Exception[])">
            <summary>
            Throws the specified exception when is able to.
            </summary>
            <param name="e">The exception to throw.</param>
            <param name="handler">The handler responsible for the exception.</param>
        </member>
        <member name="M:SevenZip.SevenZipBase.CheckedExecute(System.Int32,System.String,SevenZip.CallbackBase)">
            <summary>
            Throws exception if HRESULT != 0.
            </summary>
            <param name="hresult">Result code to check.</param>
            <param name="message">Exception message.</param>
            <param name="handler">The class responsible for the callback.</param>
        </member>
        <member name="M:SevenZip.SevenZipBase.SetLibraryPath(System.String)">
            <summary>
            Changes the path to the 7-zip native library.
            </summary>
            <param name="libraryPath">The path to the 7-zip native library.</param>
        </member>
        <member name="P:SevenZip.SevenZipBase.CurrentLibraryFeatures">
            <summary>
            Gets the current library features.
            </summary>
        </member>
        <member name="M:SevenZip.SevenZipBase.Equals(System.Object)">
            <summary>
            Determines whether the specified System.Object is equal to the current SevenZipBase.
            </summary>
            <param name="obj">The System.Object to compare with the current SevenZipBase.</param>
            <returns>true if the specified System.Object is equal to the current SevenZipBase; otherwise, false.</returns>
        </member>
        <member name="M:SevenZip.SevenZipBase.GetHashCode">
            <summary>
            Serves as a hash function for a particular type.
            </summary>
            <returns> A hash code for the current SevenZipBase.</returns>
        </member>
        <member name="M:SevenZip.SevenZipBase.ToString">
            <summary>
            Returns a System.String that represents the current SevenZipBase.
            </summary>
            <returns>A System.String that represents the current SevenZipBase.</returns>
        </member>
        <member name="T:SevenZip.SevenZipCompressor">
            <summary>
            Class to pack data into archives supported by 7-Zip.
            </summary>
            <example>
            var compr = new SevenZipCompressor();
            compr.CompressDirectory(@"C:\Dir", @"C:\Archive.7z");
            </example>
        </member>
        <member name="P:SevenZip.SevenZipCompressor.CompressionLevel">
            <summary>
            Gets or sets the archiving compression level.
            </summary>
        </member>
        <member name="P:SevenZip.SevenZipCompressor.CustomParameters">
            <summary>
            Gets the custom compression parameters - for advanced users only.
            </summary>
        </member>
        <member name="P:SevenZip.SevenZipCompressor.IncludeEmptyDirectories">
            <summary>
            Gets or sets the value indicating whether to include empty directories to archives. Default is true.
            </summary>
        </member>
        <member name="P:SevenZip.SevenZipCompressor.PreserveDirectoryRoot">
            <summary>
            Gets or sets the value indicating whether to preserve the directory root for CompressDirectory.
            </summary>
        </member>
        <member name="P:SevenZip.SevenZipCompressor.DirectoryStructure">
            <summary>
            Gets or sets the value indicating whether to preserve the directory structure.
            </summary>
        </member>
        <member name="P:SevenZip.SevenZipCompressor.CompressionMode">
            <summary>
            Gets or sets the compression mode.
            </summary>
        </member>
        <member name="P:SevenZip.SevenZipCompressor.EncryptHeaders">
            <summary>
            Gets or sets the value indicating whether to encrypt 7-Zip archive headers.
            </summary>
        </member>
        <member name="P:SevenZip.SevenZipCompressor.ScanOnlyWritable">
            <summary>
            Gets or sets the value indicating whether to compress files only open for writing.
            </summary>
        </member>
        <member name="P:SevenZip.SevenZipCompressor.ZipEncryptionMethod">
            <summary>
            Gets or sets the encryption method for zip archives.
            </summary>
        </member>
        <member name="P:SevenZip.SevenZipCompressor.TempFolderPath">
            <summary>
            Gets or sets the temporary folder path.
            </summary>
        </member>
        <member name="P:SevenZip.SevenZipCompressor.DefaultItemName">
            <summary>
            Gets or sets the default archive item name used when an item to be compressed has no name, 
            for example, when you compress a MemoryStream instance.
            </summary>
        </member>
        <member name="P:SevenZip.SevenZipCompressor.FastCompression">
            <summary>
            Gets or sets the value indicating whether to compress as fast as possible, without calling events.
            </summary>
        </member>
        <member name="M:SevenZip.SevenZipCompressor.#ctor">
            <summary>
            Initializes a new instance of the SevenZipCompressor class. 
            </summary>
        </member>
        <member name="M:SevenZip.SevenZipCompressor.#ctor(System.String)">
            <summary>
            Initializes a new instance of the SevenZipCompressor class. 
            </summary>
            <param name="temporaryPath">Your own temporary path (default is set in the parameterless constructor overload.)</param>
        </member>
        <member name="M:SevenZip.SevenZipCompressor.ValidateStream(System.IO.Stream)">
            <summary>
            Checks if the specified stream supports compression.
            </summary>
            <param name="stream">The stream to check.</param>
        </member>
        <member name="M:SevenZip.SevenZipCompressor.MethodIsValid(SevenZip.CompressionMethod)">
            <summary>
            Guaranties the correct work of the SetCompressionProperties function
            </summary>
            <param name="method">The compression method to check</param>
            <returns>The value indicating whether the specified method is valid for the current ArchiveFormat</returns>
        </member>
        <member name="M:SevenZip.SevenZipCompressor.SetCompressionProperties">
            <summary>
            Sets the compression properties
            </summary>
        </member>
        <member name="M:SevenZip.SevenZipCompressor.CommonRoot(System.Collections.Generic.ICollection{System.String})">
            <summary>
            Finds the common root of file names
            </summary>
            <param name="files">Array of file names</param>
            <returns>Common root</returns>
        </member>
        <member name="M:SevenZip.SevenZipCompressor.CheckCommonRoot(System.Collections.Generic.IReadOnlyList{System.String},System.Int32@)">
            <summary>
            Validates the common root
            </summary>
            <param name="commonRootLength">The length of the common root of the file names.</param>
            <param name="files">Array of file names</param>
        </member>
        <member name="M:SevenZip.SevenZipCompressor.RecursiveDirectoryEmptyCheck(System.String)">
            <summary>
            Ensures that directory directory is not empty
            </summary>
            <param name="directory">Directory name</param>
            <returns>False if is not empty</returns>
        </member>
        <member name="M:SevenZip.SevenZipCompressor.ProduceFileInfoArray(System.Collections.Generic.IReadOnlyList{System.String},System.Int32,System.Boolean,System.Boolean)">
            <summary>
            Makes special FileInfo array for the archive file table.
            </summary>
            <param name="files">Array of files to pack.</param>
            <param name="commonRootLength">The length of the common root of file names</param>
            <param name="directoryCompress">The value indicating whether to produce the array for files in a particular directory or just for an array of files.</param>
            <param name="directoryStructure">Preserve directory structure.</param>
            <returns>Special FileInfo array for the archive file table.</returns>
        </member>
        <member name="M:SevenZip.SevenZipCompressor.AddFilesFromDirectory(System.String,System.Collections.Generic.ICollection{System.String},System.String)">
            <summary>
            Recursive function for adding files in directory
            </summary>
            <param name="directory">Directory directory</param>
            <param name="files">List of files</param>
            <param name="searchPattern">Search string, such as "*.txt"</param>
        </member>
        <member name="M:SevenZip.SevenZipCompressor.CommonUpdateCallbackInit(SevenZip.ArchiveUpdateCallback)">
            <summary>
            Performs the common ArchiveUpdateCallback initialization.
            </summary>
            <param name="auc">The ArchiveUpdateCallback instance to initialize.</param>
        </member>
        <member name="M:SevenZip.SevenZipCompressor.GetArchiveUpdateCallback(System.IO.FileInfo[],System.Int32,System.String)">
            <summary>
            Produces  a new instance of ArchiveUpdateCallback class.
            </summary>
            <param name="files">Array of FileInfo - files to pack</param>
            <param name="rootLength">Length of the common root of file names</param>
            <param name="password">The archive password</param>
            <returns></returns>
        </member>
        <member name="M:SevenZip.SevenZipCompressor.GetArchiveUpdateCallback(System.IO.Stream,System.String)">
            <summary>
            Produces  a new instance of ArchiveUpdateCallback class.
            </summary>
            <param name="inStream">The archive input stream.</param>
            <param name="password">The archive password.</param>
            <returns></returns>
        </member>
        <member name="M:SevenZip.SevenZipCompressor.GetArchiveUpdateCallback(System.Collections.Generic.IDictionary{System.String,System.IO.Stream},System.String)">
            <summary>
            Produces  a new instance of ArchiveUpdateCallback class.
            </summary>
            <param name="streamDict">Dictionary&lt;name of the archive entry, stream&gt;.</param>
            <param name="password">The archive password</param>
            <returns></returns>
        </member>
        <member name="E:SevenZip.SevenZipCompressor.FileCompressionStarted">
            <summary>
            Occurs when the next file is going to be packed.
            </summary>
            <remarks>Occurs when 7-zip engine requests for an input stream for the next file to pack it</remarks>
        </member>
        <member name="E:SevenZip.SevenZipCompressor.FileCompressionFinished">
            <summary>
            Occurs when the current file was compressed.
            </summary>
        </member>
        <member name="E:SevenZip.SevenZipCompressor.Compressing">
            <summary>
            Occurs when data are being compressed
            </summary>
            <remarks>Use this event for accurate progress handling and various ProgressBar.StepBy(e.PercentDelta) routines</remarks>
        </member>
        <member name="E:SevenZip.SevenZipCompressor.FilesFound">
            <summary>
            Occurs when all files information was determined and SevenZipCompressor is about to start to compress them.
            </summary>
            <remarks>The incoming int value indicates the number of scanned files.</remarks>
        </member>
        <member name="E:SevenZip.SevenZipCompressor.CompressionFinished">
            <summary>
            Occurs when the compression procedure is finished
            </summary>
        </member>
        <member name="M:SevenZip.SevenZipCompressor.FileCompressionStartedEventProxy(System.Object,SevenZip.FileNameEventArgs)">
            <summary>
            Event proxy for FileCompressionStarted.
            </summary>
            <param name="sender">The sender of the event.</param>
            <param name="e">The event arguments.</param>
        </member>
        <member name="M:SevenZip.SevenZipCompressor.FileCompressionFinishedEventProxy(System.Object,System.EventArgs)">
            <summary>
            Event proxy for FileCompressionFinished.
            </summary>
            <param name="sender">The sender of the event.</param>
            <param name="e">The event arguments.</param>
        </member>
        <member name="M:SevenZip.SevenZipCompressor.CompressingEventProxy(System.Object,SevenZip.ProgressEventArgs)">
            <summary>
            Event proxy for Compressing.
            </summary>
            <param name="sender">The sender of the event.</param>
            <param name="e">The event arguments.</param>
        </member>
        <member name="M:SevenZip.SevenZipCompressor.FilesFoundEventProxy(System.Object,SevenZip.IntEventArgs)">
            <summary>
            Event proxy for FilesFound.
            </summary>
            <param name="sender">The sender of the event.</param>
            <param name="e">The event arguments.</param>
        </member>
        <member name="P:SevenZip.SevenZipCompressor.ArchiveFormat">
            <summary>
            Gets or sets the archive format
            </summary>
        </member>
        <member name="P:SevenZip.SevenZipCompressor.CompressionMethod">
            <summary>
            Gets or sets the compression method
            </summary>
        </member>
        <member name="P:SevenZip.SevenZipCompressor.VolumeSize">
            <summary>
            Gets or sets the size in bytes of an archive volume (0 for no volumes).
            </summary>
        </member>
        <member name="M:SevenZip.SevenZipCompressor.CompressFiles(System.String,System.String[])">
            <summary>
            Packs files into the archive.
            </summary>
            <param name="fileFullNames">Array of file names to pack.</param>
            <param name="archiveName">The archive file name.</param>
        </member>
        <member name="M:SevenZip.SevenZipCompressor.CompressFiles(System.IO.Stream,System.String[])">
            <summary>
            Packs files into the archive.
            </summary>
            <param name="fileFullNames">Array of file names to pack.</param>
            <param name="archiveStream">The archive output stream. 
            Use CompressFiles(string archiveName ... ) overloads for archiving to disk.</param>       
        </member>
        <member name="M:SevenZip.SevenZipCompressor.CompressFiles(System.String,System.Int32,System.String[])">
            <summary>
            Packs files into the archive.
            </summary>
            <param name="fileFullNames">Array of file names to pack.</param>
            <param name="commonRootLength">The length of the common root of the file names.</param>
            <param name="archiveName">The archive file name.</param>
        </member>
        <member name="M:SevenZip.SevenZipCompressor.CompressFiles(System.IO.Stream,System.Int32,System.String[])">
            <summary>
            Packs files into the archive.
            </summary>
            <param name="fileFullNames">Array of file names to pack.</param>
            <param name="commonRootLength">The length of the common root of the file names.</param>
            <param name="archiveStream">The archive output stream.
            Use CompressFiles(string archiveName, ... ) overloads for archiving to disk.</param>
        </member>
        <member name="M:SevenZip.SevenZipCompressor.CompressFilesEncrypted(System.String,System.String,System.String[])">
            <summary>
            Packs files into the archive.
            </summary>
            <param name="fileFullNames">Array of file names to pack.</param>
            <param name="archiveName">The archive file name.</param>
            <param name="password">The archive password.</param>
        </member>
        <member name="M:SevenZip.SevenZipCompressor.CompressFilesEncrypted(System.IO.Stream,System.String,System.String[])">
            <summary>
            Packs files into the archive.
            </summary>
            <param name="fileFullNames">Array of file names to pack.</param>
            <param name="archiveStream">The archive output stream.
            Use CompressFiles( ... string archiveName ... ) overloads for archiving to disk.</param>
            <param name="password">The archive password.</param>
        </member>
        <member name="M:SevenZip.SevenZipCompressor.CompressFilesEncrypted(System.String,System.Int32,System.String,System.String[])">
            <summary>
            Packs files into the archive.
            </summary>
            <param name="fileFullNames">Array of file names to pack.</param>
            <param name="commonRootLength">The length of the common root of the file names.</param>
            <param name="archiveName">The archive file name.</param>
            <param name="password">The archive password.</param>
        </member>
        <member name="M:SevenZip.SevenZipCompressor.CompressFilesEncrypted(System.IO.Stream,System.Int32,System.String,System.String[])">
            <summary>
            Packs files into the archive.
            </summary>
            <param name="fileFullNames">Array of file names to pack.</param>
            <param name="commonRootLength">The length of the common root of the file names.</param>
            <param name="archiveStream">The archive output stream.
            Use CompressFiles( ... string archiveName ... ) overloads for archiving to disk.</param>
            <param name="password">The archive password.</param>
        </member>
        <member name="M:SevenZip.SevenZipCompressor.CompressDirectory(System.String,System.String,System.String,System.String,System.Boolean)">
            <summary>
            Packs all files in the specified directory.
            </summary>
            <param name="directory">The directory to compress.</param>
            <param name="archiveName">The archive file name.</param>
            <param name="password">The archive password.</param>
            <param name="searchPattern">Search string, such as "*.txt".</param>
            <param name="recursion">If true, files will be searched for recursively; otherwise, not.</param>
        </member>
        <member name="M:SevenZip.SevenZipCompressor.CompressDirectory(System.String,System.IO.Stream,System.String,System.String,System.Boolean)">
            <summary>
            Packs all files in the specified directory.
            </summary>
            <param name="directory">The directory to compress.</param>
            <param name="archiveStream">The archive output stream.
            Use CompressDirectory( ... string archiveName ... ) overloads for archiving to disk.</param>        
            <param name="password">The archive password.</param>
            <param name="searchPattern">Search string, such as "*.txt".</param>
            <param name="recursion">If true, files will be searched for recursively; otherwise, not.</param>
        </member>
        <member name="M:SevenZip.SevenZipCompressor.CompressFileDictionary(System.Collections.Generic.IDictionary{System.String,System.String},System.String,System.String)">
            <summary>
            Packs the specified file dictionary.
            </summary>
            <param name="fileDictionary">Dictionary&lt;name of the archive entry, file name&gt;.
            If a file name is null, the corresponding archive entry becomes a directory.</param>
            <param name="archiveName">The archive file name.</param>
            <param name="password">The archive password.</param>
        </member>
        <member name="M:SevenZip.SevenZipCompressor.CompressFileDictionary(System.Collections.Generic.IDictionary{System.String,System.String},System.IO.Stream,System.String)">
            <summary>
            Packs the specified file dictionary.
            </summary>
            <param name="fileDictionary">Dictionary&lt;name of the archive entry, file name&gt;.
            If a file name is null, the corresponding archive entry becomes a directory.</param>
            <param name="archiveStream">The archive output stream.
            Use CompressStreamDictionary( ... string archiveName ... ) overloads for archiving to disk.</param>
            <param name="password">The archive password.</param>
        </member>
        <member name="M:SevenZip.SevenZipCompressor.CompressStreamDictionary(System.Collections.Generic.IDictionary{System.String,System.IO.Stream},System.String,System.String)">
            <summary>
            Packs the specified stream dictionary.
            </summary>
            <param name="streamDictionary">Dictionary&lt;name of the archive entry, stream&gt;.
            If a stream is null, the corresponding string becomes a directory name.</param>
            <param name="archiveName">The archive file name.</param>
            <param name="password">The archive password.</param>
        </member>
        <member name="M:SevenZip.SevenZipCompressor.CompressStreamDictionary(System.Collections.Generic.IDictionary{System.String,System.IO.Stream},System.IO.Stream,System.String)">
            <summary>
            Packs the specified stream dictionary.
            </summary>
            <param name="streamDictionary">Dictionary&lt;name of the archive entry, stream&gt;.
            If a stream is null, the corresponding string becomes a directory name.</param>
            <param name="archiveStream">The archive output stream.
            Use CompressStreamDictionary( ... string archiveName ... ) overloads for archiving to disk.</param>
            <param name="password">The archive password.</param>
        </member>
        <member name="M:SevenZip.SevenZipCompressor.CompressStream(System.IO.Stream,System.IO.Stream,System.String)">
            <summary>
            Compresses the specified stream.
            </summary>
            <param name="inStream">The source uncompressed stream.</param>
            <param name="outStream">The destination compressed stream.</param>
            <param name="password">The archive password.</param>
            <exception cref="T:System.ArgumentException">ArgumentException: at least one of the specified streams is invalid.</exception>
        </member>
        <member name="M:SevenZip.SevenZipCompressor.ModifyArchive(System.String,System.Collections.Generic.IDictionary{System.Int32,System.String},System.String)">
            <summary>
            Modifies the existing archive (renames files or deletes them).
            </summary>
            <param name="archiveName">The archive file name.</param>
            <param name="newFileNames">New file names. Null value to delete the corresponding index.</param>
            <param name="password">The archive password.</param>
        </member>
        <member name="P:SevenZip.SevenZipCompressor.LzmaDictionarySize">
            <summary>
            Gets or sets the dictionary size for the managed LZMA algorithm.
            </summary>
        </member>
        <member name="M:SevenZip.SevenZipCompressor.CompressStream(System.IO.Stream,System.IO.Stream,System.Nullable{System.Int32},System.EventHandler{SevenZip.ProgressEventArgs})">
            <summary>
            Compresses the specified stream with LZMA algorithm (C# inside)
            </summary>
            <param name="inStream">The source uncompressed stream</param>
            <param name="outStream">The destination compressed stream</param>
            <param name="inLength">The length of uncompressed data (null for inStream.Length)</param>
            <param name="codeProgressEvent">The event for handling the code progress</param>
        </member>
        <member name="M:SevenZip.SevenZipCompressor.CompressBytes(System.Byte[])">
            <summary>
            Compresses byte array with LZMA algorithm (C# inside)
            </summary>
            <param name="data">Byte array to compress</param>
            <returns>Compressed byte array</returns>
        </member>
        <member name="M:SevenZip.SevenZipCompressor.GetFullFilePaths(System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Ensures an array of file names is the full path to that file.
            </summary>
            <param name="fileFullNames">Array of file names.</param>
            <returns>Array of file names with full paths.</returns>
        </member>
        <member name="M:SevenZip.SevenZipCompressor.UpdateCompressorPassword(System.String)">
            <summary>
            Check and update password in SevenZipCompressor
            </summary>
            <param name="password">The password to use.</param>
        </member>
        <member name="M:SevenZip.SevenZipCompressor.BeginCompressFiles(System.String,System.String[])">
            <summary>
            Packs files into the archive asynchronously.
            </summary>
            <param name="fileFullNames">Array of file names to pack.</param>
            <param name="archiveName">The archive file name.</param>
        </member>
        <member name="M:SevenZip.SevenZipCompressor.BeginCompressFiles(System.IO.Stream,System.String[])">
            <summary>
            Packs files into the archive asynchronously.
            </summary>
            <param name="fileFullNames">Array of file names to pack.</param>
            <param name="archiveStream">The archive output stream. 
            Use CompressFiles(string archiveName ... ) overloads for archiving to disk.</param>
        </member>
        <member name="M:SevenZip.SevenZipCompressor.BeginCompressFiles(System.String,System.Int32,System.String[])">
            <summary>
            Packs files into the archive asynchronously.
            </summary>
            <param name="fileFullNames">Array of file names to pack.</param>
            <param name="commonRootLength">The length of the common root of the file names.</param>
            <param name="archiveName">The archive file name.</param>
        </member>
        <member name="M:SevenZip.SevenZipCompressor.BeginCompressFiles(System.IO.Stream,System.Int32,System.String[])">
            <summary>
            Packs files into the archive asynchronously.
            </summary>
            <param name="fileFullNames">Array of file names to pack.</param>
            <param name="commonRootLength">The length of the common root of the file names.</param>
            <param name="archiveStream">The archive output stream.
            Use CompressFiles(string archiveName, ... ) overloads for archiving to disk.</param>
        </member>
        <member name="M:SevenZip.SevenZipCompressor.BeginCompressFilesEncrypted(System.String,System.String,System.String[])">
            <summary>
            Packs files into the archive asynchronously.
            </summary>
            <param name="fileFullNames">Array of file names to pack.</param>
            <param name="archiveName">The archive file name</param>
            <param name="password">The archive password.</param>
        </member>
        <member name="M:SevenZip.SevenZipCompressor.BeginCompressFilesEncrypted(System.IO.Stream,System.String,System.String[])">
            <summary>
            Packs files into the archive asynchronously.
            </summary>
            <param name="fileFullNames">Array of file names to pack.</param>
            <param name="archiveStream">The archive output stream.
            Use CompressFiles( ... string archiveName ... ) overloads for archiving to disk.</param>
            <param name="password">The archive password.</param>
        </member>
        <member name="M:SevenZip.SevenZipCompressor.BeginCompressFilesEncrypted(System.String,System.Int32,System.String,System.String[])">
            <summary>
            Packs files into the archive asynchronously.
            </summary>
            <param name="fileFullNames">Array of file names to pack.</param>
            <param name="archiveName">The archive file name</param>
            <param name="password">The archive password.</param>
            <param name="commonRootLength">The length of the common root of the file names.</param>
        </member>
        <member name="M:SevenZip.SevenZipCompressor.BeginCompressFilesEncrypted(System.IO.Stream,System.Int32,System.String,System.String[])">
            <summary>
            Packs files into the archive asynchronously.
            </summary>
            <param name="fileFullNames">Array of file names to pack.</param>
            <param name="archiveStream">The archive output stream.
            Use CompressFiles( ... string archiveName ... ) overloads for archiving to disk.</param>
            <param name="password">The archive password.</param>
            <param name="commonRootLength">The length of the common root of the file names.</param>
        </member>
        <member name="M:SevenZip.SevenZipCompressor.CompressFilesAsync(System.String,System.String[])">
            <summary>
            Packs files into the archive asynchronously.
            </summary>
            <param name="fileFullNames">Array of file names to pack.</param>
            <param name="archiveName">The archive file name.</param>
        </member>
        <member name="M:SevenZip.SevenZipCompressor.CompressFilesAsync(System.IO.Stream,System.String[])">
            <summary>
            Packs files into the archive asynchronously.
            </summary>
            <param name="fileFullNames">Array of file names to pack.</param>
            <param name="archiveStream">The archive output stream. 
            Use CompressFiles(string archiveName ... ) overloads for archiving to disk.</param>
        </member>
        <member name="M:SevenZip.SevenZipCompressor.CompressFilesAsync(System.String,System.Int32,System.String[])">
            <summary>
            Packs files into the archive asynchronously.
            </summary>
            <param name="fileFullNames">Array of file names to pack.</param>
            <param name="commonRootLength">The length of the common root of the file names.</param>
            <param name="archiveName">The archive file name.</param>
        </member>
        <member name="M:SevenZip.SevenZipCompressor.CompressFilesAsync(System.IO.Stream,System.Int32,System.String[])">
            <summary>
            Packs files into the archive asynchronously.
            </summary>
            <param name="fileFullNames">Array of file names to pack.</param>
            <param name="commonRootLength">The length of the common root of the file names.</param>
            <param name="archiveStream">The archive output stream.
            Use CompressFiles(string archiveName, ... ) overloads for archiving to disk.</param>
        </member>
        <member name="M:SevenZip.SevenZipCompressor.CompressFilesEncryptedAsync(System.String,System.String,System.String[])">
            <summary>
            Packs files into the archive asynchronously.
            </summary>
            <param name="fileFullNames">Array of file names to pack.</param>
            <param name="archiveName">The archive file name</param>
            <param name="password">The archive password.</param>
        </member>
        <member name="M:SevenZip.SevenZipCompressor.CompressFilesEncryptedAsync(System.IO.Stream,System.String,System.String[])">
            <summary>
            Packs files into the archive asynchronously.
            </summary>
            <param name="fileFullNames">Array of file names to pack.</param>
            <param name="archiveStream">The archive output stream.
            Use CompressFiles( ... string archiveName ... ) overloads for archiving to disk.</param>
            <param name="password">The archive password.</param>
        </member>
        <member name="M:SevenZip.SevenZipCompressor.CompressFilesEncryptedAsync(System.String,System.Int32,System.String,System.String[])">
            <summary>
            Packs files into the archive asynchronously.
            </summary>
            <param name="fileFullNames">Array of file names to pack.</param>
            <param name="archiveName">The archive file name</param>
            <param name="password">The archive password.</param>
            <param name="commonRootLength">The length of the common root of the file names.</param>
        </member>
        <member name="M:SevenZip.SevenZipCompressor.CompressFilesEncryptedAsync(System.IO.Stream,System.Int32,System.String,System.String[])">
            <summary>
            Packs files into the archive asynchronously.
            </summary>
            <param name="fileFullNames">Array of file names to pack.</param>
            <param name="archiveStream">The archive output stream.
            Use CompressFiles( ... string archiveName ... ) overloads for archiving to disk.</param>
            <param name="password">The archive password.</param>
            <param name="commonRootLength">The length of the common root of the file names.</param>
        </member>
        <member name="M:SevenZip.SevenZipCompressor.BeginCompressDirectory(System.String,System.String,System.String,System.String,System.Boolean)">
            <summary>
            Packs all files in the specified directory asynchronously.
            </summary>
            <param name="directory">The directory to compress.</param>
            <param name="archiveName">The archive file name.</param>        
            <param name="password">The archive password.</param>
            <param name="searchPattern">Search string, such as "*.txt".</param>
            <param name="recursion">If true, files will be searched for recursively; otherwise, not.</param>
        </member>
        <member name="M:SevenZip.SevenZipCompressor.BeginCompressDirectory(System.String,System.IO.Stream,System.String,System.String,System.Boolean)">
            <summary>
            Packs all files in the specified directory asynchronously.
            </summary>
            <param name="directory">The directory to compress.</param>
            <param name="archiveStream">The archive output stream.
            Use CompressDirectory( ... string archiveName ... ) overloads for archiving to disk.</param>        
            <param name="password">The archive password.</param>
            <param name="searchPattern">Search string, such as "*.txt".</param>
            <param name="recursion">If true, files will be searched for recursively; otherwise, not.</param>
        </member>
        <member name="M:SevenZip.SevenZipCompressor.CompressDirectoryAsync(System.String,System.String,System.String,System.String,System.Boolean)">
            <summary>
            Packs all files in the specified directory asynchronously.
            </summary>
            <param name="directory">The directory to compress.</param>
            <param name="archiveName">The archive file name.</param>        
            <param name="password">The archive password.</param>
            <param name="searchPattern">Search string, such as "*.txt".</param>
            <param name="recursion">If true, files will be searched for recursively; otherwise, not.</param>
        </member>
        <member name="M:SevenZip.SevenZipCompressor.CompressDirectoryAsync(System.String,System.IO.Stream,System.String,System.String,System.Boolean)">
            <summary>
            Packs all files in the specified directory asynchronously.
            </summary>
            <param name="directory">The directory to compress.</param>
            <param name="archiveStream">The archive output stream.
            Use CompressDirectory( ... string archiveName ... ) overloads for archiving to disk.</param>        
            <param name="password">The archive password.</param>
            <param name="searchPattern">Search string, such as "*.txt".</param>
            <param name="recursion">If true, files will be searched for recursively; otherwise, not.</param>
        </member>
        <member name="M:SevenZip.SevenZipCompressor.BeginCompressStream(System.IO.Stream,System.IO.Stream,System.String)">
            <summary>
            Compresses the specified stream.
            </summary>
            <param name="inStream">The source uncompressed stream.</param>
            <param name="outStream">The destination compressed stream.</param>
            <param name="password">The archive password.</param>
            <exception cref="T:System.ArgumentException">ArgumentException: at least one of the specified streams is invalid.</exception>
        </member>
        <member name="M:SevenZip.SevenZipCompressor.CompressStreamAsync(System.IO.Stream,System.IO.Stream,System.String)">
            <summary>
            Compresses the specified stream.
            </summary>
            <param name="inStream">The source uncompressed stream.</param>
            <param name="outStream">The destination compressed stream.</param>
            <param name="password">The archive password.</param>
            <exception cref="T:System.ArgumentException">ArgumentException: at least one of the specified streams is invalid.</exception>
        </member>
        <member name="M:SevenZip.SevenZipCompressor.BeginModifyArchive(System.String,System.Collections.Generic.IDictionary{System.Int32,System.String},System.String)">
            <summary>
            Modifies the existing archive asynchronously (renames files or deletes them).
            </summary>
            <param name="archiveName">The archive file name.</param>
            <param name="newFileNames">New file names. Null value to delete the corresponding index.</param>
            <param name="password">The archive password.</param>
        </member>
        <member name="M:SevenZip.SevenZipCompressor.ModifyArchiveAsync(System.String,System.Collections.Generic.IDictionary{System.Int32,System.String},System.String)">
            <summary>
            Modifies the existing archive asynchronously (renames files or deletes them).
            </summary>
            <param name="archiveName">The archive file name.</param>
            <param name="newFileNames">New file names. Null value to delete the corresponding index.</param>
            <param name="password">The archive password.</param>
        </member>
        <member name="T:SevenZip.SevenZipExtractor">
            <summary>
            Class to unpack data from archives supported by 7-Zip.
            </summary>
            <example>
            using (var extr = new SevenZipExtractor(@"C:\Test.7z"))
            {
                extr.ExtractArchive(@"C:\TestDirectory");
            }
            </example>
        </member>
        <member name="F:SevenZip.SevenZipExtractor._asynchronousDisposeLock">
            <summary>
            This is used to lock possible Dispose() calls.
            </summary>
        </member>
        <member name="M:SevenZip.SevenZipExtractor.Init(System.String)">
            <summary>
            General initialization function.
            </summary>
            <param name="archiveFullName">The archive file name.</param>
        </member>
        <member name="M:SevenZip.SevenZipExtractor.Init(System.IO.Stream)">
            <summary>
            General initialization function.
            </summary>
            <param name="stream">The stream to read the archive from.</param>
        </member>
        <member name="M:SevenZip.SevenZipExtractor.#ctor(System.IO.Stream)">
            <summary>
            Initializes a new instance of SevenZipExtractor class.
            </summary>
            <param name="archiveStream">The stream to read the archive from.
            Use SevenZipExtractor(string) to extract from disk, though it is not necessary.</param>
            <remarks>The archive format is guessed by the signature.</remarks>
        </member>
        <member name="M:SevenZip.SevenZipExtractor.#ctor(System.IO.Stream,System.Boolean)">
            <summary>
            Initializes a new instance of SevenZipExtractor class.
            </summary>
            <param name="archiveStream">The stream to read the archive from.
            Use SevenZipExtractor(string) to extract from disk, though it is not necessary.</param>
            <param name="leaveOpen">Leaves the base stream open.</param>
            <remarks>The archive format is guessed by the signature.</remarks>
        </member>
        <member name="M:SevenZip.SevenZipExtractor.#ctor(System.IO.Stream,System.Boolean,SevenZip.InArchiveFormat)">
            <summary>
            Initializes a new instance of SevenZipExtractor class.
            </summary>
            <param name="archiveStream">The stream to read the archive from.
            Use SevenZipExtractor(string) to extract from disk, though it is not necessary.</param>
            <param name="leaveOpen">Leaves the base stream open.</param>        
            <param name="format">Manual archive format setup. You SHOULD NOT normally specify it this way.
            Instead, use SevenZipExtractor(Stream archiveStream), that constructor
            automatically detects the archive format.</param>
        </member>
        <member name="M:SevenZip.SevenZipExtractor.#ctor(System.String)">
            <summary>
            Initializes a new instance of SevenZipExtractor class.
            </summary>
            <param name="archiveFullName">The archive full file name.</param>
        </member>
        <member name="M:SevenZip.SevenZipExtractor.#ctor(System.String,SevenZip.InArchiveFormat)">
            <summary>
            Initializes a new instance of SevenZipExtractor class.
            </summary>
            <param name="archiveFullName">The archive full file name.</param>
            <param name="format">Manual archive format setup. You SHOULD NOT normally specify it this way.
            Instead, use SevenZipExtractor(string archiveFullName), that constructor
            automatically detects the archive format.</param>
        </member>
        <member name="M:SevenZip.SevenZipExtractor.#ctor(System.String,System.String)">
            <summary>
            Initializes a new instance of SevenZipExtractor class.
            </summary>
            <param name="archiveFullName">The archive full file name.</param>
            <param name="password">Password for an encrypted archive.</param>
        </member>
        <member name="M:SevenZip.SevenZipExtractor.#ctor(System.String,System.String,SevenZip.InArchiveFormat)">
            <summary>
            Initializes a new instance of SevenZipExtractor class.
            </summary>
            <param name="archiveFullName">The archive full file name.</param>
            <param name="password">Password for an encrypted archive.</param>
            <param name="format">Manual archive format setup. You SHOULD NOT normally specify it this way.
            Instead, use SevenZipExtractor(string archiveFullName, string password), that constructor
            automatically detects the archive format.</param>
        </member>
        <member name="M:SevenZip.SevenZipExtractor.#ctor(System.IO.Stream,System.String)">
            <summary>
            Initializes a new instance of SevenZipExtractor class.
            </summary>
            <param name="archiveStream">The stream to read the archive from.</param>
            <param name="password">Password for an encrypted archive.</param>
            <remarks>The archive format is guessed by the signature.</remarks>
        </member>
        <member name="M:SevenZip.SevenZipExtractor.#ctor(System.IO.Stream,System.String,System.Boolean)">
            <summary>
            Initializes a new instance of SevenZipExtractor class.
            </summary>
            <param name="archiveStream">The stream to read the archive from.</param>
            <param name="password">Password for an encrypted archive.</param>
            <param name="leaveOpen">Leaves the base stream open.</param>
            <remarks>The archive format is guessed by the signature.</remarks>
        </member>
        <member name="M:SevenZip.SevenZipExtractor.#ctor(System.IO.Stream,System.String,System.Boolean,SevenZip.InArchiveFormat)">
            <summary>
            Initializes a new instance of SevenZipExtractor class.
            </summary>
            <param name="archiveStream">The stream to read the archive from.</param>
            <param name="password">Password for an encrypted archive.</param>
            <param name="leaveOpen">Leaves the base stream open.</param>
            <param name="format">Manual archive format setup. You SHOULD NOT normally specify it this way.
            Instead, use SevenZipExtractor(Stream archiveStream, string password), that constructor
            automatically detects the archive format.</param>
        </member>
        <member name="P:SevenZip.SevenZipExtractor.FileName">
            <summary>
            Gets or sets archive full file name
            </summary>
        </member>
        <member name="P:SevenZip.SevenZipExtractor.PackedSize">
            <summary>
            Gets the size of the archive file
            </summary>
        </member>
        <member name="P:SevenZip.SevenZipExtractor.UnpackedSize">
            <summary>
            Gets the size of unpacked archive data
            </summary>
        </member>
        <member name="P:SevenZip.SevenZipExtractor.IsSolid">
            <summary>
            Gets a value indicating whether the archive is solid
            </summary>
        </member>
        <member name="P:SevenZip.SevenZipExtractor.FilesCount">
            <summary>
            Gets the number of files in the archive
            </summary>
        </member>
        <member name="P:SevenZip.SevenZipExtractor.Format">
            <summary>
            Gets archive format
            </summary>
        </member>
        <member name="P:SevenZip.SevenZipExtractor.PreserveDirectoryStructure">
            <summary>
            Gets or sets the value indicating whether to preserve the directory structure of extracted files.
            </summary>
        </member>
        <member name="M:SevenZip.SevenZipExtractor.DisposedCheck">
            <summary>
            Checked whether the class was disposed.
            </summary>
            <exception cref="T:System.ObjectDisposedException" />
        </member>
        <member name="M:SevenZip.SevenZipExtractor.GetArchiveStream(System.Boolean)">
            <summary>
            Gets the archive input stream.
            </summary>
            <returns>The archive input wrapper stream.</returns>
        </member>
        <member name="M:SevenZip.SevenZipExtractor.OpenArchiveInner(SevenZip.IInStream,SevenZip.IArchiveOpenCallback)">
            <summary>
            Opens the archive and throws exceptions or returns OperationResult.DataError if any error occurs.
            </summary>       
            <param name="archiveStream">The IInStream compliant class instance, that is, the input stream.</param>
            <param name="openCallback">The ArchiveOpenCallback instance.</param>
            <returns>OperationResult.Ok if Open() succeeds.</returns>
        </member>
        <member name="M:SevenZip.SevenZipExtractor.OpenArchive(SevenZip.IInStream,SevenZip.ArchiveOpenCallback)">
            <summary>
            Opens the archive and throws exceptions or returns OperationResult.DataError if any error occurs.
            </summary>
            <param name="archiveStream">The IInStream compliant class instance, that is, the input stream.</param>
            <param name="openCallback">The ArchiveOpenCallback instance.</param>
            <returns>True if Open() succeeds; otherwise, false.</returns>
        </member>
        <member name="M:SevenZip.SevenZipExtractor.GetArchiveInfo(System.Boolean)">
            <summary>
            Retrieves all information about the archive.
            </summary>
            <exception cref="T:SevenZip.SevenZipArchiveException"/>
        </member>
        <member name="M:SevenZip.SevenZipExtractor.InitArchiveFileData(System.Boolean)">
            <summary>
            Ensure that _archiveFileData is loaded.
            </summary>
            <param name="disposeStream">Dispose the archive stream after this operation.</param>
        </member>
        <member name="M:SevenZip.SevenZipExtractor.SolidIndexes(System.UInt32[])">
            <summary>
            Produces an array of indexes from 0 to the maximum value in the specified array
            </summary>
            <param name="indexes">The source array</param>
            <returns>The array of indexes from 0 to the maximum value in the specified array</returns>
        </member>
        <member name="M:SevenZip.SevenZipExtractor.CheckIndexes(System.Int32[])">
            <summary>
            Checks whether all the indexes are valid.
            </summary>
            <param name="indexes">The indexes to check.</param>
            <returns>True is valid; otherwise, false.</returns>
        </member>
        <member name="M:SevenZip.SevenZipExtractor.GetArchiveExtractCallback(System.String,System.Int32,System.Collections.Generic.List{System.UInt32})">
            <summary>
            Gets the IArchiveExtractCallback callback
            </summary>
            <param name="directory">The directory where extract the files</param>
            <param name="filesCount">The number of files to be extracted</param>
            <param name="actualIndexes">The list of actual indexes (solid archives support)</param>
            <returns>The ArchiveExtractCallback callback</returns>
        </member>
        <member name="M:SevenZip.SevenZipExtractor.GetArchiveExtractCallback(System.IO.Stream,System.UInt32,System.Int32)">
            <summary>
            Gets the IArchiveExtractCallback callback
            </summary>
            <param name="stream">The stream where extract the file</param>
            <param name="index">The file index</param>
            <param name="filesCount">The number of files to be extracted</param>
            <returns>The ArchiveExtractCallback callback</returns>
        </member>
        <member name="M:SevenZip.SevenZipExtractor.ValidateStream(System.IO.Stream)">
            <summary>
            Checks if the specified stream supports extraction.
            </summary>
            <param name="stream">The stream to check.</param>
        </member>
        <member name="M:SevenZip.SevenZipExtractor.Dispose">
            <summary>
            Releases the unmanaged resources used by SevenZipExtractor.
            </summary>
        </member>
        <member name="E:SevenZip.SevenZipExtractor.FileExtractionStarted">
            <summary>
            Occurs when a new file is going to be unpacked.
            </summary>
            <remarks>Occurs when 7-zip engine requests for an output stream for a new file to unpack in.</remarks>
        </member>
        <member name="E:SevenZip.SevenZipExtractor.FileExtractionFinished">
            <summary>
            Occurs when a file has been successfully unpacked.
            </summary>
        </member>
        <member name="E:SevenZip.SevenZipExtractor.ExtractionFinished">
            <summary>
            Occurs when the archive has been unpacked.
            </summary>
        </member>
        <member name="E:SevenZip.SevenZipExtractor.Extracting">
            <summary>
            Occurs when data are being extracted.
            </summary>
            <remarks>Use this event for accurate progress handling and various ProgressBar.StepBy(e.PercentDelta) routines.</remarks>
        </member>
        <member name="E:SevenZip.SevenZipExtractor.FileExists">
            <summary>
            Occurs during the extraction when a file already exists.
            </summary>
        </member>
        <member name="M:SevenZip.SevenZipExtractor.FileExtractionStartedEventProxy(System.Object,SevenZip.FileInfoEventArgs)">
            <summary>
            Event proxy for FileExtractionStarted.
            </summary>
            <param name="sender">The sender of the event.</param>
            <param name="e">The event arguments.</param>
        </member>
        <member name="M:SevenZip.SevenZipExtractor.FileExtractionFinishedEventProxy(System.Object,SevenZip.FileInfoEventArgs)">
            <summary>
            Event proxy for FileExtractionFinished.
            </summary>
            <param name="sender">The sender of the event.</param>
            <param name="e">The event arguments.</param>
        </member>
        <member name="M:SevenZip.SevenZipExtractor.ExtractingEventProxy(System.Object,SevenZip.ProgressEventArgs)">
            <summary>
            Event proxy for Extracting.
            </summary>
            <param name="sender">The sender of the event.</param>
            <param name="e">The event arguments.</param>
        </member>
        <member name="M:SevenZip.SevenZipExtractor.FileExistsEventProxy(System.Object,SevenZip.FileOverwriteEventArgs)">
            <summary>
            Event proxy for FileExists.
            </summary>
            <param name="sender">The sender of the event.</param>
            <param name="e">The event arguments.</param>
        </member>
        <member name="P:SevenZip.SevenZipExtractor.ArchiveFileData">
            <summary>
            Gets the collection of ArchiveFileInfo with all information about files in the archive
            </summary>
        </member>
        <member name="P:SevenZip.SevenZipExtractor.ArchiveProperties">
            <summary>
            Gets the properties for the current archive
            </summary>
        </member>
        <member name="P:SevenZip.SevenZipExtractor.ArchiveFileNames">
            <summary>
            Gets the collection of all file names contained in the archive.
            </summary>
            <remarks>
            Each get recreates the collection
            </remarks>
        </member>
        <member name="P:SevenZip.SevenZipExtractor.VolumeFileNames">
            <summary>
            Gets the list of archive volume file names.
            </summary>
        </member>
        <member name="M:SevenZip.SevenZipExtractor.Check">
            <summary>
            Performs the archive integrity test.
            </summary>
            <returns>True is the archive is ok; otherwise, false.</returns>
        </member>
        <member name="M:SevenZip.SevenZipExtractor.ExtractFile(System.String,System.IO.Stream)">
            <summary>
            Unpacks the file by its name to the specified stream.
            </summary>
            <param name="fileName">The file full name in the archive file table.</param>
            <param name="stream">The stream where the file is to be unpacked.</param>
        </member>
        <member name="M:SevenZip.SevenZipExtractor.ExtractFile(System.Int32,System.IO.Stream)">
            <summary>
            Unpacks the file by its index to the specified stream.
            </summary>
            <param name="index">Index in the archive file table.</param>
            <param name="stream">The stream where the file is to be unpacked.</param>
        </member>
        <member name="M:SevenZip.SevenZipExtractor.ExtractFiles(System.String,System.Int32[])">
            <summary>
            Unpacks files by their indices to the specified directory.
            </summary>
            <param name="indexes">indexes of the files in the archive file table.</param>
            <param name="directory">Directory where the files are to be unpacked.</param>
        </member>
        <member name="M:SevenZip.SevenZipExtractor.ExtractFiles(System.String,System.String[])">
            <summary>
            Unpacks files by their full names to the specified directory.
            </summary>
            <param name="fileNames">Full file names in the archive file table.</param>
            <param name="directory">Directory where the files are to be unpacked.</param>
        </member>
        <member name="M:SevenZip.SevenZipExtractor.ExtractFiles(SevenZip.ExtractFileCallback)">
            <summary>
            Extracts files from the archive, giving a callback the choice what
            to do with each file. The order of the files is given by the archive.
            7-Zip (and any other solid) archives are NOT supported.
            </summary>
            <param name="extractFileCallback">The callback to call for each file in the archive.</param>
            <exception cref="T:SevenZip.SevenZipExtractionFailedException">Thrown when trying to extract from solid archives.</exception>
        </member>
        <member name="M:SevenZip.SevenZipExtractor.ExtractArchive(System.String)">
            <summary>
            Unpacks the whole archive to the specified directory.
            </summary>
            <param name="directory">The directory where the files are to be unpacked.</param>
        </member>
        <member name="M:SevenZip.SevenZipExtractor.DecompressStream(System.IO.Stream,System.IO.Stream,System.Nullable{System.Int32},System.EventHandler{SevenZip.ProgressEventArgs})">
            <summary>
            Decompress the specified stream (C# inside)
            </summary>
            <param name="inStream">The source compressed stream</param>
            <param name="outStream">The destination uncompressed stream</param>
            <param name="inLength">The length of compressed data (null for inStream.Length)</param>
            <param name="codeProgressEvent">The event for handling the code progress</param>
        </member>
        <member name="M:SevenZip.SevenZipExtractor.ExtractBytes(System.Byte[])">
            <summary>
            Decompress byte array compressed with LZMA algorithm (C# inside)
            </summary>
            <param name="data">Byte array to decompress</param>
            <returns>Decompressed byte array</returns>
        </member>
        <member name="M:SevenZip.SevenZipExtractor.RecreateInstanceIfNeeded">
            <summary>
            Recreates the instance of the SevenZipExtractor class.
            Used in asynchronous methods.
            </summary>
        </member>
        <member name="T:SevenZip.SevenZipExtractor.ExtractArchiveDelegate">
            <summary>
            The delegate to use in BeginExtractArchive.
            </summary>
            <param name="directory">The directory where the files are to be unpacked.</param>
        </member>
        <member name="T:SevenZip.SevenZipExtractor.ExtractFileByFileNameDelegate">
            <summary>
            The delegate to use in BeginExtractFile (by file name).
            </summary>
            <param name="fileName">The file full name in the archive file table.</param>
            <param name="stream">The stream where the file is to be unpacked.</param>
        </member>
        <member name="T:SevenZip.SevenZipExtractor.ExtractFileByIndexDelegate">
            <summary>
            The delegate to use in BeginExtractFile (by index).
            </summary>
            <param name="index">Index in the archive file table.</param>
            <param name="stream">The stream where the file is to be unpacked.</param>
        </member>
        <member name="T:SevenZip.SevenZipExtractor.ExtractFiles1Delegate">
            <summary>
            The delegate to use in BeginExtractFiles(string directory, params int[] indexes).
            </summary>
            <param name="indexes">indexes of the files in the archive file table.</param>
            <param name="directory">Directory where the files are to be unpacked.</param>
        </member>
        <member name="T:SevenZip.SevenZipExtractor.ExtractFiles2Delegate">
            <summary>
            The delegate to use in BeginExtractFiles(string directory, params string[] fileNames).
            </summary>
            <param name="fileNames">Full file names in the archive file table.</param>
            <param name="directory">Directory where the files are to be unpacked.</param>
        </member>
        <member name="T:SevenZip.SevenZipExtractor.ExtractFiles3Delegate">
            <summary>
            The delegate to use in BeginExtractFiles(ExtractFileCallback extractFileCallback).
            </summary>
            <param name="extractFileCallback">The callback to call for each file in the archive.</param>
        </member>
        <member name="M:SevenZip.SevenZipExtractor.BeginExtractArchive(System.String)">
            <summary>
            Unpacks the whole archive asynchronously to the specified directory name at the specified priority.
            </summary>
            <param name="directory">The directory where the files are to be unpacked.</param>
        </member>
        <member name="M:SevenZip.SevenZipExtractor.ExtractArchiveAsync(System.String)">
            <summary>
            Unpacks the whole archive asynchronously to the specified directory name at the specified priority.
            </summary>
            <param name="directory">The directory where the files are to be unpacked.</param>
        </member>
        <member name="M:SevenZip.SevenZipExtractor.BeginExtractFile(System.String,System.IO.Stream)">
            <summary>
            Unpacks the file asynchronously by its name to the specified stream.
            </summary>
            <param name="fileName">The file full name in the archive file table.</param>
            <param name="stream">The stream where the file is to be unpacked.</param>
        </member>
        <member name="M:SevenZip.SevenZipExtractor.ExtractFileAsync(System.String,System.IO.Stream)">
            <summary>
            Unpacks the file asynchronously by its name to the specified stream.
            </summary>
            <param name="fileName">The file full name in the archive file table.</param>
            <param name="stream">The stream where the file is to be unpacked.</param>
        </member>
        <member name="M:SevenZip.SevenZipExtractor.BeginExtractFile(System.Int32,System.IO.Stream)">
            <summary>
            Unpacks the file asynchronously by its index to the specified stream.
            </summary>
            <param name="index">Index in the archive file table.</param>
            <param name="stream">The stream where the file is to be unpacked.</param>
        </member>
        <member name="M:SevenZip.SevenZipExtractor.ExtractFileAsync(System.Int32,System.IO.Stream)">
            <summary>
            Unpacks the file asynchronously by its name to the specified stream.
            </summary>
            <param name="index">Index in the archive file table.</param>
            <param name="stream">The stream where the file is to be unpacked.</param>
        </member>
        <member name="M:SevenZip.SevenZipExtractor.BeginExtractFiles(System.String,System.Int32[])">
            <summary>
            Unpacks files asynchronously by their indices to the specified directory.
            </summary>
            <param name="indexes">indexes of the files in the archive file table.</param>
            <param name="directory">Directory where the files are to be unpacked.</param>
        </member>
        <member name="M:SevenZip.SevenZipExtractor.ExtractFilesAsync(System.String,System.Int32[])">
            <summary>
            Unpacks files asynchronously by their indices to the specified directory.
            </summary>
            <param name="indexes">indexes of the files in the archive file table.</param>
            <param name="directory">Directory where the files are to be unpacked.</param>
        </member>
        <member name="M:SevenZip.SevenZipExtractor.BeginExtractFiles(System.String,System.String[])">
            <summary>
            Unpacks files asynchronously by their full names to the specified directory.
            </summary>
            <param name="fileNames">Full file names in the archive file table.</param>
            <param name="directory">Directory where the files are to be unpacked.</param>
        </member>
        <member name="M:SevenZip.SevenZipExtractor.ExtractFilesAsync(System.String,System.String[])">
            <summary>
            Unpacks files asynchronously by their full names to the specified directory.
            </summary>
            <param name="fileNames">Full file names in the archive file table.</param>
            <param name="directory">Directory where the files are to be unpacked.</param>
        </member>
        <member name="M:SevenZip.SevenZipExtractor.BeginExtractFiles(SevenZip.ExtractFileCallback)">
            <summary>
            Extracts files from the archive asynchronously, giving a callback the choice what
            to do with each file. The order of the files is given by the archive.
            7-Zip (and any other solid) archives are NOT supported.
            </summary>
            <param name="extractFileCallback">The callback to call for each file in the archive.</param>
        </member>
        <member name="M:SevenZip.SevenZipExtractor.ExtractFilesAsync(SevenZip.ExtractFileCallback)">
            <summary>
            Extracts files from the archive asynchronously, giving a callback the choice what
            to do with each file. The order of the files is given by the archive.
            7-Zip (and any other solid) archives are NOT supported.
            </summary>
            <param name="extractFileCallback">The callback to call for each file in the archive.</param>
        </member>
        <member name="T:SevenZip.SfxModule">
            <summary>
            Sfx module choice enumeration
            </summary>
        </member>
        <member name="F:SevenZip.SfxModule.Default">
            <summary>
            Default module (leave this if unsure)
            </summary>
        </member>
        <member name="F:SevenZip.SfxModule.Simple">
            <summary>
            The simple sfx module by Igor Pavlov with no adjustable parameters
            </summary>
        </member>
        <member name="F:SevenZip.SfxModule.Installer">
            <summary>
            The installer sfx module by Igor Pavlov
            </summary>
        </member>
        <member name="F:SevenZip.SfxModule.Extended">
            <summary>
            The extended installer sfx module by Oleg Scherbakov 
            </summary>
        </member>
        <member name="F:SevenZip.SfxModule.Custom">
            <summary>
            The custom sfx module. First you must specify the module file name.
            </summary>
        </member>
        <member name="T:SevenZip.SevenZipSfx">
            <summary>
            The class for making 7-zip based self-extracting archives.
            </summary>
        </member>
        <member name="M:SevenZip.SevenZipSfx.#ctor">
            <summary>
            Initializes a new instance of the SevenZipSfx class.
            </summary>
        </member>
        <member name="M:SevenZip.SevenZipSfx.#ctor(SevenZip.SfxModule)">
            <summary>
            Initializes a new instance of the SevenZipSfx class.
            </summary>
            <param name="module">The sfx module to use as a front-end.</param>
        </member>
        <member name="M:SevenZip.SevenZipSfx.#ctor(System.String)">
            <summary>
            Initializes a new instance of the SevenZipSfx class.
            </summary>
            <param name="moduleFileName"></param>
        </member>
        <member name="P:SevenZip.SevenZipSfx.SfxModule">
            <summary>
            Gets the sfx module type.
            </summary>
        </member>
        <member name="P:SevenZip.SevenZipSfx.ModuleFileName">
            <summary>
            Gets or sets the custom sfx module file name
            </summary>
        </member>
        <member name="M:SevenZip.SevenZipSfx.GetModuleByName(System.String)">
            <summary>
            Gets the sfx module enum by the list of supported modules
            </summary>
            <param name="name"></param>
            <returns></returns>
        </member>
        <member name="M:SevenZip.SevenZipSfx.LoadCommandsFromResource(System.String)">
            <summary>
            Loads the commands for each supported sfx module configuration
            </summary>
            <param name="xmlDefinitions">The resource name for xml definitions</param>
        </member>
        <member name="M:SevenZip.SevenZipSfx.ValidateSettings(System.Collections.Generic.Dictionary{System.String,System.String})">
            <summary>
            Validates the sfx scenario commands.
            </summary>
            <param name="settings">The sfx settings dictionary to validate.</param>
        </member>
        <member name="M:SevenZip.SevenZipSfx.GetSettingsStream(System.Collections.Generic.Dictionary{System.String,System.String})">
            <summary>
            Gets the stream containing the sfx settings.
            </summary>
            <param name="settings">The sfx settings dictionary.</param>
            <returns></returns>
        </member>
        <member name="M:SevenZip.SevenZipSfx.WriteStream(System.IO.Stream,System.IO.Stream)">
            <summary>
            Writes the whole to the other one.
            </summary>
            <param name="src">The source stream to read from.</param>
            <param name="dest">The destination stream to write to.</param>
        </member>
        <member name="M:SevenZip.SevenZipSfx.MakeSfx(System.IO.Stream,System.String)">
            <summary>
            Makes the self-extracting archive.
            </summary>
            <param name="archive">The archive stream.</param>
            <param name="sfxFileName">The name of the self-extracting executable.</param>
        </member>
        <member name="M:SevenZip.SevenZipSfx.MakeSfx(System.IO.Stream,System.IO.Stream)">
            <summary>
            Makes the self-extracting archive.
            </summary>
            <param name="archive">The archive stream.</param>
            <param name="sfxStream">The stream to write the self-extracting executable to.</param>
        </member>
        <member name="M:SevenZip.SevenZipSfx.MakeSfx(System.IO.Stream,System.Collections.Generic.Dictionary{System.String,System.String},System.String)">
            <summary>
            Makes the self-extracting archive.
            </summary>
            <param name="archive">The archive stream.</param>
            <param name="settings">The sfx settings.</param>
            <param name="sfxFileName">The name of the self-extracting executable.</param>
        </member>
        <member name="M:SevenZip.SevenZipSfx.MakeSfx(System.IO.Stream,System.Collections.Generic.Dictionary{System.String,System.String},System.IO.Stream)">
            <summary>
            Makes the self-extracting archive.
            </summary>
            <param name="archive">The archive stream.</param>
            <param name="settings">The sfx settings.</param>
            <param name="sfxStream">The stream to write the self-extracting executable to.</param>
        </member>
        <member name="M:SevenZip.SevenZipSfx.MakeSfx(System.String,System.String)">
            <summary>
            Makes the self-extracting archive.
            </summary>
            <param name="archiveFileName">The archive file name.</param>
            <param name="sfxFileName">The name of the self-extracting executable.</param>
        </member>
        <member name="M:SevenZip.SevenZipSfx.MakeSfx(System.String,System.IO.Stream)">
            <summary>
            Makes the self-extracting archive.
            </summary>
            <param name="archiveFileName">The archive file name.</param>
            <param name="sfxStream">The stream to write the self-extracting executable to.</param>
        </member>
        <member name="M:SevenZip.SevenZipSfx.MakeSfx(System.String,System.Collections.Generic.Dictionary{System.String,System.String},System.String)">
            <summary>
            Makes the self-extracting archive.
            </summary>
            <param name="archiveFileName">The archive file name.</param>
            <param name="settings">The sfx settings.</param>
            <param name="sfxFileName">The name of the self-extracting executable.</param>
        </member>
        <member name="M:SevenZip.SevenZipSfx.MakeSfx(System.String,System.Collections.Generic.Dictionary{System.String,System.String},System.IO.Stream)">
            <summary>
            Makes the self-extracting archive.
            </summary>
            <param name="archiveFileName">The archive file name.</param>
            <param name="settings">The sfx settings.</param>
            <param name="sfxStream">The stream to write the self-extracting executable to.</param>
        </member>
        <member name="T:SevenZip.DisposeVariableWrapper">
            <summary>
            A class that has DisposeStream property.
            </summary>
        </member>
        <member name="T:SevenZip.StreamWrapper">
            <summary>
            Stream wrapper used in InStreamWrapper
            </summary>
        </member>
        <member name="F:SevenZip.StreamWrapper._fileName">
            <summary>
            File name associated with the stream (for date fix)
            </summary>
        </member>
        <member name="F:SevenZip.StreamWrapper._baseStream">
            <summary>
            Worker stream for reading, writing and seeking.
            </summary>
        </member>
        <member name="M:SevenZip.StreamWrapper.#ctor(System.IO.Stream,System.String,System.DateTime,System.Boolean)">
            <summary>
            Initializes a new instance of the StreamWrapper class
            </summary>
            <param name="baseStream">Worker stream for reading, writing and seeking</param>
            <param name="fileName">File name associated with the stream (for attributes fix)</param>
            <param name="time">File last write time (for attributes fix)</param>
            <param name="disposeStream">Indicates whether to dispose the baseStream</param>
        </member>
        <member name="M:SevenZip.StreamWrapper.#ctor(System.IO.Stream,System.Boolean)">
            <summary>
            Initializes a new instance of the StreamWrapper class
            </summary>
            <param name="baseStream">Worker stream for reading, writing and seeking</param>
            <param name="disposeStream">Indicates whether to dispose the baseStream</param>
        </member>
        <member name="P:SevenZip.StreamWrapper.BaseStream">
            <summary>
            Gets the worker stream for reading, writing and seeking.
            </summary>
        </member>
        <member name="M:SevenZip.StreamWrapper.Dispose">
            <summary>
            Cleans up any resources used and fixes file attributes.
            </summary>
        </member>
        <member name="T:SevenZip.InStreamWrapper">
            <summary>
            IInStream wrapper used in stream read operations.
            </summary>
        </member>
        <member name="M:SevenZip.InStreamWrapper.#ctor(System.IO.Stream,System.Boolean)">
            <summary>
            Initializes a new instance of the InStreamWrapper class.
            </summary>
            <param name="baseStream">Stream for writing data</param>
            <param name="disposeStream">Indicates whether to dispose the baseStream</param>
        </member>
        <member name="M:SevenZip.InStreamWrapper.Read(System.Byte[],System.UInt32)">
            <summary>
            Reads data from the stream.
            </summary>
            <param name="data">A data array.</param>
            <param name="size">The array size.</param>
            <returns>The read bytes count.</returns>
        </member>
        <member name="E:SevenZip.InStreamWrapper.BytesRead">
            <summary>
            Occurs when IntEventArgs.Value bytes were read from the source.
            </summary>
        </member>
        <member name="T:SevenZip.OutStreamWrapper">
            <summary>
            IOutStream wrapper used in stream write operations.
            </summary>
        </member>
        <member name="M:SevenZip.OutStreamWrapper.#ctor(System.IO.Stream,System.String,System.DateTime,System.Boolean)">
            <summary>
            Initializes a new instance of the OutStreamWrapper class
            </summary>
            <param name="baseStream">Stream for writing data</param>
            <param name="fileName">File name (for attributes fix)</param>
            <param name="time">Time of the file creation (for attributes fix)</param>
            <param name="disposeStream">Indicates whether to dispose the baseStream</param>
        </member>
        <member name="M:SevenZip.OutStreamWrapper.#ctor(System.IO.Stream,System.Boolean)">
            <summary>
            Initializes a new instance of the OutStreamWrapper class
            </summary>
            <param name="baseStream">Stream for writing data</param>
            <param name="disposeStream">Indicates whether to dispose the baseStream</param>
        </member>
        <member name="M:SevenZip.OutStreamWrapper.Write(System.Byte[],System.UInt32,System.IntPtr)">
            <summary>
            Writes data to the stream
            </summary>
            <param name="data">Data array</param>
            <param name="size">Array size</param>
            <param name="processedSize">Count of written bytes</param>
            <returns>Zero if Ok</returns>
        </member>
        <member name="E:SevenZip.OutStreamWrapper.BytesWritten">
            <summary>
            Occurs when IntEventArgs.Value bytes were written.
            </summary>
        </member>
        <member name="T:SevenZip.MultiStreamWrapper">
            <summary>
            Base multi volume stream wrapper class.
            </summary>
        </member>
        <member name="M:SevenZip.MultiStreamWrapper.#ctor(System.Boolean)">
            <summary>
            Initializes a new instance of the MultiStreamWrapper class.
            </summary>
            <param name="dispose">Perform Dispose() if requested to.</param>
        </member>
        <member name="P:SevenZip.MultiStreamWrapper.Length">
            <summary>
            Gets the total length of input data.
            </summary>
        </member>
        <member name="M:SevenZip.MultiStreamWrapper.Dispose">
            <summary>
            Cleans up any resources used and fixes file attributes.
            </summary>
        </member>
        <member name="T:SevenZip.InMultiStreamWrapper">
            <summary>
            IInStream wrapper used in stream multi volume read operations.
            </summary>
        </member>
        <member name="M:SevenZip.InMultiStreamWrapper.#ctor(System.String,System.Boolean)">
            <summary>
            Initializes a new instance of the InMultiStreamWrapper class.
            </summary>
            <param name="fileName">The archive file name.</param>
            <param name="dispose">Perform Dispose() if requested to.</param>
        </member>
        <member name="M:SevenZip.InMultiStreamWrapper.Read(System.Byte[],System.UInt32)">
            <summary>
            Reads data from the stream.
            </summary>
            <param name="data">A data array.</param>
            <param name="size">The array size.</param>
            <returns>The read bytes count.</returns>
        </member>
        <member name="T:SevenZip.OutMultiStreamWrapper">
            <summary>
            IOutStream wrapper used in multi volume stream write operations.
            </summary>
        </member>
        <member name="M:SevenZip.OutMultiStreamWrapper.#ctor(System.String,System.Int64)">
            <summary>
            Initializes a new instance of the OutMultiStreamWrapper class.
            </summary>
            <param name="archiveName">The archive name.</param>
            <param name="volumeSize">The volume size.</param>
        </member>
        <member name="M:SevenZip.FakeOutStreamWrapper.Write(System.Byte[],System.UInt32,System.IntPtr)">
            <summary>
            Does nothing except calling the BytesWritten event
            </summary>
            <param name="data">Data array</param>
            <param name="size">Array size</param>
            <param name="processedSize">Count of written bytes</param>
            <returns>Zero if Ok</returns>
        </member>
        <member name="E:SevenZip.FakeOutStreamWrapper.BytesWritten">
            <summary>
            Occurs when IntEventArgs.Value bytes were written
            </summary>
        </member>
        <member name="T:SevenZip.UpdateData">
            <summary>
            Archive update data for UpdateCallback.
            </summary>
        </member>
        <member name="T:SevenZip.ZipEncryptionMethod">
            <summary>
            Zip encryption method enum.
            </summary>
        </member>
        <member name="F:SevenZip.ZipEncryptionMethod.ZipCrypto">
            <summary>
            ZipCrypto encryption method.
            </summary>
        </member>
        <member name="F:SevenZip.ZipEncryptionMethod.Aes128">
            <summary>
            AES 128 bit encryption method.
            </summary>
        </member>
        <member name="F:SevenZip.ZipEncryptionMethod.Aes192">
            <summary>
            AES 192 bit encryption method.
            </summary>
        </member>
        <member name="F:SevenZip.ZipEncryptionMethod.Aes256">
            <summary>
            AES 256 bit encryption method.
            </summary>
        </member>
    </members>
</doc>
