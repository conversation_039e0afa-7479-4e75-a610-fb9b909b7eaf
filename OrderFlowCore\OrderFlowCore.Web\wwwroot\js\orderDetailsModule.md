# Order Details Module Documentation

## Overview

The Order Details Module provides reusable functionality for displaying and managing order details across different views in the application. This module eliminates code duplication and provides a consistent user experience.

## Components

### 1. Partial View: `_OrderDetailsPartial.cshtml`

**Location**: `Views/Shared/_OrderDetailsPartial.cshtml`

**Usage**: Include this partial view in any view that needs to display order details:

```html
@await Html.PartialAsync("_OrderDetailsPartial")
```

**Features**:
- Complete order details table structure
- All necessary HTML elements with proper IDs
- Responsive design
- Consistent styling

### 2. JavaScript Module: `orderDetailsModule.js`

**Location**: `wwwroot/js/orderDetailsModule.js`

**Usage**: Include this script in your view and initialize with configuration:

```html
@section Scripts {
    <script src="~/js/orderDetailsModule.js"></script>
    <script>
        $(document).ready(function () {
            // Initialize the module with configuration
            OrderDetailsModule.init({
                showLoading: function() {
                    $('#loading').show();
                    $('#orderDetails').hide();
                },
                hideLoading: function() {
                    $('#loading').hide();
                },
                showMessage: function(message, type) {
                    // Your message display logic
                },
                showOrderDetails: function() {
                    $('#orderDetails').show();
                },
                hideOrderDetails: function() {
                    $('#orderDetails').hide();
                }
            });

            // Load order details
            $('#orderSelect').change(function () {
                const orderId = $(this).val();
                if (orderId) {
                    OrderDetailsModule.loadOrderDetails(orderId, '/YourController/GetOrderDetails');
                } else {
                    OrderDetailsModule.hideOrderDetails();
                }
            });
        });
    </script>
}
```

## API Reference

### OrderDetailsModule Methods

#### `init(options)`
Initialize the module with configuration options.

**Parameters**:
- `options.showLoading`: Function to show loading state (optional)
- `options.hideLoading`: Function to hide loading state (optional)
- `options.showMessage`: Function to display messages (optional)
- `options.showOrderDetails`: Function to show order details section (optional)
- `options.hideOrderDetails`: Function to hide order details section (optional)

#### `getConfig()`
Get the current configuration.

**Returns**: Current configuration object

#### `loadOrderDetails(orderId, endpoint, additionalData)`
Load order details from the server.

**Parameters**:
- `orderId`: The order ID to load
- `endpoint`: The endpoint URL to call
- `additionalData`: Additional data to send with the request (optional)

#### `populateOrderDetails(data)`
Populate order details in the DOM.

**Parameters**:
- `data`: Order details data object

#### `hideOrderDetails()`
Hide order details and clear current order ID.

#### `getCurrentOrderId()`
Get the current order ID.

**Returns**: Current order ID or null

#### `clearOrderDetails()`
Clear all order details fields.

#### `showOrderDetails()`
Show the order details section.

#### `hideOrderDetailsSection()`
Hide the order details section.

#### `showLoading()`
Show loading state (uses config function if provided, otherwise default behavior).

#### `hideLoading()`
Hide loading state (uses config function if provided, otherwise default behavior).

#### `showMessage(message, type)`
Show a message (uses config function if provided, otherwise default behavior).

**Parameters**:
- `message`: Message to display
- `type`: Message type ('success' or 'error')

#### `confirmOrder(orderId, endpoint, successMessage, errorMessage, onSuccess)`
Confirm an order with built-in confirmation dialog and AJAX call.

**Parameters**:
- `orderId`: Order ID to confirm
- `endpoint`: Confirmation endpoint URL
- `successMessage`: Success message to display
- `errorMessage`: Error message to display
- `onSuccess`: Success callback function (optional)

#### `rejectOrder(orderId, reason, endpoint, successMessage, errorMessage, onSuccess)`
Reject an order with built-in confirmation dialog and AJAX call.

**Parameters**:
- `orderId`: Order ID to reject
- `reason`: Rejection reason
- `endpoint`: Rejection endpoint URL
- `successMessage`: Success message to display
- `errorMessage`: Error message to display
- `onSuccess`: Success callback function (optional)

## Data Structure

The module expects the following data structure from the server:

```javascript
{
    orderNumber: "12345",
    orderDate: "2024-01-01",
    orderStatus: "قيد الانتظار",
    orderType: "نوع الطلب",
    employeeName: "اسم الموظف",
    department: "القسم",
    jobTitle: "الوظيفة",
    employmentType: "نوع التوظيف",
    qualification: "المؤهل",
    employeeNumber: "رقم الموظف",
    civilRegistry: "السجل المدني",
    nationality: "الجنسية",
    mobileNumber: "رقم الجوال",
    notes: "ملاحظات",
    managerApproval: "موافق",
    supervisorApproval: "قيد الانتظار",
    coordinatorApproval: "قيد الانتظار",
    cancellationReason: "سبب الإلغاء",
    coordinatorDetails: "تفاصيل المنسق",
    hrManagerApproval: "قيد الانتظار",
    // Supervisor permissions
    supervisorOfEmployeeServices: "موافق",
    supervisorOfHumanResourcesPlanning: "قيد الانتظار",
    // ... other supervisor fields
}
```

## Status Badges

The module automatically applies status badges based on the following mapping:

- `موافق` → Green badge
- `مرفوض` → Red badge
- `قيد الانتظار` → Yellow badge
- `تم التحويل` → Blue badge
- `تم الإلغاء` → Gray badge
- `تم الإعادة` → Yellow badge

## Examples

### Basic Implementation

```html
<!-- In your view -->
<div id="orderSelection">
    <select id="orderSelect">
        <option value="">اختر الطلب</option>
        <!-- Options populated from server -->
    </select>
</div>

<div id="loading" style="display: none;">
    جاري التحميل...
</div>

@await Html.PartialAsync("_OrderDetailsPartial")

<div id="messageContainer"></div>

@section Scripts {
    <script src="~/js/orderDetailsModule.js"></script>
    <script>
        $(document).ready(function () {
            OrderDetailsModule.init({
                showLoading: () => $('#loading').show(),
                hideLoading: () => $('#loading').hide(),
                showMessage: (msg, type) => {
                    const alertClass = type === 'error' ? 'alert-danger' : 'alert-success';
                    $('#messageContainer').html(`<div class="alert ${alertClass}">${msg}</div>`);
                },
                showOrderDetails: () => $('#orderDetails').show(),
                hideOrderDetails: () => $('#orderDetails').hide()
            });

            $('#orderSelect').change(function () {
                const orderId = $(this).val();
                if (orderId) {
                    OrderDetailsModule.loadOrderDetails(orderId, '/YourController/GetOrderDetails');
                } else {
                    OrderDetailsModule.hideOrderDetails();
                }
            });
        });
    </script>
}
```

### Advanced Implementation with Custom Actions

```html
@section Scripts {
    <script src="~/js/orderDetailsModule.js"></script>
    <script>
        $(document).ready(function () {
            OrderDetailsModule.init({
                // ... configuration
            });

            // Custom action buttons
            $('#confirmBtn').click(function() {
                const orderId = OrderDetailsModule.getCurrentOrderId();
                if (orderId) {
                    OrderDetailsModule.confirmOrder(
                        orderId,
                        '/YourController/ConfirmOrder',
                        'تم التأكيد بنجاح',
                        'حدث خطأ أثناء التأكيد'
                    );
                }
            });

            $('#rejectBtn').click(function() {
                const orderId = OrderDetailsModule.getCurrentOrderId();
                if (orderId) {
                    const reason = $('#rejectReason').val();
                    if (reason.trim()) {
                        OrderDetailsModule.rejectOrder(
                            orderId,
                            reason,
                            '/YourController/RejectOrder',
                            'تم الإلغاء بنجاح',
                            'حدث خطأ أثناء الإلغاء'
                        );
                    } else {
                        OrderDetailsModule.showMessage('يجب إدخال سبب الإلغاء', 'error');
                    }
                }
            });
        });
    </script>
}
```

## Benefits

1. **Code Reusability**: Eliminates duplication across multiple views
2. **Consistency**: Ensures uniform behavior and appearance
3. **Maintainability**: Changes in one place affect all implementations
4. **Modularity**: Easy to extend and customize
5. **Performance**: Optimized DOM manipulation and event handling

## Migration Guide

To migrate existing views to use the shared components:

1. Replace the order details HTML with `@await Html.PartialAsync("_OrderDetailsPartial")`
2. Include the JavaScript module: `<script src="~/js/orderDetailsModule.js"></script>`
3. Initialize the module with appropriate configuration
4. Replace direct DOM manipulation with module method calls
5. Update event handlers to use the module's API

## Troubleshooting

### Common Issues

1. **Module not found**: Ensure the JavaScript file is properly included
2. **Configuration errors**: Check that all required configuration functions are provided
3. **Data mapping issues**: Verify that server response matches expected data structure
4. **CSS conflicts**: Ensure the partial view CSS classes don't conflict with existing styles

### Debug Mode

Enable debug mode by setting `window.orderDetailsDebug = true` before initializing the module:

```javascript
window.orderDetailsDebug = true;
OrderDetailsModule.init({...});
```

This will log additional information to the console for debugging purposes. 