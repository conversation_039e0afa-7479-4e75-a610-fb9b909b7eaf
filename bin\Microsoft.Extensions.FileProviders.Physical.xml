<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.Extensions.FileProviders.Physical</name>
    </assembly>
    <members>
        <member name="T:Microsoft.Extensions.FileProviders.Physical.ExclusionFilters">
            <summary>
            Specifies filtering behavior for files or directories.
            </summary>
        </member>
        <member name="F:Microsoft.Extensions.FileProviders.Physical.ExclusionFilters.Sensitive">
            <summary>
            Equivalent to <c>DotPrefixed | Hidden | System</c>. Exclude files and directories when the name begins with a period, or has either <see cref="F:System.IO.FileAttributes.Hidden"/> or <see cref="F:System.IO.FileAttributes.System"/> is set on <see cref="P:System.IO.FileSystemInfo.Attributes"/>.
            </summary>
        </member>
        <member name="F:Microsoft.Extensions.FileProviders.Physical.ExclusionFilters.DotPrefixed">
            <summary>
            Exclude files and directories when the name begins with period.
            </summary>
        </member>
        <member name="F:Microsoft.Extensions.FileProviders.Physical.ExclusionFilters.Hidden">
            <summary>
            Exclude files and directories when <see cref="F:System.IO.FileAttributes.Hidden"/> is set on <see cref="P:System.IO.FileSystemInfo.Attributes"/>.
            </summary>
        </member>
        <member name="F:Microsoft.Extensions.FileProviders.Physical.ExclusionFilters.System">
            <summary>
            Exclude files and directories when <see cref="F:System.IO.FileAttributes.System"/> is set on <see cref="P:System.IO.FileSystemInfo.Attributes"/>.
            </summary>
        </member>
        <member name="F:Microsoft.Extensions.FileProviders.Physical.ExclusionFilters.None">
            <summary>
            Do not exclude any files.
            </summary>
        </member>
        <member name="T:Microsoft.Extensions.FileProviders.Physical.PhysicalDirectoryInfo">
            <summary>
            Represents a directory on a physical filesystem
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.FileProviders.Physical.PhysicalDirectoryInfo.#ctor(System.IO.DirectoryInfo)">
            <summary>
            Initializes an instance of <see cref="T:Microsoft.Extensions.FileProviders.Physical.PhysicalDirectoryInfo"/> that wraps an instance of <see cref="T:System.IO.DirectoryInfo"/>
            </summary>
            <param name="info">The directory</param>
        </member>
        <member name="P:Microsoft.Extensions.FileProviders.Physical.PhysicalDirectoryInfo.Exists">
            <inheritdoc />
        </member>
        <member name="P:Microsoft.Extensions.FileProviders.Physical.PhysicalDirectoryInfo.Length">
            <summary>
            Always equals -1.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.FileProviders.Physical.PhysicalDirectoryInfo.PhysicalPath">
            <inheritdoc />
        </member>
        <member name="P:Microsoft.Extensions.FileProviders.Physical.PhysicalDirectoryInfo.Name">
            <inheritdoc />
        </member>
        <member name="P:Microsoft.Extensions.FileProviders.Physical.PhysicalDirectoryInfo.LastModified">
            <summary>
            The time when the directory was last written to.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.FileProviders.Physical.PhysicalDirectoryInfo.IsDirectory">
            <summary>
            Always true.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.FileProviders.Physical.PhysicalDirectoryInfo.CreateReadStream">
            <summary>
            Always throws an exception because read streams are not support on directories.
            </summary>
            <exception cref="T:System.InvalidOperationException">Always thrown</exception>
            <returns>Never returns</returns>
        </member>
        <member name="T:Microsoft.Extensions.FileProviders.Physical.PhysicalFileInfo">
            <summary>
            Represents a file on a physical filesystem
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.FileProviders.Physical.PhysicalFileInfo.#ctor(System.IO.FileInfo)">
            <summary>
            Initializes an instance of <see cref="T:Microsoft.Extensions.FileProviders.Physical.PhysicalFileInfo"/> that wraps an instance of <see cref="T:System.IO.FileInfo"/>
            </summary>
            <param name="info">The <see cref="T:System.IO.FileInfo"/></param>
        </member>
        <member name="P:Microsoft.Extensions.FileProviders.Physical.PhysicalFileInfo.Exists">
            <inheritdoc />
        </member>
        <member name="P:Microsoft.Extensions.FileProviders.Physical.PhysicalFileInfo.Length">
            <inheritdoc />
        </member>
        <member name="P:Microsoft.Extensions.FileProviders.Physical.PhysicalFileInfo.PhysicalPath">
            <inheritdoc />
        </member>
        <member name="P:Microsoft.Extensions.FileProviders.Physical.PhysicalFileInfo.Name">
            <inheritdoc />
        </member>
        <member name="P:Microsoft.Extensions.FileProviders.Physical.PhysicalFileInfo.LastModified">
            <inheritdoc />
        </member>
        <member name="P:Microsoft.Extensions.FileProviders.Physical.PhysicalFileInfo.IsDirectory">
            <summary>
            Always false.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.FileProviders.Physical.PhysicalFileInfo.CreateReadStream">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.Extensions.FileProviders.Physical.PhysicalFilesWatcher">
            <summary>
                <para>
                A file watcher that watches a physical filesystem for changes.
                </para>
                <para>
                Triggers events on <see cref="T:Microsoft.Extensions.Primitives.IChangeToken" /> when files are created, change, renamed, or deleted.
                </para>
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.FileProviders.Physical.PhysicalFilesWatcher.#ctor(System.String,System.IO.FileSystemWatcher,System.Boolean)">
            <summary>
            Initializes an instance of <see cref="T:Microsoft.Extensions.FileProviders.Physical.PhysicalFilesWatcher" /> that watches files in <paramref name="root" />.
            Wraps an instance of <see cref="T:System.IO.FileSystemWatcher" />
            </summary>
            <param name="root">Root directory for the watcher</param>
            <param name="fileSystemWatcher">The wrapped watcher that is watching <paramref name="root" /></param>
            <param name="pollForChanges">
            True when the watcher should use polling to trigger instances of
            <see cref="T:Microsoft.Extensions.Primitives.IChangeToken" /> created by <see cref="M:Microsoft.Extensions.FileProviders.Physical.PhysicalFilesWatcher.CreateFileChangeToken(System.String)" />
            </param>
        </member>
        <member name="M:Microsoft.Extensions.FileProviders.Physical.PhysicalFilesWatcher.#ctor(System.String,System.IO.FileSystemWatcher,System.Boolean,Microsoft.Extensions.FileProviders.Physical.ExclusionFilters)">
            <summary>
            Initializes an instance of <see cref="T:Microsoft.Extensions.FileProviders.Physical.PhysicalFilesWatcher" /> that watches files in <paramref name="root" />.
            Wraps an instance of <see cref="T:System.IO.FileSystemWatcher" />
            </summary>
            <param name="root">Root directory for the watcher</param>
            <param name="fileSystemWatcher">The wrapped watcher that is watching <paramref name="root" /></param>
            <param name="pollForChanges">
            True when the watcher should use polling to trigger instances of
            <see cref="T:Microsoft.Extensions.Primitives.IChangeToken" /> created by <see cref="M:Microsoft.Extensions.FileProviders.Physical.PhysicalFilesWatcher.CreateFileChangeToken(System.String)" />
            </param>
            <param name="filters">Specifies which files or directories are excluded. Notifications of changes to are not raised to these.</param>
        </member>
        <member name="M:Microsoft.Extensions.FileProviders.Physical.PhysicalFilesWatcher.CreateFileChangeToken(System.String)">
            <summary>
                <para>
                Creates an instance of <see cref="T:Microsoft.Extensions.Primitives.IChangeToken" /> for all files and directories that match the
                <paramref name="filter" />
                </para>
                <para>
                Globbing patterns are relative to the root directory given in the constructor
                <seealso cref="M:Microsoft.Extensions.FileProviders.Physical.PhysicalFilesWatcher.#ctor(System.String,System.IO.FileSystemWatcher,System.Boolean)" />. Globbing patterns
                are interpreted by <seealso cref="T:Microsoft.Extensions.FileSystemGlobbing.Matcher" />.
                </para>
            </summary>
            <param name="filter">A globbing pattern for files and directories to watch</param>
            <returns>A change token for all files that match the filter</returns>
            <exception cref="T:System.ArgumentNullException">When <paramref name="filter" /> is null</exception>
        </member>
        <member name="M:Microsoft.Extensions.FileProviders.Physical.PhysicalFilesWatcher.Dispose">
            <summary>
            Disposes the provider. Change tokens may not trigger after the provider is disposed.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.FileProviders.Physical.PhysicalFilesWatcher.Dispose(System.Boolean)">
            <summary>
            Disposes the provider.
            </summary>
            <param name="disposing"><c>true</c> is invoked from <see cref="M:System.IDisposable.Dispose"/>.</param>
        </member>
        <member name="T:Microsoft.Extensions.FileProviders.Physical.PollingFileChangeToken">
            <summary>
                <para>
                A change token that polls for file system changes.
                </para>
                <para>
                This change token does not raise any change callbacks. Callers should watch for <see cref="P:Microsoft.Extensions.FileProviders.Physical.PollingFileChangeToken.HasChanged" /> to turn
                from false to true
                and dispose the token after this happens.
                </para>
            </summary>
            <remarks>
            Polling occurs every 4 seconds.
            </remarks>
        </member>
        <member name="M:Microsoft.Extensions.FileProviders.Physical.PollingFileChangeToken.#ctor(System.IO.FileInfo)">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.Extensions.FileProviders.Physical.PollingFileChangeToken" /> that polls the specified file for changes as
            determined by <see cref="P:System.IO.FileSystemInfo.LastWriteTimeUtc" />.
            </summary>
            <param name="fileInfo">The <see cref="T:System.IO.FileInfo"/> to poll</param>
        </member>
        <member name="P:Microsoft.Extensions.FileProviders.Physical.PollingFileChangeToken.ActiveChangeCallbacks">
            <summary>
            Always false.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.FileProviders.Physical.PollingFileChangeToken.HasChanged">
            <summary>
            True when the file has changed since the change token was created. Once the file changes, this value is always true
            </summary>
            <remarks>
            Once true, the value will always be true. Change tokens should not re-used once expired. The caller should discard this
            instance once it sees <see cref="P:Microsoft.Extensions.FileProviders.Physical.PollingFileChangeToken.HasChanged" /> is true.
            </remarks>
        </member>
        <member name="M:Microsoft.Extensions.FileProviders.Physical.PollingFileChangeToken.RegisterChangeCallback(System.Action{System.Object},System.Object)">
            <summary>
            Does not actually register callbacks.
            </summary>
            <param name="callback">This parameter is ignored</param>
            <param name="state">This parameter is ignored</param>
            <returns>A disposable object that noops when disposed</returns>
        </member>
        <member name="T:Microsoft.Extensions.FileProviders.Physical.PollingWildCardChangeToken">
            <summary>
            A polling based <see cref="T:Microsoft.Extensions.Primitives.IChangeToken"/> for wildcard patterns.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.FileProviders.Physical.PollingWildCardChangeToken.#ctor(System.String,System.String)">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.Extensions.FileProviders.Physical.PollingWildCardChangeToken"/>.
            </summary>
            <param name="root">The root of the file system.</param>
            <param name="pattern">The pattern to watch.</param>
        </member>
        <member name="P:Microsoft.Extensions.FileProviders.Physical.PollingWildCardChangeToken.ActiveChangeCallbacks">
            <inheritdoc />
        </member>
        <member name="P:Microsoft.Extensions.FileProviders.Physical.PollingWildCardChangeToken.HasChanged">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.Extensions.FileProviders.Physical.PollingWildCardChangeToken.GetLastWriteUtc(System.String)">
            <summary>
            Gets the last write time of the file at the specified <paramref name="path"/>.
            </summary>
            <param name="path">The root relative path.</param>
            <returns>The <see cref="T:System.DateTime"/> that the file was last modified.</returns>
        </member>
        <member name="T:Microsoft.Extensions.FileProviders.Internal.PhysicalDirectoryContents">
            <summary>
            Represents the contents of a physical file directory
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.FileProviders.Internal.PhysicalDirectoryContents.#ctor(System.String)">
            <summary>
            Initializes an instance of <see cref="T:Microsoft.Extensions.FileProviders.Internal.PhysicalDirectoryContents"/>
            </summary>
            <param name="directory">The directory</param>
        </member>
        <member name="M:Microsoft.Extensions.FileProviders.Internal.PhysicalDirectoryContents.#ctor(System.String,Microsoft.Extensions.FileProviders.Physical.ExclusionFilters)">
            <summary>
            Initializes an instance of <see cref="T:Microsoft.Extensions.FileProviders.Internal.PhysicalDirectoryContents"/>
            </summary>
            <param name="directory">The directory</param>
            <param name="filters">Specifies which files or directories are excluded from enumeration.</param>
        </member>
        <member name="P:Microsoft.Extensions.FileProviders.Internal.PhysicalDirectoryContents.Exists">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.Extensions.FileProviders.Internal.PhysicalDirectoryContents.GetEnumerator">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.Extensions.FileProviders.PhysicalFileProvider">
            <summary>
            Looks up files using the on-disk file system
            </summary>
            <remarks>
            When the environment variable "DOTNET_USE_POLLING_FILE_WATCHER" is set to "1" or "true", calls to
            <see cref="M:Microsoft.Extensions.FileProviders.PhysicalFileProvider.Watch(System.String)" /> will use <see cref="T:Microsoft.Extensions.FileProviders.Physical.PollingFileChangeToken" />.
            </remarks>
        </member>
        <member name="M:Microsoft.Extensions.FileProviders.PhysicalFileProvider.#ctor(System.String)">
            <summary>
            Initializes a new instance of a PhysicalFileProvider at the given root directory.
            </summary>
            <param name="root">The root directory. This should be an absolute path.</param>
        </member>
        <member name="M:Microsoft.Extensions.FileProviders.PhysicalFileProvider.#ctor(System.String,Microsoft.Extensions.FileProviders.Physical.ExclusionFilters)">
            <summary>
            Initializes a new instance of a PhysicalFileProvider at the given root directory.
            </summary>
            <param name="root">The root directory. This should be an absolute path.</param>
            <param name="filters">Specifies which files or directories are excluded.</param>
        </member>
        <member name="P:Microsoft.Extensions.FileProviders.PhysicalFileProvider.UsePollingFileWatcher">
            <summary>
            Gets or sets a value that determines if this instance of <see cref="T:Microsoft.Extensions.FileProviders.PhysicalFileProvider"/>
            uses polling to determine file changes.
            <para>
            By default, <see cref="T:Microsoft.Extensions.FileProviders.PhysicalFileProvider"/>  uses <see cref="T:System.IO.FileSystemWatcher"/> to listen to file change events
            for <see cref="M:Microsoft.Extensions.FileProviders.PhysicalFileProvider.Watch(System.String)"/>. <see cref="T:System.IO.FileSystemWatcher"/> is ineffective in some scenarios such as mounted drives.
            Polling is required to effectively watch for file changes.
            </para>
            <seealso cref="P:Microsoft.Extensions.FileProviders.PhysicalFileProvider.UseActivePolling"/>.
            </summary>
            <value>
            The default value of this property is determined by the value of environment variable named <c>DOTNET_USE_POLLING_FILE_WATCHER</c>.
            When <c>true</c> or <c>1</c>, this property defaults to <c>true</c>; otherwise false.
            </value>
        </member>
        <member name="P:Microsoft.Extensions.FileProviders.PhysicalFileProvider.UseActivePolling">
            <summary>
            Gets or sets a value that determines if this instance of <see cref="T:Microsoft.Extensions.FileProviders.PhysicalFileProvider"/>
            actively polls for file changes.
            <para>
            When <see langword="true"/>, <see cref="T:Microsoft.Extensions.Primitives.IChangeToken"/> returned by <see cref="M:Microsoft.Extensions.FileProviders.PhysicalFileProvider.Watch(System.String)"/> will actively poll for file changes
            (<see cref="P:Microsoft.Extensions.Primitives.IChangeToken.ActiveChangeCallbacks"/> will be <see langword="true"/>) instead of being passive.
            </para>
            <para>
            This property is only effective when <see cref="P:Microsoft.Extensions.FileProviders.PhysicalFileProvider.UsePollingFileWatcher"/> is set.
            </para>
            </summary>
            <value>
            The default value of this property is determined by the value of environment variable named <c>DOTNET_USE_POLLING_FILE_WATCHER</c>.
            When <c>true</c> or <c>1</c>, this property defaults to <c>true</c>; otherwise false.
            </value>
        </member>
        <member name="M:Microsoft.Extensions.FileProviders.PhysicalFileProvider.Dispose">
            <summary>
            Disposes the provider. Change tokens may not trigger after the provider is disposed.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.FileProviders.PhysicalFileProvider.Dispose(System.Boolean)">
            <summary>
            Disposes the provider.
            </summary>
            <param name="disposing"><c>true</c> is invoked from <see cref="M:System.IDisposable.Dispose"/>.</param>
        </member>
        <member name="P:Microsoft.Extensions.FileProviders.PhysicalFileProvider.Root">
            <summary>
            The root directory for this instance.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.FileProviders.PhysicalFileProvider.GetFileInfo(System.String)">
            <summary>
            Locate a file at the given path by directly mapping path segments to physical directories.
            </summary>
            <param name="subpath">A path under the root directory</param>
            <returns>The file information. Caller must check <see cref="P:Microsoft.Extensions.FileProviders.IFileInfo.Exists"/> property. </returns>
        </member>
        <member name="M:Microsoft.Extensions.FileProviders.PhysicalFileProvider.GetDirectoryContents(System.String)">
            <summary>
            Enumerate a directory at the given path, if any.
            </summary>
            <param name="subpath">A path under the root directory. Leading slashes are ignored.</param>
            <returns>
            Contents of the directory. Caller must check <see cref="P:Microsoft.Extensions.FileProviders.IDirectoryContents.Exists"/> property. <see cref="T:Microsoft.Extensions.FileProviders.NotFoundDirectoryContents" /> if
            <paramref name="subpath" /> is absolute, if the directory does not exist, or <paramref name="subpath" /> has invalid
            characters.
            </returns>
        </member>
        <member name="M:Microsoft.Extensions.FileProviders.PhysicalFileProvider.Watch(System.String)">
            <summary>
                <para>Creates a <see cref="T:Microsoft.Extensions.Primitives.IChangeToken" /> for the specified <paramref name="filter" />.</para>
                <para>Globbing patterns are interpreted by <seealso cref="T:Microsoft.Extensions.FileSystemGlobbing.Matcher" />.</para>
            </summary>
            <param name="filter">
            Filter string used to determine what files or folders to monitor. Example: **/*.cs, *.*,
            subFolder/**/*.cshtml.
            </param>
            <returns>
            An <see cref="T:Microsoft.Extensions.Primitives.IChangeToken" /> that is notified when a file matching <paramref name="filter" /> is added,
            modified or deleted. Returns a <see cref="T:Microsoft.Extensions.FileProviders.NullChangeToken" /> if <paramref name="filter" /> has invalid filter
            characters or if <paramref name="filter" /> is an absolute path or outside the root directory specified in the
            constructor <seealso cref="M:Microsoft.Extensions.FileProviders.PhysicalFileProvider.#ctor(System.String)" />.
            </returns>
        </member>
        <member name="M:System.ThrowHelper.ThrowIfNull(System.Object,System.String)">
            <summary>Throws an <see cref="T:System.ArgumentNullException"/> if <paramref name="argument"/> is null.</summary>
            <param name="argument">The reference type argument to validate as non-null.</param>
            <param name="paramName">The name of the parameter with which <paramref name="argument"/> corresponds.</param>
        </member>
        <member name="M:System.ThrowHelper.IfNullOrWhitespace(System.String,System.String)">
            <summary>
            Throws either an <see cref="T:System.ArgumentNullException"/> or an <see cref="T:System.ArgumentException"/>
            if the specified string is <see langword="null"/> or whitespace respectively.
            </summary>
            <param name="argument">String to be checked for <see langword="null"/> or whitespace.</param>
            <param name="paramName">The name of the parameter being checked.</param>
            <returns>The original value of <paramref name="argument"/>.</returns>
        </member>
        <member name="T:System.Runtime.InteropServices.LibraryImportAttribute">
            <summary>
            Attribute used to indicate a source generator should create a function for marshalling
            arguments instead of relying on the runtime to generate an equivalent marshalling function at run-time.
            </summary>
            <remarks>
            This attribute is meaningless if the source generator associated with it is not enabled.
            The current built-in source generator only supports C# and only supplies an implementation when
            applied to static, partial, non-generic methods.
            </remarks>
        </member>
        <member name="M:System.Runtime.InteropServices.LibraryImportAttribute.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Runtime.InteropServices.LibraryImportAttribute"/>.
            </summary>
            <param name="libraryName">Name of the library containing the import.</param>
        </member>
        <member name="P:System.Runtime.InteropServices.LibraryImportAttribute.LibraryName">
            <summary>
            Gets the name of the library containing the import.
            </summary>
        </member>
        <member name="P:System.Runtime.InteropServices.LibraryImportAttribute.EntryPoint">
            <summary>
            Gets or sets the name of the entry point to be called.
            </summary>
        </member>
        <member name="P:System.Runtime.InteropServices.LibraryImportAttribute.StringMarshalling">
            <summary>
            Gets or sets how to marshal string arguments to the method.
            </summary>
            <remarks>
            If this field is set to a value other than <see cref="F:System.Runtime.InteropServices.StringMarshalling.Custom" />,
            <see cref="P:System.Runtime.InteropServices.LibraryImportAttribute.StringMarshallingCustomType" /> must not be specified.
            </remarks>
        </member>
        <member name="P:System.Runtime.InteropServices.LibraryImportAttribute.StringMarshallingCustomType">
            <summary>
            Gets or sets the <see cref="T:System.Type"/> used to control how string arguments to the method are marshalled.
            </summary>
            <remarks>
            If this field is specified, <see cref="P:System.Runtime.InteropServices.LibraryImportAttribute.StringMarshalling" /> must not be specified
            or must be set to <see cref="F:System.Runtime.InteropServices.StringMarshalling.Custom" />.
            </remarks>
        </member>
        <member name="P:System.Runtime.InteropServices.LibraryImportAttribute.SetLastError">
            <summary>
            Gets or sets whether the callee sets an error (SetLastError on Windows or errno
            on other platforms) before returning from the attributed method.
            </summary>
        </member>
        <member name="T:System.Runtime.InteropServices.StringMarshalling">
            <summary>
            Specifies how strings should be marshalled for generated p/invokes
            </summary>
        </member>
        <member name="F:System.Runtime.InteropServices.StringMarshalling.Custom">
            <summary>
            Indicates the user is suppling a specific marshaller in <see cref="P:System.Runtime.InteropServices.LibraryImportAttribute.StringMarshallingCustomType"/>.
            </summary>
        </member>
        <member name="F:System.Runtime.InteropServices.StringMarshalling.Utf8">
            <summary>
            Use the platform-provided UTF-8 marshaller.
            </summary>
        </member>
        <member name="F:System.Runtime.InteropServices.StringMarshalling.Utf16">
            <summary>
            Use the platform-provided UTF-16 marshaller.
            </summary>
        </member>
        <member name="T:System.Runtime.Versioning.OSPlatformAttribute">
            <summary>
            Base type for all platform-specific API attributes.
            </summary>
        </member>
        <member name="T:System.Runtime.Versioning.TargetPlatformAttribute">
            <summary>
            Records the platform that the project targeted.
            </summary>
        </member>
        <member name="T:System.Runtime.Versioning.SupportedOSPlatformAttribute">
             <summary>
             Records the operating system (and minimum version) that supports an API. Multiple attributes can be
             applied to indicate support on multiple operating systems.
             </summary>
             <remarks>
             Callers can apply a <see cref="T:System.Runtime.Versioning.SupportedOSPlatformAttribute" />
             or use guards to prevent calls to APIs on unsupported operating systems.
            
             A given platform should only be specified once.
             </remarks>
        </member>
        <member name="T:System.Runtime.Versioning.UnsupportedOSPlatformAttribute">
            <summary>
            Marks APIs that were removed in a given operating system version.
            </summary>
            <remarks>
            Primarily used by OS bindings to indicate APIs that are only available in
            earlier versions.
            </remarks>
        </member>
        <member name="T:System.Runtime.Versioning.ObsoletedOSPlatformAttribute">
            <summary>
            Marks APIs that were obsoleted in a given operating system version.
            </summary>
            <remarks>
            Primarily used by OS bindings to indicate APIs that should not be used anymore.
            </remarks>
        </member>
        <member name="T:System.Runtime.Versioning.SupportedOSPlatformGuardAttribute">
             <summary>
             Annotates a custom guard field, property or method with a supported platform name and optional version.
             Multiple attributes can be applied to indicate guard for multiple supported platforms.
             </summary>
             <remarks>
             Callers can apply a <see cref="T:System.Runtime.Versioning.SupportedOSPlatformGuardAttribute" /> to a field, property or method
             and use that field, property or method in a conditional or assert statements in order to safely call platform specific APIs.
            
             The type of the field or property should be boolean, the method return type should be boolean in order to be used as platform guard.
             </remarks>
        </member>
        <member name="T:System.Runtime.Versioning.UnsupportedOSPlatformGuardAttribute">
             <summary>
             Annotates the custom guard field, property or method with an unsupported platform name and optional version.
             Multiple attributes can be applied to indicate guard for multiple unsupported platforms.
             </summary>
             <remarks>
             Callers can apply a <see cref="T:System.Runtime.Versioning.UnsupportedOSPlatformGuardAttribute" /> to a field, property or method
             and use that  field, property or method in a conditional or assert statements as a guard to safely call APIs unsupported on those platforms.
            
             The type of the field or property should be boolean, the method return type should be boolean in order to be used as platform guard.
             </remarks>
        </member>
        <member name="T:System.Security.Cryptography.IncrementalHash">
            <summary>
            Provides support for computing a hash or HMAC value incrementally across several segments.
            </summary>
        </member>
        <member name="P:System.Security.Cryptography.IncrementalHash.AlgorithmName">
            <summary>
            Get the name of the algorithm being performed.
            </summary>
        </member>
        <member name="M:System.Security.Cryptography.IncrementalHash.AppendData(System.Byte[])">
            <summary>
            Append the entire contents of <paramref name="data"/> to the data already processed in the hash or HMAC.
            </summary>
            <param name="data">The data to process.</param>
            <exception cref="T:System.ArgumentNullException"><paramref name="data"/> is <c>null</c>.</exception>
            <exception cref="T:System.ObjectDisposedException">The object has already been disposed.</exception>
        </member>
        <member name="M:System.Security.Cryptography.IncrementalHash.AppendData(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Append <paramref name="count"/> bytes of <paramref name="data"/>, starting at <paramref name="offset"/>,
            to the data already processed in the hash or HMAC.
            </summary>
            <param name="data">The data to process.</param>
            <param name="offset">The offset into the byte array from which to begin using data.</param>
            <param name="count">The number of bytes in the array to use as data.</param>
            <exception cref="T:System.ArgumentNullException"><paramref name="data"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">
                <paramref name="offset"/> is out of range. This parameter requires a non-negative number.
            </exception>
            <exception cref="T:System.ArgumentOutOfRangeException">
                <paramref name="count"/> is out of range. This parameter requires a non-negative number less than
                the <see cref="P:System.Array.Length"/> value of <paramref name="data"/>.
                </exception>
            <exception cref="T:System.ArgumentException">
                <paramref name="count"/> is greater than
                <paramref name="data"/>.<see cref="P:System.Array.Length"/> - <paramref name="offset"/>.
            </exception>
            <exception cref="T:System.ObjectDisposedException">The object has already been disposed.</exception>
        </member>
        <member name="M:System.Security.Cryptography.IncrementalHash.GetHashAndReset">
            <summary>
            Retrieve the hash or HMAC for the data accumulated from prior calls to
            <see cref="M:System.Security.Cryptography.IncrementalHash.AppendData(System.Byte[])"/>, and return to the state the object
            was in at construction.
            </summary>
            <returns>The computed hash or HMAC.</returns>
            <exception cref="T:System.ObjectDisposedException">The object has already been disposed.</exception>
        </member>
        <member name="M:System.Security.Cryptography.IncrementalHash.Dispose">
            <summary>
            Release all resources used by the current instance of the
            <see cref="T:System.Security.Cryptography.IncrementalHash"/> class.
            </summary>
        </member>
        <member name="M:System.Security.Cryptography.IncrementalHash.CreateHash(System.Security.Cryptography.HashAlgorithmName)">
            <summary>
            Create an <see cref="T:System.Security.Cryptography.IncrementalHash"/> for the algorithm specified by <paramref name="hashAlgorithm"/>.
            </summary>
            <param name="hashAlgorithm">The name of the hash algorithm to perform.</param>
            <returns>
            An <see cref="T:System.Security.Cryptography.IncrementalHash"/> instance ready to compute the hash algorithm specified
            by <paramref name="hashAlgorithm"/>.
            </returns>
            <exception cref="T:System.ArgumentException">
                <paramref name="hashAlgorithm"/>.<see cref="P:System.Security.Cryptography.HashAlgorithmName.Name"/> is <c>null</c>, or
                the empty string.
            </exception>
            <exception cref="T:System.Security.Cryptography.CryptographicException"><paramref name="hashAlgorithm"/> is not a known hash algorithm.</exception>
        </member>
        <member name="M:System.Security.Cryptography.IncrementalHash.CreateHMAC(System.Security.Cryptography.HashAlgorithmName,System.Byte[])">
            <summary>
            Create an <see cref="T:System.Security.Cryptography.IncrementalHash"/> for the Hash-based Message Authentication Code (HMAC)
            algorithm utilizing the hash algorithm specified by <paramref name="hashAlgorithm"/>, and a
            key specified by <paramref name="key"/>.
            </summary>
            <param name="hashAlgorithm">The name of the hash algorithm to perform within the HMAC.</param>
            <param name="key">
                The secret key for the HMAC. The key can be any length, but a key longer than the output size
                of the hash algorithm specified by <paramref name="hashAlgorithm"/> will be hashed (using the
                algorithm specified by <paramref name="hashAlgorithm"/>) to derive a correctly-sized key. Therefore,
                the recommended size of the secret key is the output size of the hash specified by
                <paramref name="hashAlgorithm"/>.
            </param>
            <returns>
            An <see cref="T:System.Security.Cryptography.IncrementalHash"/> instance ready to compute the hash algorithm specified
            by <paramref name="hashAlgorithm"/>.
            </returns>
            <exception cref="T:System.ArgumentException">
                <paramref name="hashAlgorithm"/>.<see cref="P:System.Security.Cryptography.HashAlgorithmName.Name"/> is <c>null</c>, or
                the empty string.
            </exception>
            <exception cref="T:System.Security.Cryptography.CryptographicException"><paramref name="hashAlgorithm"/> is not a known hash algorithm.</exception>
        </member>
        <member name="P:System.SR.Argument_InvalidOffLen">
            <summary>Offset and length were out of bounds for the array or count is greater than the number of elements from index to the end of the source collection.</summary>
        </member>
        <member name="P:System.SR.ArgumentOutOfRange_NeedNonNegNum">
            <summary>Non-negative number required.</summary>
        </member>
        <member name="P:System.SR.Error_FileSystemWatcherRequiredWithoutPolling">
            <summary>The fileSystemWatcher parameter must be non-null when pollForChanges is false.</summary>
        </member>
        <member name="P:System.SR.CannotCreateStream">
            <summary>Cannot create a stream for a directory.</summary>
        </member>
        <member name="P:System.SR.CannotModifyWhenFileWatcherInitialized">
            <summary>Cannot modify {0} once file watcher has been initialized.</summary>
        </member>
        <member name="P:System.SR.Cryptography_HashAlgorithmNameNullOrEmpty">
            <summary>The hash algorithm name cannot be null or empty.</summary>
        </member>
        <member name="P:System.SR.UnexpectedFileSystemInfo">
            <summary>Unexpected type of FileSystemInfo</summary>
        </member>
        <member name="P:System.SR.FileSystemWatcher_PlatformNotSupported">
            <summary>The type '{0}' is not supported on this platform, use polling instead.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.AllowNullAttribute">
            <summary>Specifies that null is allowed as an input even if the corresponding type disallows it.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.DisallowNullAttribute">
            <summary>Specifies that null is disallowed as an input even if the corresponding type allows it.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.MaybeNullAttribute">
            <summary>Specifies that an output may be null even if the corresponding type disallows it.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.NotNullAttribute">
            <summary>Specifies that an output will not be null even if the corresponding type allows it. Specifies that an input argument was not null when the call returns.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.MaybeNullWhenAttribute">
            <summary>Specifies that when a method returns <see cref="P:System.Diagnostics.CodeAnalysis.MaybeNullWhenAttribute.ReturnValue"/>, the parameter may be null even if the corresponding type disallows it.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MaybeNullWhenAttribute.#ctor(System.Boolean)">
            <summary>Initializes the attribute with the specified return value condition.</summary>
            <param name="returnValue">
            The return value condition. If the method returns this value, the associated parameter may be null.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.MaybeNullWhenAttribute.ReturnValue">
            <summary>Gets the return value condition.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.NotNullWhenAttribute">
            <summary>Specifies that when a method returns <see cref="P:System.Diagnostics.CodeAnalysis.NotNullWhenAttribute.ReturnValue"/>, the parameter will not be null even if the corresponding type allows it.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.NotNullWhenAttribute.#ctor(System.Boolean)">
            <summary>Initializes the attribute with the specified return value condition.</summary>
            <param name="returnValue">
            The return value condition. If the method returns this value, the associated parameter will not be null.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.NotNullWhenAttribute.ReturnValue">
            <summary>Gets the return value condition.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.NotNullIfNotNullAttribute">
            <summary>Specifies that the output will be non-null if the named parameter is non-null.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.NotNullIfNotNullAttribute.#ctor(System.String)">
            <summary>Initializes the attribute with the associated parameter name.</summary>
            <param name="parameterName">
            The associated parameter name.  The output will be non-null if the argument to the parameter specified is non-null.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.NotNullIfNotNullAttribute.ParameterName">
            <summary>Gets the associated parameter name.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.DoesNotReturnAttribute">
            <summary>Applied to a method that will never return under any circumstance.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.DoesNotReturnIfAttribute">
            <summary>Specifies that the method will not return if the associated Boolean parameter is passed the specified value.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.DoesNotReturnIfAttribute.#ctor(System.Boolean)">
            <summary>Initializes the attribute with the specified parameter value.</summary>
            <param name="parameterValue">
            The condition parameter value. Code after the method will be considered unreachable by diagnostics if the argument to
            the associated parameter matches this value.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.DoesNotReturnIfAttribute.ParameterValue">
            <summary>Gets the condition parameter value.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.MemberNotNullAttribute">
            <summary>Specifies that the method or property will ensure that the listed field and property members have not-null values.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MemberNotNullAttribute.#ctor(System.String)">
            <summary>Initializes the attribute with a field or property member.</summary>
            <param name="member">
            The field or property member that is promised to be not-null.
            </param>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MemberNotNullAttribute.#ctor(System.String[])">
            <summary>Initializes the attribute with the list of field and property members.</summary>
            <param name="members">
            The list of field and property members that are promised to be not-null.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.MemberNotNullAttribute.Members">
            <summary>Gets field or property member names.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.MemberNotNullWhenAttribute">
            <summary>Specifies that the method or property will ensure that the listed field and property members have not-null values when returning with the specified return value condition.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MemberNotNullWhenAttribute.#ctor(System.Boolean,System.String)">
            <summary>Initializes the attribute with the specified return value condition and a field or property member.</summary>
            <param name="returnValue">
            The return value condition. If the method returns this value, the associated parameter will not be null.
            </param>
            <param name="member">
            The field or property member that is promised to be not-null.
            </param>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MemberNotNullWhenAttribute.#ctor(System.Boolean,System.String[])">
            <summary>Initializes the attribute with the specified return value condition and list of field and property members.</summary>
            <param name="returnValue">
            The return value condition. If the method returns this value, the associated parameter will not be null.
            </param>
            <param name="members">
            The list of field and property members that are promised to be not-null.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.MemberNotNullWhenAttribute.ReturnValue">
            <summary>Gets the return value condition.</summary>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.MemberNotNullWhenAttribute.Members">
            <summary>Gets field or property member names.</summary>
        </member>
    </members>
</doc>
