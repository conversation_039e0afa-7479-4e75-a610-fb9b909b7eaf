@model OrderFlowCore.Application.DTOs.DistributionDto
@using OrderFlowCore.Core.Entities
@{
    ViewData["Title"] = "إدارة التوزيع";
    var assistantManagerTypes = new[]
    {
        AssistantManagerType.A1,
        AssistantManagerType.A2,
        AssistantManagerType.A3,
        AssistantManagerType.A4,
        AssistantManagerType.B
    };
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="page-title">إدارة التوزيع</h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="@Url.Action("Index", "Dashboard")">لوحة التحكم</a></li>
                            <li class="breadcrumb-item active" aria-current="page">إدارة التوزيع</li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="card-title mb-0">إدارة التوزيع الديناميكي للأقسام</h5>
                </div>
                <div class="row">
                    <!-- القسم الأيمن - الإحصائيات -->
                    <div class="col-md-4 mb-4">
                        <div class="card">
                            <div class="card-header bg-light">
                                <h5 class="mb-0">إحصائيات التوزيع</h5>
                            </div>
                            <div class="card-body">
                                @if (Model.DistributionStats.Any())
                                {
                                    <div class="table-responsive">
                                        <table class="table table-striped table-bordered">
                                            <thead>
                                                <tr>
                                                    <th>مساعد المدير</th>
                                                    <th>عدد الأقسام</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @foreach (var stat in Model.DistributionStats)
                                                {
                                                    <tr>
                                                        <td>@stat.AssistantManager</td>
                                                        <td>@stat.TotalDepartments</td>
                                                    </tr>
                                                }
                                            </tbody>
                                        </table>
                                    </div>
                                }
                                else
                                {
                                    <p class="text-muted">لا توجد إحصائيات متاحة</p>
                                }
                            </div>
                        </div>

                        <!-- القسم السفلي - تعديل قسم واحد -->
                        <div class="card mt-4">
                            <div class="card-header bg-light">
                                <h5 class="mb-0">تعديل قسم فردي</h5>
                            </div>
                            <div class="card-body">
                                <form asp-action="UpdateDistribution" method="post">
                                    <div class="form-group mb-3">
                                        <label>اختر القسم:</label>
                                        <select name="DepartmentName" class="form-control" required>
                                            <option value="">-- اختر القسم --</option>
                                            @foreach (var dept in Model.AvailableDepartments)
                                            {
                                                <option value="@dept">@dept</option>
                                            }
                                        </select>
                                    </div>
                                    <div class="form-group mb-3">
                                        <label>اختر مساعد المدير:</label>
                                        <select name="AssistantManagerId" class="form-control" required>
                                            <option value="">-- اختر مساعد المدير --</option>
                                            @foreach (var type in assistantManagerTypes)
                                            {
                                                <option value="@type.ToId()">@type.ToDisplayName()</option>
                                            }
                                        </select>
                                    </div>
                                    <button type="submit" class="btn btn-primary w-100">حفظ التعديل</button>
                                </form>
                            </div>
                        </div>

                        <!-- Distribution Report Button -->
                        <div class="card mt-4">
                            <div class="card-header bg-light">
                                <h5 class="mb-0">تقرير التوزيع</h5>
                            </div>
                            <div class="card-body">
                                <button type="button" class="btn btn-info w-100" onclick="generateDistributionReport()">
                                    <i class="fas fa-chart-bar me-2"></i>إنشاء تقرير التوزيع
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- القسم الأيسر - توزيع المجموعات والعرض -->
                    <div class="col-md-8">
                        <!-- توزيع مجموعات الأقسام -->
                        <div class="card mb-4">
                            <div class="card-header bg-light">
                                <h5 class="mb-0">توزيع مجموعات الأقسام</h5>
                            </div>
                            <div class="card-body">
                                <form asp-action="BulkUpdateDistribution" method="post">
                                    <div class="row">
                                        <div class="col-md-8">
                                            <div class="form-group">
                                                <label>اختر الأقسام:</label>
                                                <select name="SelectedDepartments" multiple class="form-control"
                                                        style="height: 300px;" required>
                                                    @foreach (var dept in Model.AvailableDepartments)
                                                    {
                                                        <option value="@dept">@dept</option>
                                                    }
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label>اختر مساعد المدير:</label>
                                                <select name="AssistantManagerId" class="form-control mb-3" required>
                                                    <option value="">-- اختر مساعد المدير --</option>
                                                    @foreach (var type in assistantManagerTypes)
                                                    {
                                                        <option value="@type.ToId()">@type.ToDisplayName()</option>
                                                    }
                                                </select>
                                                <button type="submit" class="btn btn-primary w-100">تعيين المجموعة</button>
                                            </div>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>

                        <!-- عرض التوزيع الحالي -->
                        <div class="card">
                            <div class="card-header bg-light">
                                <h5 class="mb-0">التوزيع الحالي للأقسام</h5>
                            </div>
                            <div class="card-body">
                                @if (Model.DepartmentDistribution.Any())
                                {
                                    <div class="table-responsive">
                                        <table class="table table-striped table-bordered">
                                            <thead class="table-dark">
                                                <tr>
                                                    <th>اسم القسم</th>
                                                    <th>مساعد المدير</th>
                                                    <th>الحالة</th>
                                                    <th>الإجراءات</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @foreach (var dist in Model.DepartmentDistribution)
                                                {
                                                    <tr>
                                                        <td><strong>@dist.DepartmentName</strong></td>
                                                        <td>
                                                            @if (dist.AssistantManagerId == AssistantManagerType.Unknown)
                                                            {
                                                                <span class="badge bg-secondary">@dist.AssistantManagerId.ToDisplayName()</span>
                                                            }
                                                            else
                                                            {
                                                                <span class="badge bg-success">@dist.AssistantManagerId.ToDisplayName()</span>
                                                            }
                                                        </td>
                                                        <td>
                                                            @if (dist.AssistantManagerId == AssistantManagerType.Unknown)
                                                            {
                                                                <span class="badge bg-warning">غير موزع</span>
                                                            }
                                                            else
                                                            {
                                                                <span class="badge bg-primary">موزع</span>
                                                            }
                                                        </td>
                                                        <td>
                                                            @if (dist.AssistantManagerId != AssistantManagerType.Unknown)
                                                            {
                                                                <form asp-action="ClearDistribution" method="post" style="display: inline;">
                                                                    <input type="hidden" name="departmentName" value="@dist.DepartmentName" />
                                                                    <button type="submit" class="btn btn-sm btn-outline-warning"
                                                                            onclick="return confirm('هل أنت متأكد من إلغاء توزيع هذا القسم؟')"
                                                                            title="إلغاء التوزيع">
                                                                        <i class="fas fa-times"></i>
                                                                    </button>
                                                                </form>
                                                            }
                                                            else
                                                            {
                                                                <span class="text-muted">-</span>
                                                            }
                                                        </td>
                                                    </tr>
                                                }
                                            </tbody>
                                        </table>
                                    </div>
                                }
                                else
                                {
                                    <div class="text-center py-5">
                                        <i class="fas fa-sitemap fa-3x text-muted mb-3"></i>
                                        <h5 class="text-muted">لا توجد بيانات توزيع متاحة</h5>
                                        <p class="text-muted">قم بإضافة أقسام أولاً لبدء التوزيع</p>
                                    </div>
                                }
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .card {
        border-radius: 0.75rem;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        transition: box-shadow 0.3s ease;
    }

    .card:hover {
        box-shadow: 0 4px 16px rgba(0,0,0,0.15);
    }

    .card-header {
        border-radius: 0.75rem 0.75rem 0 0 !important;
        border-bottom: 2px solid #e9ecef;
    }

    .table th {
        font-weight: 600;
        border-top: none;
    }

    .badge {
        font-size: 0.85em;
        padding: 0.5em 0.75em;
    }

    .btn-group .btn {
        border-radius: 0.375rem;
        margin: 0 2px;
    }

    .form-control:focus {
        border-color: #80bdff;
        box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
    }

    .distribution-report h4 {
        color: #495057;
        margin-bottom: 1.5rem;
    }

    .distribution-report .bg-primary,
    .distribution-report .bg-success,
    .distribution-report .bg-warning {
        border-radius: 0.5rem;
    }

    @@media (max-width: 768px) {
        .col-md-4 {
            margin-bottom: 1rem;
        }

        .table-responsive {
            font-size: 0.9rem;
        }

        .btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.8rem;
        }
    }
</style>

@section Scripts {
    <script>
        // Show Toastr notifications
    @if (TempData["SuccessMessage"] != null)
    {
        <text>
            toastr.success('@TempData["SuccessMessage"]');
        </text>
    }
    @if (TempData["ErrorMessage"] != null)
    {
        <text>
            toastr.error('@TempData["ErrorMessage"]');
        </text>
    }

        // Generate Distribution Report
        function generateDistributionReport() {
            fetch('@Url.Action("GetDistributionReport")')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showDistributionReport(data.data);
                    } else {
                        toastr.error(data.message || 'حدث خطأ أثناء إنشاء التقرير');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    toastr.error('حدث خطأ أثناء إنشاء التقرير');
                });
        }

        function showDistributionReport(reportData) {
            let reportHtml = `
                <div class="distribution-report">
                    <h4>تقرير توزيع الأقسام</h4>
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <div class="text-center p-3 bg-primary text-white rounded">
                                <h5>${reportData.totalDepartments}</h5>
                                <p class="mb-0">إجمالي الأقسام</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center p-3 bg-success text-white rounded">
                                <h5>${reportData.assignedDepartments}</h5>
                                <p class="mb-0">الأقسام الموزعة</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center p-3 bg-warning text-white rounded">
                                <h5>${reportData.unassignedDepartments}</h5>
                                <p class="mb-0">الأقسام غير الموزعة</p>
                            </div>
                        </div>
                    </div>
                    <h5>التوزيع حسب مساعد المدير:</h5>
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>مساعد المدير</th>
                                    <th>عدد الأقسام</th>
                                    <th>الأقسام</th>
                                </tr>
                            </thead>
                            <tbody>`;

            reportData.distributionByManager.forEach(manager => {
                reportHtml += `
                    <tr>
                        <td>${manager.managerName}</td>
                        <td class="text-center">${manager.departmentCount}</td>
                        <td>${manager.departments.join(', ')}</td>
                    </tr>`;
            });

            reportHtml += `
                            </tbody>
                        </table>
                    </div>
                </div>`;

            // Show in modal
            const modalHtml = `
                <div class="modal fade" id="reportModal" tabindex="-1" aria-hidden="true">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">تقرير توزيع الأقسام</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                ${reportHtml}
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                                <button type="button" class="btn btn-primary" onclick="printReport()">طباعة</button>
                            </div>
                        </div>
                    </div>
                </div>`;

            // Remove existing modal if any
            const existingModal = document.getElementById('reportModal');
            if (existingModal) {
                existingModal.remove();
            }

            // Add modal to body
            document.body.insertAdjacentHTML('beforeend', modalHtml);

            // Show modal
            const modal = new bootstrap.Modal(document.getElementById('reportModal'));
            modal.show();
        }

        function printReport() {
            const reportContent = document.querySelector('.distribution-report').innerHTML;
            const printWindow = window.open('', '_blank');
            printWindow.document.write(`
                
                    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
                    <style>
                        body { font-family: 'Arial', sans-serif; direction: rtl; }
                        .bg-primary, .bg-success, .bg-warning { color: white !important; }
                        @@media print {
                            .btn { display: none; }
                        }
                    </style>
                    
                    <div class="container mt-4">
                        ${reportContent}
                    </div>

            `);
            printWindow.document.close();
            printWindow.print();
        }

        // Enhance multi-select functionality
        document.addEventListener('DOMContentLoaded', function() {
            const multiSelect = document.querySelector('select[multiple]');
            if (multiSelect) {
                multiSelect.addEventListener('change', function() {
                    const selectedCount = this.selectedOptions.length;
                    const button = document.querySelector('button[type="submit"]');
                    if (button) {
                        button.textContent = selectedCount > 0 ?
                            `تعيين المجموعة (${selectedCount} قسم)` :
                            'تعيين المجموعة';
                    }
                });
            }
        });
    </script>
}
