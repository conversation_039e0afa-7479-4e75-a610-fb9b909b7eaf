<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Order Details Module Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .full-table-container {
            border: 3px solid #007bff;
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
            margin-top: 20px;
        }
        .details-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        .details-table th,
        .details-table td {
            border: 1px solid #dee2e6;
            padding: 12px;
            text-align: right;
        }
        .details-table th {
            background-color: #f8f9fa;
            font-weight: 600;
            width: 25%;
        }
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }
        .order-details {
            display: none;
        }
        .alert {
            border-radius: 8px;
            padding: 15px 20px;
            margin: 15px 0;
            border: none;
            font-weight: 500;
        }
        .alert-success {
            background-color: #d4edda;
            color: #155724;
            border-left: 4px solid #28a745;
        }
        .alert-danger {
            background-color: #f8d7da;
            color: #721c24;
            border-left: 4px solid #dc3545;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h2>Order Details Module Test</h2>
        
        <div class="mb-3">
            <label for="orderSelect" class="form-label">Select Order:</label>
            <select id="orderSelect" class="form-select">
                <option value="">Choose an order</option>
                <option value="1">Order 1</option>
                <option value="2">Order 2</option>
            </select>
        </div>

        <div id="messageContainer"></div>

        <div id="loading" class="loading">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2">Loading order details...</p>
        </div>

        <!-- Order Details Section -->
        <div id="orderDetails" class="order-details">
            <div class="full-table-container">
                <!-- Table 1: Basic Order Info -->
                <table class="details-table">
                    <tr>
                        <th>رقم الطلب</th>
                        <td id="orderNumber"></td>
                        <th>تاريخ الطلب</th>
                        <td id="orderDate"></td>
                    </tr>
                    <tr>
                        <th>حالة الطلب</th>
                        <td colspan="3" id="orderStatus"></td>
                    </tr>
                </table>

                <!-- Table 2: Employee Details -->
                <table class="details-table">
                    <tr>
                        <th>نوع الطلب</th>
                        <td colspan="3" id="orderType"></td>
                    </tr>
                    <tr>
                        <th>اسم الموظف</th>
                        <td id="employeeName"></td>
                        <th>القسم</th>
                        <td id="department"></td>
                    </tr>
                    <tr>
                        <th>الوظيفة</th>
                        <td id="jobTitle"></td>
                        <th></th>
                        <td></td>
                    </tr>
                    <tr>
                        <th>نوع التوظيف</th>
                        <td id="employmentType"></td>
                        <th>المؤهل</th>
                        <td id="qualification"></td>
                    </tr>
                    <tr>
                        <th>رقم الموظف</th>
                        <td id="employeeNumber"></td>
                        <th>السجل المدني</th>
                        <td id="civilRegistry"></td>
                    </tr>
                    <tr>
                        <th>الجنسية</th>
                        <td id="nationality"></td>
                        <th>رقم الجوال</th>
                        <td id="mobileNumber"></td>
                    </tr>
                    <tr id="notesRow" style="display: none;">
                        <td colspan="4">
                            <strong>تفاصيل مقدم الطلب:</strong><br />
                            <span id="notes"></span>
                        </td>
                    </tr>
                </table>

                <!-- Table 3: Approval Status -->
                <table class="details-table">
                    <tr>
                        <th>تم التأكيد/الإلغاء من مدير القسم</th>
                        <th>تم التأكيد/الإلغاء من قبل مساعد المدير</th>
                        <th>تم التحويل/الإلغاء من قبل المنسق</th>
                    </tr>
                    <tr>
                        <td id="managerApproval"></td>
                        <td id="supervisorApproval"></td>
                        <td id="coordinatorApproval"></td>
                    </tr>
                    <tr id="cancellationRow" style="display: none;">
                        <th>سبب الإلغاء/الإعادة</th>
                        <th colspan="2">تفاصيل المنسق</th>
                    </tr>
                    <tr id="cancellationDetailsRow" style="display: none;">
                        <td id="cancellationReason"></td>
                        <td colspan="2" id="coordinatorDetails"></td>
                    </tr>
                </table>

                <!-- Table 4: Supervisors Permissions -->
                <table class="details-table">
                    <tr>
                        <th>مشرف خدمات الموظفين</th>
                        <td id="medicalServicesPermission"></td>
                        <th>مشرف إدارة تخطيط الموارد البشرية</th>
                        <td id="hrPlanningPermission"></td>
                    </tr>
                    <tr>
                        <th>مشرف إدارة تقنية المعلومات</th>
                        <td id="itPermission"></td>
                        <th>مشرف مراقبة الدوام</th>
                        <td id="attendanceControlPermission"></td>
                    </tr>
                    <tr>
                        <th>مشرف السجلات الطبية</th>
                        <td id="medicalRecordsPermission"></td>
                        <th>مشرف إدارة الرواتب والاستحقاقات</th>
                        <td id="payrollPermission"></td>
                    </tr>
                    <tr>
                        <th>مشرف إدارة القانونية والالتزام</th>
                        <td id="legalCompliancePermission"></td>
                        <th>مشرف خدمات الموارد البشرية</th>
                        <td id="hrServicesPermission"></td>
                    </tr>
                    <tr>
                        <th>مشرف إدارة الإسكان</th>
                        <td id="housingPermission"></td>
                        <th>مشرف قسم الملفات</th>
                        <td id="filesSectionPermission"></td>
                    </tr>
                    <tr>
                        <th>مشرف العيادات الخارجية</th>
                        <td id="outpatientPermission"></td>
                        <th>مشرف التأمينات الاجتماعية</th>
                        <td id="socialInsurancePermission"></td>
                    </tr>
                    <tr>
                        <th>مشرف وحدة مراقبة المخزون</th>
                        <td id="inventoryControlPermission"></td>
                        <th>مشرف إدارة تنمية الإيرادات</th>
                        <td id="selfResourcesPermission"></td>
                    </tr>
                    <tr>
                        <th>مشرف إدارة الأمن و السلامة</th>
                        <td id="nursingPermission"></td>
                        <th>مشرف الطب الاتصالي</th>
                        <td id="employeeServicesPermission"></td>
                    </tr>
                </table>

                <!-- Table 5: HR Manager -->
                <table class="details-table">
                    <tr>
                        <th>مدير الموارد البشرية</th>
                        <td colspan="3" id="hrManagerApproval"></td>
                    </tr>
                </table>
            </div>
        </div>

        <div class="mt-3">
            <button id="testConfirmBtn" class="btn btn-success me-2">Test Confirm Order</button>
            <button id="testRejectBtn" class="btn btn-danger me-2">Test Reject Order</button>
            <button id="testMessageBtn" class="btn btn-info">Test Message</button>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="orderDetailsModule.js"></script>
    <script>
        $(document).ready(function () {
            // Initialize the module
            OrderDetailsModule.init({
                showLoading: function() {
                    $('#loading').show();
                    $('#orderDetails').hide();
                },
                hideLoading: function() {
                    $('#loading').hide();
                },
                showMessage: function(message, type) {
                    const alertClass = type === 'error' ? 'alert-danger' : 'alert-success';
                    const icon = type === 'error' ? '❌' : '✅';
                    $('#messageContainer').html(`<div class="alert ${alertClass}">${icon} ${message}</div>`);
                    
                    if (type === 'success') {
                        setTimeout(() => {
                            $('#messageContainer .alert').fadeOut();
                        }, 5000);
                    }
                },
                showOrderDetails: function() {
                    $('#orderDetails').show();
                },
                hideOrderDetails: function() {
                    $('#orderDetails').hide();
                }
            });

            // Test order selection
            $('#orderSelect').change(function () {
                const orderId = $(this).val();
                if (orderId) {
                    // Simulate loading order details
                    OrderDetailsModule.showLoading();
                    
                    setTimeout(() => {
                        // Mock data
                        const mockData = {
                            orderNumber: `ORD-${orderId}`,
                            orderDate: '2024-01-15',
                            orderStatus: 'قيد الانتظار',
                            orderType: 'طلب توظيف',
                            employeeName: 'أحمد محمد',
                            department: 'تقنية المعلومات',
                            jobTitle: 'مطور برمجيات',
                            employmentType: 'دوام كامل',
                            qualification: 'بكالوريوس',
                            employeeNumber: `EMP-${orderId}00`,
                            civilRegistry: '1234567890',
                            nationality: 'سعودي',
                            mobileNumber: '0501234567',
                            notes: 'طلب توظيف جديد',
                            managerApproval: 'موافق',
                            supervisorApproval: 'قيد الانتظار',
                            coordinatorApproval: 'قيد الانتظار',
                            hrManagerApproval: 'قيد الانتظار',
                            supervisorOfEmployeeServices: 'موافق',
                            supervisorOfHumanResourcesPlanning: 'قيد الانتظار',
                            supervisorOfInformationTechnology: 'موافق',
                            supervisorOfAttendance: 'قيد الانتظار',
                            supervisorOfMedicalRecords: 'قيد الانتظار',
                            supervisorOfPayrollAndBenefits: 'قيد الانتظار',
                            supervisorOfLegalAndCompliance: 'قيد الانتظار',
                            supervisorOfHumanResourcesServices: 'قيد الانتظار',
                            supervisorOfHousing: 'قيد الانتظار',
                            supervisorOfFiles: 'قيد الانتظار',
                            supervisorOfOutpatientClinics: 'قيد الانتظار',
                            supervisorOfSocialSecurity: 'قيد الانتظار',
                            supervisorOfInventoryControl: 'قيد الانتظار',
                            supervisorOfSelfResources: 'قيد الانتظار',
                            supervisorOfNursing: 'قيد الانتظار',
                            supervisorOfEmployeeServices: 'قيد الانتظار'
                        };
                        
                        OrderDetailsModule.populateOrderDetails(mockData);
                        OrderDetailsModule.hideLoading();
                        OrderDetailsModule.showOrderDetails();
                        OrderDetailsModule.showMessage('تم تحميل تفاصيل الطلب بنجاح', 'success');
                    }, 1000);
                } else {
                    OrderDetailsModule.hideOrderDetails();
                }
            });

            // Test buttons
            $('#testConfirmBtn').click(function() {
                const orderId = OrderDetailsModule.getCurrentOrderId();
                if (orderId) {
                    OrderDetailsModule.confirmOrder(
                        orderId,
                        '/test/confirm',
                        'تم تأكيد الطلب بنجاح',
                        'حدث خطأ أثناء التأكيد'
                    );
                } else {
                    OrderDetailsModule.showMessage('يرجى اختيار طلب أولاً', 'error');
                }
            });

            $('#testRejectBtn').click(function() {
                const orderId = OrderDetailsModule.getCurrentOrderId();
                if (orderId) {
                    OrderDetailsModule.rejectOrder(
                        orderId,
                        'سبب تجريبي للإلغاء',
                        '/test/reject',
                        'تم إلغاء الطلب بنجاح',
                        'حدث خطأ أثناء الإلغاء'
                    );
                } else {
                    OrderDetailsModule.showMessage('يرجى اختيار طلب أولاً', 'error');
                }
            });

            $('#testMessageBtn').click(function() {
                OrderDetailsModule.showMessage('هذه رسالة تجريبية', 'success');
            });
        });
    </script>
</body>
</html> 