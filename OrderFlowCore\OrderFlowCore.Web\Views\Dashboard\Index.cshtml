@model OrderFlowCore.Web.ViewModels.DashboardViewModel

@{
    ViewData["Title"] = "لوحة التحكم";
}

<div class="container-fluid dashboard-container">
    @if (Model.HasError)
    {
        <div class="alert error-alert" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <strong>خطأ:</strong> @Model.ErrorMessage
        </div>
    }

    <!-- Welcome Header -->
    <div class="welcome-header">
        <div class="row align-items-center">
            <div class="col-md-2 text-center">
                <i class="fas fa-user-circle fa-4x"></i>
            </div>
            <div class="col-md-10">
                <h2 class="mb-1">مرحباً، @Model.Username!</h2>
                <p class="mb-1"><i class="fas fa-user-tag me-2"></i>الدور: @Model.Role</p>
                <p class="mb-0"><i class="fas fa-envelope me-2"></i>البريد الإلكتروني: @Model.Email</p>
            </div>
        </div>
    </div>

    @if (!Model.HasError)
    {
        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stat-card primary">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="stat-number">@Model.Statistics.OrderStatistics.TotalOrders</div>
                            <div>إجمالي الطلبات</div>
                        </div>
                        <i class="fas fa-file-alt stat-icon"></i>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stat-card warning">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="stat-number">@Model.Statistics.OrderStatistics.PendingOrders</div>
                            <div>الطلبات المعلقة</div>
                        </div>
                        <i class="fas fa-clock stat-icon"></i>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stat-card success">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="stat-number">@Model.Statistics.OrderStatistics.CompletedOrders</div>
                            <div>الطلبات المكتملة</div>
                        </div>
                        <i class="fas fa-check-circle stat-icon"></i>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stat-card danger">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="stat-number">@Model.Statistics.OrderStatistics.CancelledOrders</div>
                            <div>الطلبات المُلغاة</div>
                        </div>
                        <i class="fas fa-times-circle stat-icon"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Time-based Statistics -->
        <div class="row mb-4">
            <div class="col-lg-4 col-md-6 mb-3">
                <div class="card dashboard-card">
                    <div class="card-body text-center">
                        <i class="fas fa-calendar-day text-primary mb-2" style="font-size: 2rem;"></i>
                        <h4 class="text-primary">@Model.Statistics.OrderStatistics.TodayOrders</h4>
                        <p class="text-muted mb-0">طلبات اليوم</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 col-md-6 mb-3">
                <div class="card dashboard-card">
                    <div class="card-body text-center">
                        <i class="fas fa-calendar-week text-success mb-2" style="font-size: 2rem;"></i>
                        <h4 class="text-success">@Model.Statistics.OrderStatistics.ThisWeekOrders</h4>
                        <p class="text-muted mb-0">طلبات هذا الأسبوع</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 col-md-6 mb-3">
                <div class="card dashboard-card">
                    <div class="card-body text-center">
                        <i class="fas fa-calendar-alt text-info mb-2" style="font-size: 2rem;"></i>
                        <h4 class="text-info">@Model.Statistics.OrderStatistics.ThisMonthOrders</h4>
                        <p class="text-muted mb-0">طلبات هذا الشهر</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts and Analytics -->
        <div class="row mb-4">
            <div class="col-lg-6 mb-4">
                <div class="card dashboard-card">
                    <div class="card-header bg-transparent">
                        <h5 class="mb-0"><i class="fas fa-chart-pie me-2"></i>توزيع الطلبات حسب الحالة</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="orderStatusChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 mb-4">
                <div class="card dashboard-card">
                    <div class="card-header bg-transparent">
                        <h5 class="mb-0"><i class="fas fa-chart-bar me-2"></i>أنواع الطلبات</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="orderTypeChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Workflow Statistics -->
        <div class="row mb-4">
            <div class="col-lg-12 mb-4">
                <div class="card dashboard-card">
                    <div class="card-header bg-transparent">
                        <h5 class="mb-0"><i class="fas fa-sitemap me-2"></i>مراحل سير العمل</h5>
                    </div>
                    <div class="card-body">
                        @foreach (var stage in Model.Statistics.WorkflowStatistics.OrdersByWorkflowStage)
                        {
                            <div class="workflow-stage" style="--stage-color: @stage.StageColor;">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span class="fw-bold">@stage.StageName</span>
                                    <span class="badge bg-primary">@stage.Count طلب</span>
                                </div>
                                <div class="progress-modern">
                                    <div class="progress-bar" 
                                         style="width: @stage.Percentage.ToString("F1")%; --progress-color: @stage.StageColor; --progress-color-light: @stage.StageColor;"></div>
                                </div>
                                <small class="text-muted">@stage.Percentage.ToString("F1")%</small>
                            </div>
                        }
                    </div>
                </div>
            </div>
            
        </div>

        <!-- Department Distribution -->
        <div class="row mb-4">
            <div class="col-lg-8 mb-4">
                <div class="card dashboard-card">
                    <div class="card-header bg-transparent">
                        <h5 class="mb-0"><i class="fas fa-chart-donut me-2"></i>توزيع الأقسام حسب المدراء</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            @foreach (var manager in Model.Statistics.DepartmentStatistics.DepartmentsByManager.Take(5))
                            {
                                <div class="col-lg-6 mb-3">
                                    <div class="mb-3">
                                        <div class="d-flex justify-content-between align-items-center mb-1">
                                            <span class="fw-bold">@manager.ManagerName</span>
                                            <span class="text-muted">@manager.DepartmentCount أقسام</span>
                                        </div>
                                        <div class="progress-modern">
                                            <div class="progress-bar bg-info" style="width: @manager.Percentage.ToString("F1")%; --progress-color: #17a2b8; --progress-color-light: #5bc0de;"></div>
                                        </div>
                                        <small class="text-muted">@manager.Percentage.ToString("F1")%</small>
                                    </div>
                                </div>
                            }
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 mb-4">
                <div class="card dashboard-card">
                    <div class="card-header bg-transparent">
                        <h5 class="mb-0"><i class="fas fa-building me-2"></i>إحصائيات الأقسام</h5>
                    </div>
                    <div class="card-body">
                        <div class="text-center mb-3">
                            <h3 class="text-primary">@Model.Statistics.DepartmentStatistics.TotalDepartments</h3>
                            <p class="text-muted">إجمالي الأقسام</p>
                        </div>
                        <div class="row text-center">
                            <div class="col-6">
                                <h5 class="text-success">@Model.Statistics.DepartmentStatistics.AssignedDepartments</h5>
                                <small class="text-muted">مُوزعة</small>
                            </div>
                            <div class="col-6">
                                <h5 class="text-warning">@Model.Statistics.DepartmentStatistics.UnassignedDepartments</h5>
                                <small class="text-muted">غير مُوزعة</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Employee Statistics -->
        <div class="row mb-4">
            <div class="col-lg-4 mb-4">
                <div class="card dashboard-card">
                    <div class="card-header bg-transparent">
                        <h5 class="mb-0"><i class="fas fa-users me-2"></i>إحصائيات الموظفين</h5>
                    </div>
                    <div class="card-body text-center">
                        <h2 class="text-primary mb-3">@Model.Statistics.EmployeeStatistics.TotalEmployees</h2>
                        <p class="text-muted">إجمالي الموظفين</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 mb-4">
                <div class="card dashboard-card">
                    <div class="card-header bg-transparent">
                        <h6 class="mb-0">الجنسيات الأكثر</h6>
                    </div>
                    <div class="card-body">
                        @foreach (var nationality in Model.Statistics.EmployeeStatistics.EmployeesByNationality.Take(5))
                        {
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span>@nationality.Nationality</span>
                                <span class="badge bg-secondary">@nationality.Count</span>
                            </div>
                        }
                    </div>
                </div>
            </div>
            <div class="col-lg-4 mb-4">
                <div class="card dashboard-card">
                    <div class="card-header bg-transparent">
                        <h6 class="mb-0">أنواع التوظيف</h6>
                    </div>
                    <div class="card-body">
                        @foreach (var empType in Model.Statistics.EmployeeStatistics.EmployeesByType.Take(5))
                        {
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span>@empType.EmploymentType</span>
                                <span class="badge bg-info">@empType.Count</span>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>

        <!-- Top Departments by Orders -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card dashboard-card">
                    <div class="card-header bg-transparent">
                        <h5 class="mb-0"><i class="fas fa-trophy me-2"></i>الأقسام الأكثر طلباً</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            @foreach (var dept in Model.Statistics.OrderStatistics.OrdersByDepartment.Take(6))
                            {
                                <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                                    <div class="department-item">
                                        <h4 class="text-primary mb-1">@dept.Count</h4>
                                        <p class="mb-0 small">@dept.DepartmentName</p>
                                        <small class="text-muted">@dept.Percentage.ToString("F1")%</small>
                                    </div>
                                </div>
                            }
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }
</div>



@section Scripts {
    <!-- Chart Data for JavaScript -->
    <script>
        window.dashboardData = {
            orderStatusLabels: [@Html.Raw(string.Join(",", Model.Statistics.OrderStatistics.OrdersByStatus.Select(s => $"'{s.StatusDisplayName}'")))],
            orderStatusData: [@string.Join(",", Model.Statistics.OrderStatistics.OrdersByStatus.Select(s => s.Count))],
            orderTypeLabels: [@Html.Raw(string.Join(",", Model.Statistics.OrderStatistics.OrdersByType.Take(10).Select(t => $"'{t.OrderType}'")))],
            orderTypeData: [@string.Join(",", Model.Statistics.OrderStatistics.OrdersByType.Take(10).Select(t => t.Count))]
        };
    </script>

    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="~/js/dashboard-charts.js"></script>
}
