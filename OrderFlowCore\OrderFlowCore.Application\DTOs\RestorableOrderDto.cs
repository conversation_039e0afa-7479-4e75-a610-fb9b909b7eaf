using OrderFlowCore.Core.Entities;

namespace OrderFlowCore.Application.DTOs
{
    public class RestorableOrderDto
    {
        public int Id { get; set; }
        public string OrderNumber { get; set; }
        public string EmployeeName { get; set; }
        public string Department { get; set; }
        public string OrderDate { get; set; }
        public OrderStatus OrderStatus { get; set; }
        public string DisplayText { get; set; }
    }
}