<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.Extensions.Configuration</name>
    </assembly>
    <members>
        <member name="T:Microsoft.Extensions.Configuration.ChainedBuilderExtensions">
            <summary>
            Extension methods for adding <see cref="T:Microsoft.Extensions.Configuration.IConfiguration"/> to an <see cref="T:Microsoft.Extensions.Configuration.IConfigurationBuilder"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.ChainedBuilderExtensions.AddConfiguration(Microsoft.Extensions.Configuration.IConfigurationBuilder,Microsoft.Extensions.Configuration.IConfiguration)">
            <summary>
            Adds an existing configuration to <paramref name="configurationBuilder"/>.
            </summary>
            <param name="configurationBuilder">The <see cref="T:Microsoft.Extensions.Configuration.IConfigurationBuilder"/> to add to.</param>
            <param name="config">The <see cref="T:Microsoft.Extensions.Configuration.IConfiguration"/> to add.</param>
            <returns>The <see cref="T:Microsoft.Extensions.Configuration.IConfigurationBuilder"/>.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.ChainedBuilderExtensions.AddConfiguration(Microsoft.Extensions.Configuration.IConfigurationBuilder,Microsoft.Extensions.Configuration.IConfiguration,System.Boolean)">
            <summary>
            Adds an existing configuration to <paramref name="configurationBuilder"/>.
            </summary>
            <param name="configurationBuilder">The <see cref="T:Microsoft.Extensions.Configuration.IConfigurationBuilder"/> to add to.</param>
            <param name="config">The <see cref="T:Microsoft.Extensions.Configuration.IConfiguration"/> to add.</param>
            <param name="shouldDisposeConfiguration">Whether the configuration should get disposed when the configuration provider is disposed.</param>
            <returns>The <see cref="T:Microsoft.Extensions.Configuration.IConfigurationBuilder"/>.</returns>
        </member>
        <member name="T:Microsoft.Extensions.Configuration.ChainedConfigurationProvider">
            <summary>
            Chained implementation of <see cref="T:Microsoft.Extensions.Configuration.IConfigurationProvider"/>
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.ChainedConfigurationProvider.#ctor(Microsoft.Extensions.Configuration.ChainedConfigurationSource)">
            <summary>
            Initialize a new instance from the source configuration.
            </summary>
            <param name="source">The source configuration.</param>
        </member>
        <member name="P:Microsoft.Extensions.Configuration.ChainedConfigurationProvider.Configuration">
            <summary>
            Gets the chained configuration.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.ChainedConfigurationProvider.TryGet(System.String,System.String@)">
            <summary>
            Tries to get a configuration value for the specified key.
            </summary>
            <param name="key">The key.</param>
            <param name="value">The value.</param>
            <returns><c>True</c> if a value for the specified key was found, otherwise <c>false</c>.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.ChainedConfigurationProvider.Set(System.String,System.String)">
            <summary>
            Sets a configuration value for the specified key.
            </summary>
            <param name="key">The key.</param>
            <param name="value">The value.</param>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.ChainedConfigurationProvider.GetReloadToken">
            <summary>
            Returns a change token if this provider supports change tracking, null otherwise.
            </summary>
            <returns>The change token.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.ChainedConfigurationProvider.Load">
            <summary>
            Loads configuration values from the source represented by this <see cref="T:Microsoft.Extensions.Configuration.IConfigurationProvider"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.ChainedConfigurationProvider.GetChildKeys(System.Collections.Generic.IEnumerable{System.String},System.String)">
            <summary>
            Returns the immediate descendant configuration keys for a given parent path based on this
            <see cref="T:Microsoft.Extensions.Configuration.IConfigurationProvider"/>s data and the set of keys returned by all the preceding
            <see cref="T:Microsoft.Extensions.Configuration.IConfigurationProvider"/>s.
            </summary>
            <param name="earlierKeys">The child keys returned by the preceding providers for the same parent path.</param>
            <param name="parentPath">The parent path.</param>
            <returns>The child keys.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.ChainedConfigurationProvider.Dispose">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.Extensions.Configuration.ChainedConfigurationSource">
            <summary>
            Represents a chained <see cref="T:Microsoft.Extensions.Configuration.IConfiguration"/> as an <see cref="T:Microsoft.Extensions.Configuration.IConfigurationSource"/>.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Configuration.ChainedConfigurationSource.Configuration">
            <summary>
            The chained configuration.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Configuration.ChainedConfigurationSource.ShouldDisposeConfiguration">
            <summary>
            Whether the chained configuration should be disposed when the
            configuration provider gets disposed.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.ChainedConfigurationSource.Build(Microsoft.Extensions.Configuration.IConfigurationBuilder)">
            <summary>
            Builds the <see cref="T:Microsoft.Extensions.Configuration.ChainedConfigurationProvider"/> for this source.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.Extensions.Configuration.IConfigurationBuilder"/>.</param>
            <returns>A <see cref="T:Microsoft.Extensions.Configuration.ChainedConfigurationProvider"/></returns>
        </member>
        <member name="T:Microsoft.Extensions.Configuration.ConfigurationBuilder">
            <summary>
            Used to build key/value based configuration settings for use in an application.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Configuration.ConfigurationBuilder.Sources">
            <summary>
            Returns the sources used to obtain configuration values.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Configuration.ConfigurationBuilder.Properties">
            <summary>
            Gets a key/value collection that can be used to share data between the <see cref="T:Microsoft.Extensions.Configuration.IConfigurationBuilder"/>
            and the registered <see cref="T:Microsoft.Extensions.Configuration.IConfigurationProvider"/>s.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.ConfigurationBuilder.Add(Microsoft.Extensions.Configuration.IConfigurationSource)">
            <summary>
            Adds a new configuration source.
            </summary>
            <param name="source">The configuration source to add.</param>
            <returns>The same <see cref="T:Microsoft.Extensions.Configuration.IConfigurationBuilder"/>.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.ConfigurationBuilder.Build">
            <summary>
            Builds an <see cref="T:Microsoft.Extensions.Configuration.IConfiguration"/> with keys and values from the set of providers registered in
            <see cref="P:Microsoft.Extensions.Configuration.ConfigurationBuilder.Sources"/>.
            </summary>
            <returns>An <see cref="T:Microsoft.Extensions.Configuration.IConfigurationRoot"/> with keys and values from the registered providers.</returns>
        </member>
        <member name="T:Microsoft.Extensions.Configuration.ConfigurationKeyComparer">
            <summary>
            IComparer implementation used to order configuration keys.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Configuration.ConfigurationKeyComparer.Instance">
            <summary>
            The default instance.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Configuration.ConfigurationKeyComparer.Comparison">
            <summary>A comparer delegate with the default instance.</summary>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.ConfigurationKeyComparer.Compare(System.String,System.String)">
            <summary>
            Compares two strings.
            </summary>
            <param name="x">First string.</param>
            <param name="y">Second string.</param>
            <returns>Less than 0 if x is less than y, 0 if x is equal to y and greater than 0 if x is greater than y.</returns>
        </member>
        <member name="T:Microsoft.Extensions.Configuration.ConfigurationManager">
            <summary>
            Represents a mutable configuration object.
            </summary>
            <remarks>
            It is both an <see cref="T:Microsoft.Extensions.Configuration.IConfigurationBuilder"/> and an <see cref="T:Microsoft.Extensions.Configuration.IConfigurationRoot"/>.
            As sources are added, it updates its current view of configuration.
            </remarks>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.ConfigurationManager.#ctor">
            <summary>
            Creates an empty mutable configuration object that is both an <see cref="T:Microsoft.Extensions.Configuration.IConfigurationBuilder"/> and an <see cref="T:Microsoft.Extensions.Configuration.IConfigurationRoot"/>.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Configuration.ConfigurationManager.Item(System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.ConfigurationManager.GetSection(System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.ConfigurationManager.GetChildren">
            <inheritdoc/>
        </member>
        <member name="P:Microsoft.Extensions.Configuration.ConfigurationManager.Sources">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.Extensions.Configuration.ConfigurationManager.Dispose">
            <inheritdoc/>
        </member>
        <member name="T:Microsoft.Extensions.Configuration.ConfigurationProvider">
            <summary>
            Base helper class for implementing an <see cref="T:Microsoft.Extensions.Configuration.IConfigurationProvider"/>
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.ConfigurationProvider.#ctor">
            <summary>
            Initializes a new <see cref="T:Microsoft.Extensions.Configuration.IConfigurationProvider"/>
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Configuration.ConfigurationProvider.Data">
            <summary>
            The configuration key value pairs for this provider.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.ConfigurationProvider.TryGet(System.String,System.String@)">
            <summary>
            Attempts to find a value with the given key, returns true if one is found, false otherwise.
            </summary>
            <param name="key">The key to lookup.</param>
            <param name="value">The value found at key if one is found.</param>
            <returns>True if key has a value, false otherwise.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.ConfigurationProvider.Set(System.String,System.String)">
            <summary>
            Sets a value for a given key.
            </summary>
            <param name="key">The configuration key to set.</param>
            <param name="value">The value to set.</param>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.ConfigurationProvider.Load">
            <summary>
            Loads (or reloads) the data for this provider.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.ConfigurationProvider.GetChildKeys(System.Collections.Generic.IEnumerable{System.String},System.String)">
            <summary>
            Returns the list of keys that this provider has.
            </summary>
            <param name="earlierKeys">The earlier keys that other providers contain.</param>
            <param name="parentPath">The path for the parent IConfiguration.</param>
            <returns>The list of keys for this provider.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.ConfigurationProvider.GetReloadToken">
            <summary>
            Returns a <see cref="T:Microsoft.Extensions.Primitives.IChangeToken"/> that can be used to listen when this provider is reloaded.
            </summary>
            <returns>The <see cref="T:Microsoft.Extensions.Primitives.IChangeToken"/>.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.ConfigurationProvider.OnReload">
            <summary>
            Triggers the reload change token and creates a new one.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.ConfigurationProvider.ToString">
            <summary>
            Generates a string representing this provider name and relevant details.
            </summary>
            <returns> The configuration name. </returns>
        </member>
        <member name="T:Microsoft.Extensions.Configuration.ConfigurationReloadToken">
            <summary>
            Implements <see cref="T:Microsoft.Extensions.Primitives.IChangeToken"/>
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Configuration.ConfigurationReloadToken.ActiveChangeCallbacks">
            <summary>
            Indicates if this token will proactively raise callbacks. Callbacks are still guaranteed to be invoked, eventually.
            </summary>
            <returns>True if the token will proactively raise callbacks.</returns>
        </member>
        <member name="P:Microsoft.Extensions.Configuration.ConfigurationReloadToken.HasChanged">
            <summary>
            Gets a value that indicates if a change has occurred.
            </summary>
            <returns>True if a change has occurred.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.ConfigurationReloadToken.RegisterChangeCallback(System.Action{System.Object},System.Object)">
            <summary>
            Registers for a callback that will be invoked when the entry has changed. <see cref="P:Microsoft.Extensions.Primitives.IChangeToken.HasChanged"/>
            MUST be set before the callback is invoked.
            </summary>
            <param name="callback">The callback to invoke.</param>
            <param name="state">State to be passed into the callback.</param>
            <returns>The <see cref="T:System.Threading.CancellationToken"/> registration.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.ConfigurationReloadToken.OnReload">
            <summary>
            Used to trigger the change token when a reload occurs.
            </summary>
        </member>
        <member name="T:Microsoft.Extensions.Configuration.ConfigurationRoot">
            <summary>
            The root node for a configuration.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.ConfigurationRoot.#ctor(System.Collections.Generic.IList{Microsoft.Extensions.Configuration.IConfigurationProvider})">
            <summary>
            Initializes a Configuration root with a list of providers.
            </summary>
            <param name="providers">The <see cref="T:Microsoft.Extensions.Configuration.IConfigurationProvider"/>s for this configuration.</param>
        </member>
        <member name="P:Microsoft.Extensions.Configuration.ConfigurationRoot.Providers">
            <summary>
            The <see cref="T:Microsoft.Extensions.Configuration.IConfigurationProvider"/>s for this configuration.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Configuration.ConfigurationRoot.Item(System.String)">
            <summary>
            Gets or sets the value corresponding to a configuration key.
            </summary>
            <param name="key">The configuration key.</param>
            <returns>The configuration value.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.ConfigurationRoot.GetChildren">
            <summary>
            Gets the immediate children sub-sections.
            </summary>
            <returns>The children.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.ConfigurationRoot.GetReloadToken">
            <summary>
            Returns a <see cref="T:Microsoft.Extensions.Primitives.IChangeToken"/> that can be used to observe when this configuration is reloaded.
            </summary>
            <returns>The <see cref="T:Microsoft.Extensions.Primitives.IChangeToken"/>.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.ConfigurationRoot.GetSection(System.String)">
            <summary>
            Gets a configuration sub-section with the specified key.
            </summary>
            <param name="key">The key of the configuration section.</param>
            <returns>The <see cref="T:Microsoft.Extensions.Configuration.IConfigurationSection"/>.</returns>
            <remarks>
                This method will never return <c>null</c>. If no matching sub-section is found with the specified key,
                an empty <see cref="T:Microsoft.Extensions.Configuration.IConfigurationSection"/> will be returned.
            </remarks>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.ConfigurationRoot.Reload">
            <summary>
            Force the configuration values to be reloaded from the underlying sources.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.ConfigurationRoot.Dispose">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.Extensions.Configuration.ConfigurationSection">
            <summary>
            Represents a section of application configuration values.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.ConfigurationSection.#ctor(Microsoft.Extensions.Configuration.IConfigurationRoot,System.String)">
            <summary>
            Initializes a new instance.
            </summary>
            <param name="root">The configuration root.</param>
            <param name="path">The path to this section.</param>
        </member>
        <member name="P:Microsoft.Extensions.Configuration.ConfigurationSection.Path">
            <summary>
            Gets the full path to this section from the <see cref="T:Microsoft.Extensions.Configuration.IConfigurationRoot"/>.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Configuration.ConfigurationSection.Key">
            <summary>
            Gets the key this section occupies in its parent.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Configuration.ConfigurationSection.Value">
            <summary>
            Gets or sets the section value.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Configuration.ConfigurationSection.Item(System.String)">
            <summary>
            Gets or sets the value corresponding to a configuration key.
            </summary>
            <param name="key">The configuration key.</param>
            <returns>The configuration value.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.ConfigurationSection.GetSection(System.String)">
            <summary>
            Gets a configuration sub-section with the specified key.
            </summary>
            <param name="key">The key of the configuration section.</param>
            <returns>The <see cref="T:Microsoft.Extensions.Configuration.IConfigurationSection"/>.</returns>
            <remarks>
                This method will never return <c>null</c>. If no matching sub-section is found with the specified key,
                an empty <see cref="T:Microsoft.Extensions.Configuration.IConfigurationSection"/> will be returned.
            </remarks>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.ConfigurationSection.GetChildren">
            <summary>
            Gets the immediate descendant configuration sub-sections.
            </summary>
            <returns>The configuration sub-sections.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.ConfigurationSection.GetReloadToken">
            <summary>
            Returns a <see cref="T:Microsoft.Extensions.Primitives.IChangeToken"/> that can be used to observe when this configuration is reloaded.
            </summary>
            <returns>The <see cref="T:Microsoft.Extensions.Primitives.IChangeToken"/>.</returns>
        </member>
        <member name="T:Microsoft.Extensions.Configuration.InternalConfigurationRootExtensions">
            <summary>
            Extensions method for <see cref="T:Microsoft.Extensions.Configuration.IConfigurationRoot"/>
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.InternalConfigurationRootExtensions.GetChildrenImplementation(Microsoft.Extensions.Configuration.IConfigurationRoot,System.String)">
            <summary>
            Gets the immediate children sub-sections of configuration root based on key.
            </summary>
            <param name="root">Configuration from which to retrieve sub-sections.</param>
            <param name="path">Key of a section of which children to retrieve.</param>
            <returns>Immediate children sub-sections of section specified by key.</returns>
        </member>
        <member name="T:Microsoft.Extensions.Configuration.MemoryConfigurationBuilderExtensions">
            <summary>
            IConfigurationBuilder extension methods for the MemoryConfigurationProvider.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.MemoryConfigurationBuilderExtensions.AddInMemoryCollection(Microsoft.Extensions.Configuration.IConfigurationBuilder)">
            <summary>
            Adds the memory configuration provider to <paramref name="configurationBuilder"/>.
            </summary>
            <param name="configurationBuilder">The <see cref="T:Microsoft.Extensions.Configuration.IConfigurationBuilder"/> to add to.</param>
            <returns>The <see cref="T:Microsoft.Extensions.Configuration.IConfigurationBuilder"/>.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.MemoryConfigurationBuilderExtensions.AddInMemoryCollection(Microsoft.Extensions.Configuration.IConfigurationBuilder,System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{System.String,System.String}})">
            <summary>
            Adds the memory configuration provider to <paramref name="configurationBuilder"/>.
            </summary>
            <param name="configurationBuilder">The <see cref="T:Microsoft.Extensions.Configuration.IConfigurationBuilder"/> to add to.</param>
            <param name="initialData">The data to add to memory configuration provider.</param>
            <returns>The <see cref="T:Microsoft.Extensions.Configuration.IConfigurationBuilder"/>.</returns>
        </member>
        <member name="T:Microsoft.Extensions.Configuration.Memory.MemoryConfigurationProvider">
            <summary>
            In-memory implementation of <see cref="T:Microsoft.Extensions.Configuration.IConfigurationProvider"/>
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.Memory.MemoryConfigurationProvider.#ctor(Microsoft.Extensions.Configuration.Memory.MemoryConfigurationSource)">
            <summary>
            Initialize a new instance from the source.
            </summary>
            <param name="source">The source settings.</param>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.Memory.MemoryConfigurationProvider.Add(System.String,System.String)">
            <summary>
            Add a new key and value pair.
            </summary>
            <param name="key">The configuration key.</param>
            <param name="value">The configuration value.</param>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.Memory.MemoryConfigurationProvider.GetEnumerator">
            <summary>
            Returns an enumerator that iterates through the collection.
            </summary>
            <returns>An enumerator that can be used to iterate through the collection.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.Memory.MemoryConfigurationProvider.System#Collections#IEnumerable#GetEnumerator">
            <summary>
            Returns an enumerator that iterates through the collection.
            </summary>
            <returns>An enumerator that can be used to iterate through the collection.</returns>
        </member>
        <member name="T:Microsoft.Extensions.Configuration.Memory.MemoryConfigurationSource">
            <summary>
            Represents in-memory data as an <see cref="T:Microsoft.Extensions.Configuration.IConfigurationSource"/>.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Configuration.Memory.MemoryConfigurationSource.InitialData">
            <summary>
            The initial key value configuration pairs.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.Memory.MemoryConfigurationSource.Build(Microsoft.Extensions.Configuration.IConfigurationBuilder)">
            <summary>
            Builds the <see cref="T:Microsoft.Extensions.Configuration.Memory.MemoryConfigurationProvider"/> for this source.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.Extensions.Configuration.IConfigurationBuilder"/>.</param>
            <returns>A <see cref="T:Microsoft.Extensions.Configuration.Memory.MemoryConfigurationProvider"/></returns>
        </member>
        <member name="T:Microsoft.Extensions.Configuration.StreamConfigurationProvider">
            <summary>
            Stream based configuration provider
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Configuration.StreamConfigurationProvider.Source">
            <summary>
            The source settings for this provider.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.StreamConfigurationProvider.#ctor(Microsoft.Extensions.Configuration.StreamConfigurationSource)">
            <summary>
            Constructor.
            </summary>
            <param name="source">The source.</param>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.StreamConfigurationProvider.Load(System.IO.Stream)">
            <summary>
            Load the configuration data from the stream.
            </summary>
            <param name="stream">The data stream.</param>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.StreamConfigurationProvider.Load">
            <summary>
            Load the configuration data from the stream. Will throw after the first call.
            </summary>
        </member>
        <member name="T:Microsoft.Extensions.Configuration.StreamConfigurationSource">
            <summary>
            Stream based <see cref="T:Microsoft.Extensions.Configuration.IConfigurationSource" />.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Configuration.StreamConfigurationSource.Stream">
            <summary>
            The stream containing the configuration data.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.StreamConfigurationSource.Build(Microsoft.Extensions.Configuration.IConfigurationBuilder)">
            <summary>
            Builds the <see cref="T:Microsoft.Extensions.Configuration.StreamConfigurationProvider"/> for this source.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.Extensions.Configuration.IConfigurationBuilder"/>.</param>
            <returns>An <see cref="T:Microsoft.Extensions.Configuration.IConfigurationProvider"/></returns>
        </member>
        <member name="M:Microsoft.Extensions.Internal.ChangeCallbackRegistrar.UnsafeRegisterChangeCallback``1(System.Action{System.Object},System.Object,System.Threading.CancellationToken,System.Action{``0},``0)">
            <summary>
            Registers for a callback that will be invoked when the entry has changed. <see cref="P:Microsoft.Extensions.Primitives.IChangeToken.HasChanged"/>
            MUST be set before the callback is invoked.
            </summary>
            <param name="callback">The callback to invoke.</param>
            <param name="state">State to be passed into the callback.</param>
            <param name="token">The <see cref="T:System.Threading.CancellationToken"/> to invoke the callback with.</param>
            <param name="onFailure">The action to execute when an <see cref="T:System.ObjectDisposedException"/> is thrown. Should be used to set the IChangeToken's ActiveChangeCallbacks property to false.</param>
            <param name="onFailureState">The state to be passed into the <paramref name="onFailure"/> action.</param>
            <returns>The <see cref="T:System.Threading.CancellationToken"/> registration.</returns>
        </member>
        <member name="M:System.ThrowHelper.ThrowIfNull(System.Object,System.String)">
            <summary>Throws an <see cref="T:System.ArgumentNullException"/> if <paramref name="argument"/> is null.</summary>
            <param name="argument">The reference type argument to validate as non-null.</param>
            <param name="paramName">The name of the parameter with which <paramref name="argument"/> corresponds.</param>
        </member>
        <member name="M:System.ThrowHelper.IfNullOrWhitespace(System.String,System.String)">
            <summary>
            Throws either an <see cref="T:System.ArgumentNullException"/> or an <see cref="T:System.ArgumentException"/>
            if the specified string is <see langword="null"/> or whitespace respectively.
            </summary>
            <param name="argument">String to be checked for <see langword="null"/> or whitespace.</param>
            <param name="paramName">The name of the parameter being checked.</param>
            <returns>The original value of <paramref name="argument"/>.</returns>
        </member>
        <member name="T:System.Runtime.InteropServices.LibraryImportAttribute">
            <summary>
            Attribute used to indicate a source generator should create a function for marshalling
            arguments instead of relying on the runtime to generate an equivalent marshalling function at run-time.
            </summary>
            <remarks>
            This attribute is meaningless if the source generator associated with it is not enabled.
            The current built-in source generator only supports C# and only supplies an implementation when
            applied to static, partial, non-generic methods.
            </remarks>
        </member>
        <member name="M:System.Runtime.InteropServices.LibraryImportAttribute.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Runtime.InteropServices.LibraryImportAttribute"/>.
            </summary>
            <param name="libraryName">Name of the library containing the import.</param>
        </member>
        <member name="P:System.Runtime.InteropServices.LibraryImportAttribute.LibraryName">
            <summary>
            Gets the name of the library containing the import.
            </summary>
        </member>
        <member name="P:System.Runtime.InteropServices.LibraryImportAttribute.EntryPoint">
            <summary>
            Gets or sets the name of the entry point to be called.
            </summary>
        </member>
        <member name="P:System.Runtime.InteropServices.LibraryImportAttribute.StringMarshalling">
            <summary>
            Gets or sets how to marshal string arguments to the method.
            </summary>
            <remarks>
            If this field is set to a value other than <see cref="F:System.Runtime.InteropServices.StringMarshalling.Custom" />,
            <see cref="P:System.Runtime.InteropServices.LibraryImportAttribute.StringMarshallingCustomType" /> must not be specified.
            </remarks>
        </member>
        <member name="P:System.Runtime.InteropServices.LibraryImportAttribute.StringMarshallingCustomType">
            <summary>
            Gets or sets the <see cref="T:System.Type"/> used to control how string arguments to the method are marshalled.
            </summary>
            <remarks>
            If this field is specified, <see cref="P:System.Runtime.InteropServices.LibraryImportAttribute.StringMarshalling" /> must not be specified
            or must be set to <see cref="F:System.Runtime.InteropServices.StringMarshalling.Custom" />.
            </remarks>
        </member>
        <member name="P:System.Runtime.InteropServices.LibraryImportAttribute.SetLastError">
            <summary>
            Gets or sets whether the callee sets an error (SetLastError on Windows or errno
            on other platforms) before returning from the attributed method.
            </summary>
        </member>
        <member name="T:System.Runtime.InteropServices.StringMarshalling">
            <summary>
            Specifies how strings should be marshalled for generated p/invokes
            </summary>
        </member>
        <member name="F:System.Runtime.InteropServices.StringMarshalling.Custom">
            <summary>
            Indicates the user is suppling a specific marshaller in <see cref="P:System.Runtime.InteropServices.LibraryImportAttribute.StringMarshallingCustomType"/>.
            </summary>
        </member>
        <member name="F:System.Runtime.InteropServices.StringMarshalling.Utf8">
            <summary>
            Use the platform-provided UTF-8 marshaller.
            </summary>
        </member>
        <member name="F:System.Runtime.InteropServices.StringMarshalling.Utf16">
            <summary>
            Use the platform-provided UTF-16 marshaller.
            </summary>
        </member>
        <member name="P:System.SR.Error_NoSources">
            <summary>A configuration source is not registered. Please register one before setting a value.</summary>
        </member>
        <member name="P:System.SR.InvalidNullArgument">
            <summary>Null is not a valid value for '{0}'.</summary>
        </member>
        <member name="P:System.SR.StreamConfigurationProvidersAlreadyLoaded">
            <summary>StreamConfigurationProviders cannot be loaded more than once.</summary>
        </member>
        <member name="P:System.SR.StreamConfigurationSourceStreamCannotBeNull">
            <summary>Source.Stream cannot be null.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.AllowNullAttribute">
            <summary>Specifies that null is allowed as an input even if the corresponding type disallows it.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.DisallowNullAttribute">
            <summary>Specifies that null is disallowed as an input even if the corresponding type allows it.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.MaybeNullAttribute">
            <summary>Specifies that an output may be null even if the corresponding type disallows it.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.NotNullAttribute">
            <summary>Specifies that an output will not be null even if the corresponding type allows it. Specifies that an input argument was not null when the call returns.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.MaybeNullWhenAttribute">
            <summary>Specifies that when a method returns <see cref="P:System.Diagnostics.CodeAnalysis.MaybeNullWhenAttribute.ReturnValue"/>, the parameter may be null even if the corresponding type disallows it.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MaybeNullWhenAttribute.#ctor(System.Boolean)">
            <summary>Initializes the attribute with the specified return value condition.</summary>
            <param name="returnValue">
            The return value condition. If the method returns this value, the associated parameter may be null.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.MaybeNullWhenAttribute.ReturnValue">
            <summary>Gets the return value condition.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.NotNullWhenAttribute">
            <summary>Specifies that when a method returns <see cref="P:System.Diagnostics.CodeAnalysis.NotNullWhenAttribute.ReturnValue"/>, the parameter will not be null even if the corresponding type allows it.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.NotNullWhenAttribute.#ctor(System.Boolean)">
            <summary>Initializes the attribute with the specified return value condition.</summary>
            <param name="returnValue">
            The return value condition. If the method returns this value, the associated parameter will not be null.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.NotNullWhenAttribute.ReturnValue">
            <summary>Gets the return value condition.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.NotNullIfNotNullAttribute">
            <summary>Specifies that the output will be non-null if the named parameter is non-null.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.NotNullIfNotNullAttribute.#ctor(System.String)">
            <summary>Initializes the attribute with the associated parameter name.</summary>
            <param name="parameterName">
            The associated parameter name.  The output will be non-null if the argument to the parameter specified is non-null.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.NotNullIfNotNullAttribute.ParameterName">
            <summary>Gets the associated parameter name.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.DoesNotReturnAttribute">
            <summary>Applied to a method that will never return under any circumstance.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.DoesNotReturnIfAttribute">
            <summary>Specifies that the method will not return if the associated Boolean parameter is passed the specified value.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.DoesNotReturnIfAttribute.#ctor(System.Boolean)">
            <summary>Initializes the attribute with the specified parameter value.</summary>
            <param name="parameterValue">
            The condition parameter value. Code after the method will be considered unreachable by diagnostics if the argument to
            the associated parameter matches this value.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.DoesNotReturnIfAttribute.ParameterValue">
            <summary>Gets the condition parameter value.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.MemberNotNullAttribute">
            <summary>Specifies that the method or property will ensure that the listed field and property members have not-null values.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MemberNotNullAttribute.#ctor(System.String)">
            <summary>Initializes the attribute with a field or property member.</summary>
            <param name="member">
            The field or property member that is promised to be not-null.
            </param>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MemberNotNullAttribute.#ctor(System.String[])">
            <summary>Initializes the attribute with the list of field and property members.</summary>
            <param name="members">
            The list of field and property members that are promised to be not-null.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.MemberNotNullAttribute.Members">
            <summary>Gets field or property member names.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.MemberNotNullWhenAttribute">
            <summary>Specifies that the method or property will ensure that the listed field and property members have not-null values when returning with the specified return value condition.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MemberNotNullWhenAttribute.#ctor(System.Boolean,System.String)">
            <summary>Initializes the attribute with the specified return value condition and a field or property member.</summary>
            <param name="returnValue">
            The return value condition. If the method returns this value, the associated parameter will not be null.
            </param>
            <param name="member">
            The field or property member that is promised to be not-null.
            </param>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MemberNotNullWhenAttribute.#ctor(System.Boolean,System.String[])">
            <summary>Initializes the attribute with the specified return value condition and list of field and property members.</summary>
            <param name="returnValue">
            The return value condition. If the method returns this value, the associated parameter will not be null.
            </param>
            <param name="members">
            The list of field and property members that are promised to be not-null.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.MemberNotNullWhenAttribute.ReturnValue">
            <summary>Gets the return value condition.</summary>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.MemberNotNullWhenAttribute.Members">
            <summary>Gets field or property member names.</summary>
        </member>
    </members>
</doc>
