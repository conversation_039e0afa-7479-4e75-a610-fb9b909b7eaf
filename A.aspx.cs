﻿using System;
using System.Configuration;
using System.Data.SqlClient;
using System.IO.Compression;
using System.IO;
using System.Web.UI.WebControls;
using abozyad.Helpers;
using System.Linq;
using System.Collections.Generic;
using WebGrease.Activities;

namespace abozyad
{

    // تقوم هذه الدالة بمعالجة تحميل الصفحة عند استدعائها:
    // 1. التحقق من صلاحيات المستخدم المخزنة في الجلسة:
    //    - إذا كانت الصلاحيات غير موجودة أو كانت للمستخدمين "منسق الموارد البشرية"، "مدير حسابات"، أو تبدأ بـ "مشرف"، يتم إعادة توجيه المستخدم إلى صفحة "AccessDenied.aspx".
    // 2. إذا كان يتم تحميل الصفحة لأول مرة (وليس بسبب PostBack)، يتم استدعاء الدالة PopulateOrderNumbers() لتحميل أرقام الطلبات في الصفحة.

    public partial class WebForm4 : System.Web.UI.Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            try
            {
                // التحقق مما إذا كان المستخدم قد سجل الدخول
                if (Session["Username"] == null)
                {
                    // إعادة التوجيه إلى صفحة رفض الوصول إذا لم يكن المستخدم مسجلاً الدخول
                    Response.Redirect("AccessDenied.aspx");
                    return;
                }

                // التحقق من الصلاحية باستخدام مساعد المدير
                //var assistantManager = GetCurrentAssistantManager();
                //if (assistantManager?.Id == null || !Session["UserPermission"]?.ToString().StartsWith("مساعد المدير") == true)
                //{
                //    // إعادة التوجيه إلى صفحة رفض الوصول إذا لم تكن الصلاحية مناسبة
                //    Response.Redirect("AccessDenied.aspx");
                //    return;
                //}

                // تخزين معرف مساعد المدير في الجلسة
                Session["AssistantManagerID"] = "A1";

                // تحميل البيانات عند أول تحميل للصفحة فقط
                if (!IsPostBack)
                {
                    PopulateOrderNumbers();
                }
            }
            catch (Exception ex)
            {
                // عرض رسالة خطأ ودية على الصفحة وتسجيل الخطأ
                LabelError.Text = $"حدث خطأ أثناء تحميل الصفحة: {ex.Message}";
                LabelError.Visible = true;
                LogError(ex);
            }
        }







        private string CleanFileName(string fileName)
        {
            // إزالة الأحرف غير المسموح بها في أسماء الملفات
            char[] invalidChars = System.IO.Path.GetInvalidFileNameChars();
            foreach (char c in invalidChars)
            {
                fileName = fileName.Replace(c, '_');
            }

            // التأكد من أن الاسم لا يتجاوز طولاً معيناً
            const int maxLength = 100;
            if (fileName.Length > maxLength)
            {
                string extension = System.IO.Path.GetExtension(fileName);
                fileName = fileName.Substring(0, maxLength - extension.Length) + extension;
            }

            return fileName;
        }

        protected void btnDownload_Click(object sender, EventArgs e)
        {
            try
            {
                string selectedOrderNumber = ddlOrderNumbers.SelectedValue;
                string connectionString = ConfigurationManager.ConnectionStrings["MyConnectionString"].ConnectionString;

                using (SqlConnection con = new SqlConnection(connectionString))
                {
                    string query = @"SELECT ملف1, ملف2, ملف3, ملف4, [اسم الموظف] 
                           FROM ordersTable 
                           WHERE [رقم الطلب] = @OrderNumber";

                    using (SqlCommand cmd = new SqlCommand(query, con))
                    {
                        cmd.Parameters.AddWithValue("@OrderNumber", selectedOrderNumber);
                        con.Open();

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                string employeeName = reader["اسم الموظف"].ToString().Trim();
                                bool filesFound = false;

                                using (MemoryStream zipStream = new MemoryStream())
                                {
                                    using (ZipArchive zipArchive = new ZipArchive(zipStream, ZipArchiveMode.Create, true))
                                    {
                                        for (int i = 0; i < 4; i++)
                                        {
                                            byte[] compressedData = reader[i] as byte[];
                                            if (compressedData != null && compressedData.Length > 0)
                                            {
                                                try
                                                {
                                                    // فك ضغط البيانات
                                                    byte[] pdfData = FileCompressor.ExtractFile(compressedData);

                                                    if (pdfData != null && IsPdfFile(pdfData))
                                                    {
                                                        filesFound = true;
                                                        string fileName = CleanFileName($"مرفق_{i + 1}_طلب_{selectedOrderNumber}_{employeeName}.pdf");

                                                        ZipArchiveEntry zipEntry = zipArchive.CreateEntry(fileName);
                                                        using (Stream entryStream = zipEntry.Open())
                                                        {
                                                            entryStream.Write(pdfData, 0, pdfData.Length);
                                                        }

                                                        System.Diagnostics.Debug.WriteLine($"✅ تم إضافة الملف: {fileName}");
                                                    }
                                                    else
                                                    {
                                                        System.Diagnostics.Debug.WriteLine($"❌ الملف {i + 1} ليس PDF صالح");
                                                    }
                                                }
                                                catch (Exception ex)
                                                {
                                                    System.Diagnostics.Debug.WriteLine($"❌ خطأ في معالجة الملف {i + 1}: {ex.Message}");
                                                    continue;  // استمر مع الملف التالي
                                                }
                                            }
                                        }
                                    }

                                    if (filesFound)
                                    {
                                        string zipFileName = CleanFileName($"مرفقات_طلب_{selectedOrderNumber}_{employeeName}.zip");

                                        Response.Clear();
                                        Response.ContentType = "application/zip";
                                        Response.AddHeader("Content-Disposition", $"attachment; filename={zipFileName}");
                                        Response.BinaryWrite(zipStream.ToArray());
                                        Response.Flush();
                                        Response.End();
                                    }
                                    else
                                    {
                                        LabelError.Text = "لا توجد ملفات صالحة لتحميلها.";
                                        LabelError.Visible = true;
                                    }
                                }
                            }
                            else
                            {
                                LabelError.Text = "لم يتم العثور على الطلب.";
                                LabelError.Visible = true;
                            }
                        }
                    }
                }
            }
            catch (System.Threading.ThreadAbortException)
            {
                // تجاهل
            }
            catch (Exception ex)
            {
                LabelError.Text = "حدث خطأ أثناء تحميل الملفات: " + ex.Message;
                LabelError.Visible = true;
                System.Diagnostics.Debug.WriteLine($"❌ خطأ عام: {ex.Message}");
            }
        }

        private bool IsPdfFile(byte[] data)
        {
            if (data == null || data.Length < 4)
                return false;

            // التحقق من PDF header
            return data[0] == 0x25 && // %
                   data[1] == 0x50 && // P
                   data[2] == 0x44 && // D
                   data[3] == 0x46;   // F
        }



        /// <summary>
        /// تحديد امتداد الملف بناءً على محتواه - تم تحسينها للتعامل مع PDF فقط
        /// </summary>
        private string DetermineFileExtension(byte[] fileData)
        {
            return ValidatePdfFormat(fileData) ? ".pdf" : null;
        }

        /// <summary>
        /// التحقق من أن الملف هو PDF صالح
        /// </summary>
        private bool ValidatePdfFormat(byte[] data)
        {
            try
            {
                if (data == null || data.Length < 4)
                {
                    System.Diagnostics.Debug.WriteLine("❌ البيانات فارغة أو قصيرة جداً");
                    return false;
                }

                // فحص توقيع PDF (PDF Signature)
                bool isPdf = data[0] == 0x25 && // %
                            data[1] == 0x50 && // P
                            data[2] == 0x44 && // D
                            data[3] == 0x46;   // F

                if (!isPdf)
                {
                    System.Diagnostics.Debug.WriteLine("❌ الملف ليس بصيغة PDF صالحة");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("✅ تم التحقق من صحة ملف PDF");
                }

                return isPdf;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في فحص نوع الملف: {ex.Message}");
                return false;
            }
        }

        // تقوم هذه الدالة بمعالجة تغيير القيمة المختارة في القائمة المنسدلة لأرقام الطلبات:
        // 1. إذا كانت القيمة المختارة ليست "0"، يتم استدعاء الدالة LoadOrderDetails لتحميل تفاصيل الطلب بناءً على رقم الطلب المختار.

        protected void ddlOrderNumbers_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (ddlOrderNumbers.SelectedValue != "0")
            {
                LoadOrderDetails(ddlOrderNumbers.SelectedValue);
            }
        }


        // تقوم هذه الدالة بتحميل تفاصيل الطلب بناءً على رقم الطلب الممرر كمعامل.
        // 1. الاتصال بقاعدة البيانات باستخدام SqlConnection.
        // 2. تنفيذ استعلام SQL لاسترجاع تفاصيل الطلبات المرتبطة برقم الطلب المحدد.
        // 3. تعيين القيم المسترجعة في الحقول المناسبة (مثل رقم الطلب، حالة الطلب، تفاصيل المشرفين...إلخ).
        // 4. إذا تم العثور على الطلب، يتم عرض التفاصيل في OrderDetailsPanel.
        // 5. إذا لم يتم العثور على الطلب، يتم إخفاء OrderDetailsPanel.
        private void LoadOrderDetails(string orderNumber)
        {
            string connectionString = ConfigurationManager.ConnectionStrings["MyConnectionString"].ConnectionString;

            using (SqlConnection con = new SqlConnection(connectionString))
            {
                string query = @"SELECT [رقم الطلب], [تاريخ الطلب], [حالة الطلب], [نوع الطلب], [اسم الموظف],
                [القسم], [تفاصيل مقدم الطلب], [الوظيفة], [رقم الموظف], [السجل المدني], 
                [الجنسية], [رقم الجوال], [نوع التوظيف], [المؤهل], 
                [تم التأكيد/الإلغاء من مدير القسم], [تم التأكيد/الإلغاء من قبل المشرف], 
                [تم التحويل/الإلغاء من قبل المنسق], [سبب الإلغاء/الإعادة], [تفاصيل المنسق], 
                [مشرف خدمات الموظفين], [مشرف إدارة تخطيط الموارد البشرية], 
                [مشرف إدارة تقنية المعلومات], [مشرف مراقبة الدوام], [مشرف السجلات الطبية], 
                [مشرف إدارة الرواتب والاستحقاقات], [مشرف إدارة القانونية والالتزام], 
                [مشرف خدمات الموارد البشرية], [مشرف إدارة الإسكان], [مشرف قسم الملفات], 
                [مشرف العيادات الخارجية], [مشرف التأمينات الاجتماعية], [مشرف وحدة مراقبة المخزون], 
                [مشرف إدارة تنمية الإيرادات], [مشرف إدارة الأمن و السلامة], [مشرف الطب الاتصالي], 
                [مدير الموارد البشرية], [نوع التحويل], [ملاحظات المشرفين], 
                [ملف1], [ملف2], [ملف3], [ملف4]  
     FROM ordersTable 
     WHERE [رقم الطلب] = @OrderNumber AND [حالة الطلب] LIKE '(A%'";


                using (SqlCommand cmd = new SqlCommand(query, con))
                {
                    cmd.Parameters.AddWithValue("@OrderNumber", orderNumber);
                    con.Open();
                    SqlDataReader reader = cmd.ExecuteReader();

                    if (reader.Read())
                    {
                        SetLabelVisibilityAndText(LabelOrderNumber, reader["رقم الطلب"].ToString());
                        SetLabelVisibilityAndText(LabelOrderDate, reader["تاريخ الطلب"].ToString());
                        SetLabelVisibilityAndText(LabelOrderStatus, reader["حالة الطلب"].ToString());
                        SetLabelVisibilityAndText(LabelOrderType, reader["نوع الطلب"].ToString());
                        SetLabelVisibilityAndText(LabelEmployeeName, reader["اسم الموظف"].ToString());
                        SetLabelVisibilityAndText(LabelDepartment, reader["القسم"].ToString());
                        SetLabelVisibilityAndText(LabelNotes, reader["تفاصيل مقدم الطلب"].ToString());
                        SetLabelVisibilityAndText(LabelJobTitle, reader["الوظيفة"].ToString());
                        SetLabelVisibilityAndText(LabelEmployeeNumber, reader["رقم الموظف"].ToString());
                        SetLabelVisibilityAndText(LabelCivilRegistry, reader["السجل المدني"].ToString());
                        SetLabelVisibilityAndText(LabelNationality, reader["الجنسية"].ToString());
                        SetLabelVisibilityAndText(LabelMobileNumber, reader["رقم الجوال"].ToString());
                        SetLabelVisibilityAndText(LabelEmploymentType, reader["نوع التوظيف"].ToString());
                        SetLabelVisibilityAndText(LabelQualification, reader["المؤهل"].ToString());
                        SetLabelVisibilityAndText(LabelManagerApproval, reader["تم التأكيد/الإلغاء من مدير القسم"].ToString());
                        SetLabelVisibilityAndText(LabelSupervisorApproval, reader["تم التأكيد/الإلغاء من قبل المشرف"].ToString());
                        SetLabelVisibilityAndText(LabelCoordinatorApproval, reader["تم التحويل/الإلغاء من قبل المنسق"].ToString());
                        SetLabelVisibilityAndText(LabelCancellationReason, reader["سبب الإلغاء/الإعادة"].ToString());
                        SetLabelVisibilityAndText(LabelCoordinatorDetails, reader["تفاصيل المنسق"].ToString());
                        SetLabelVisibilityAndText1(LabelMedicalServicesPermission, reader["مشرف خدمات الموظفين"].ToString());
                        SetLabelVisibilityAndText1(LabelHRPlanningPermission, reader["مشرف إدارة تخطيط الموارد البشرية"].ToString());
                        SetLabelVisibilityAndText1(LabelITPermission, reader["مشرف إدارة تقنية المعلومات"].ToString());
                        SetLabelVisibilityAndText1(LabelAttendanceControlPermission, reader["مشرف مراقبة الدوام"].ToString());
                        SetLabelVisibilityAndText1(LabelMedicalRecordsPermission, reader["مشرف السجلات الطبية"].ToString());
                        SetLabelVisibilityAndText1(LabelPayrollPermission, reader["مشرف إدارة الرواتب والاستحقاقات"].ToString());
                        SetLabelVisibilityAndText1(LabelLegalCompliancePermission, reader["مشرف إدارة القانونية والالتزام"].ToString());
                        SetLabelVisibilityAndText1(LabelHRServicesPermission, reader["مشرف خدمات الموارد البشرية"].ToString());
                        SetLabelVisibilityAndText1(LabelHousingPermission, reader["مشرف إدارة الإسكان"].ToString());
                        SetLabelVisibilityAndText1(LabelFilesSectionPermission, reader["مشرف قسم الملفات"].ToString());
                        SetLabelVisibilityAndText1(LabelOutpatientPermission, reader["مشرف العيادات الخارجية"].ToString());
                        SetLabelVisibilityAndText1(LabelSocialInsurancePermission, reader["مشرف التأمينات الاجتماعية"].ToString());
                        SetLabelVisibilityAndText1(LabelInventoryControlPermission, reader["مشرف وحدة مراقبة المخزون"].ToString());
                        SetLabelVisibilityAndText1(LabelSelfResourcesPermission, reader["مشرف إدارة تنمية الإيرادات"].ToString());
                        SetLabelVisibilityAndText1(LabelNursingPermission, reader["مشرف إدارة الأمن و السلامة"].ToString());
                        SetLabelVisibilityAndText1(LabelEmployeeServicesPermission, reader["مشرف الطب الاتصالي"].ToString());
                        SetLabelVisibilityAndText(LabelHRManagerApproval, reader["مدير الموارد البشرية"].ToString());
                        OrderDetailsPanel.Visible = true;
                    }
                    else
                    {
                        OrderDetailsPanel.Visible = false;
                    }
                }
            }
        }

        // تقوم هذه الدالة بتعيين نص معين إلى عنصر Label وتحديد ما إذا كان يجب إظهاره أو إخفاؤه.
        // 1. إذا كان النص الممرر غير فارغ أو يحتوي على مسافات فقط، يتم التحقق مما إذا كان يمكن تحويله إلى تاريخ.
        // 2. إذا كان النص يمثل تاريخًا، يتم تحويله إلى صيغة "yyyy-MM-dd" وعرضه في Label.
        // 3. إذا لم يكن النص يمثل تاريخًا، يتم تعيين النص كما هو إلى Label.
        // 4. إذا كان النص فارغًا أو null، يتم إخفاء الـ Label.
        private void SetLabelVisibilityAndText(Label label, string text)
        {
            if (!string.IsNullOrWhiteSpace(text))
            {


                DateTime dateValue;
                if (DateTime.TryParse(text, out dateValue))
                {
                    label.Text = dateValue.ToString("yyyy-MM-dd");
                    label.Visible = true;
                }
                else
                {

                    label.Text = text;
                    label.Visible = true;
                }
            }
            else
            {
                label.Visible = false;
            }


        }
        // تقوم هذه الدالة بتعيين نص إلى عنصر Label مع تعديل النص إذا احتوى على عبارة "اعتماد بواسطة".
        // 1. إذا كان النص يحتوي على "اعتماد بواسطة"، تتم إزالة هذه العبارة.
        // 2. إذا كان النص فارغًا أو null، يتم تعيين النص إلى "/".
        // 3. يتم عرض الـ Label وتعيين النص المعدل إليه.
        private void SetLabelVisibilityAndText1(Label label, string text)
        {
            if (text.Contains("اعتماد بواسطة"))
            {
                text = text.Replace("اعتماد بواسطة", "").Trim();
            }

            if (string.IsNullOrEmpty(text))
            {
                text = "/";
            }

            label.Visible = true; // Make sure the label is visible
            label.Text = text; // Set the text of the label

        }



        // تقوم هذه الدالة بتحميل أرقام الطلبات في القائمة المنسدلة بناءً على صلاحية المستخدم المخزنة في الجلسة:
        // 1. يتم التحقق من صلاحية المستخدم (القسم) المخزنة في الجلسة.
        // 2. إذا كانت الصلاحية غير موجودة أو فارغة، يتم عرض رسالة خطأ.
        // 3. إذا كانت الصلاحية "مدير الموارد البشرية"، يتم استرجاع أرقام الطلبات التي حالتها '(1)'.
        // 4. إذا كانت الصلاحية لقسم آخر، يتم استرجاع أرقام الطلبات التي حالتها '(A)' والمخصصة لهذا القسم.
        // 5. يتم تعيين البيانات المسترجعة إلى القائمة المنسدلة (ddlOrderNumbers)، وإضافة خيار افتراضي في بداية القائمة.
        private readonly string connectionString = ConfigurationManager.ConnectionStrings["MyConnectionString"].ConnectionString;
        private void PopulateOrderNumbers()
        {
            try
            {
                // التحقق من وجود AssistantManagerID في الجلسة
                if (Session["AssistantManagerID"] == null)
                {
                    ShowError("لم يتم تحديد مساعد المدير. يرجى تسجيل الدخول.");
                    return;
                }

                string currentAssistantManagerId = Session["AssistantManagerID"].ToString().Trim();
                if (string.IsNullOrEmpty(currentAssistantManagerId))
                {
                    ShowError("معرف مساعد المدير غير صالح.");
                    return;
                }

                // استعلام لجلب الطلبات المناسبة لمساعد المدير المحدد
                string query = @"
    SELECT o.[رقم الطلب], 
           o.[اسم الموظف],  -- تمت إضافة هذا الحقل
           o.[القسم], 
           o.[حالة الطلب],
           o.[تم التأكيد/الإلغاء من قبل المشرف],
           o.[تم التحويل/الإلغاء من قبل المنسق],
           o.[سبب الإلغاء/الإعادة]
    FROM ordersTable o
    WHERE o.[حالة الطلب] = @Status 
    AND (
        (o.[تم التأكيد/الإلغاء من قبل المشرف] LIKE @SupervisorStatus)
        OR
        (o.[تم التحويل/الإلغاء من قبل المنسق] IS NOT NULL 
         AND o.[تم التحويل/الإلغاء من قبل المنسق] LIKE '%تم الإعادة%')
    )
    ORDER BY o.[تاريخ الطلب] DESC, 
             o.[رقم الطلب] DESC";

                using (SqlConnection con = new SqlConnection(connectionString))
                {
                    using (SqlCommand cmd = new SqlCommand(query, con))
                    {
                        string requiredStatus = $"({currentAssistantManagerId})";
                        cmd.Parameters.AddWithValue("@Status", requiredStatus);
                        cmd.Parameters.AddWithValue("@SupervisorStatus", "%الطلب تحت التنفيذ%");

                        con.Open();
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            ddlOrderNumbers.Items.Clear();
                            ddlOrderNumbers.Items.Add(new ListItem("-- اختر رقم الطلب --", ""));

                            while (reader.Read())
                            {
                                string orderNumber = reader["رقم الطلب"].ToString();
                                string employeeName = reader["اسم الموظف"]?.ToString() ?? "غير محدد";
                                string department = reader["القسم"].ToString();
                                string supervisorStatus = reader["تم التأكيد/الإلغاء من قبل المشرف"]?.ToString() ?? "";
                                string coordinatorStatus = reader["تم التحويل/الإلغاء من قبل المنسق"]?.ToString() ?? "";
                                string rejectReason = reader["سبب الإلغاء/الإعادة"]?.ToString() ?? "";

                                string displayText = $"{orderNumber} - {employeeName} - {department}";
                                if (!string.IsNullOrEmpty(coordinatorStatus) && coordinatorStatus.Contains("تم الإعادة"))
                                {
                                    displayText += " (معاد)";
                                    if (!string.IsNullOrEmpty(rejectReason))
                                    {
                                        displayText += $" - {rejectReason}";
                                    }
                                }

                                ddlOrderNumbers.Items.Add(new ListItem(displayText, orderNumber));
                            }
                        }
                    }
                }

                // تحديث رسالة عدد الطلبات
                int orderCount = ddlOrderNumbers.Items.Count - 1;
                LabelMessage.Text = orderCount <= 0 ? "لا توجد طلبات جديدة" : $"تم تحميل {orderCount} طلب";
                LabelMessage.Visible = true;
            }
            catch (Exception ex)
            {
                LogError(ex);
                ShowError("حدث خطأ أثناء تحميل الطلبات: " + ex.Message);
            }
        }








        public class AssistantManager
        {
            public string Id { get; set; } // تعديل النوع إلى string
            public string Name { get; set; }
        }


        private AssistantManager GetCurrentAssistantManager()
        {
            try
            {
                // التحقق من وجود اسم المستخدم في الجلسة
                string username = Session["Username"]?.ToString();
                if (string.IsNullOrEmpty(username))
                {
                    throw new InvalidOperationException("لم يتم تسجيل الدخول. يرجى تسجيل الدخول أولاً.");
                }

                System.Diagnostics.Debug.WriteLine($"Current Username: {username}");

                // استعلام لجلب الصلاحية من جدول login
                string query = @"
            SELECT 
                permission
            FROM login
            WHERE username = @Username";

                using (SqlConnection con = new SqlConnection(connectionString))
                {
                    using (SqlCommand cmd = new SqlCommand(query, con))
                    {
                        cmd.Parameters.AddWithValue("@Username", username);
                        con.Open();

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                string permission = reader.GetString(0);

                                // تحديد AssistantManagerID بناءً على الصلاحية باستخدام switch expression
                                string assistantManagerId;
                                switch (permission)
                                {
                                    case "مساعد المدير للخدمات الطبية":
                                        assistantManagerId = "A1";
                                        break;
                                    case "مساعد المدير لخدمات التمريض":
                                        assistantManagerId = "A2";
                                        break;
                                    case "مساعد المدير للخدمات الإدارية والتشغيل":
                                        assistantManagerId = "A3";
                                        break;
                                    case "مساعد المدير للموارد البشرية":
                                        assistantManagerId = "A4";
                                        break;
                                    default:
                                        assistantManagerId = "غير معروف";
                                        break;
                                }


                                var result = new AssistantManager
                                {
                                    Id = assistantManagerId,
                                    Name = permission
                                };

                                System.Diagnostics.Debug.WriteLine($"Found AssistantManager: ID={result.Id}, Name={result.Name}");
                                return result;
                            }
                            else
                            {
                                throw new InvalidOperationException("لم يتم العثور على الصلاحية للمستخدم.");
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in GetCurrentAssistantManager: {ex.Message}");
                throw new Exception("حدث خطأ أثناء جلب معلومات مساعد المدير.", ex);
            }
        }


        protected void btnLogin_Click(object sender, EventArgs e)
        {
            string username = hfUsername.Value.Trim();
            string password = hfPassword.Value.Trim();


            string query = "SELECT username, permission FROM login WHERE username = @Username AND password = @Password";

            using (SqlConnection con = new SqlConnection(connectionString))
            {
                using (SqlCommand cmd = new SqlCommand(query, con))
                {
                    cmd.Parameters.AddWithValue("@Username", username);
                    cmd.Parameters.AddWithValue("@Password", password);
                    con.Open();

                    using (SqlDataReader reader = cmd.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            // تخزين اسم المستخدم والصلاحية في الجلسة
                            Session["Username"] = reader["username"].ToString();
                            Session["UserPermission"] = reader["permission"].ToString();

                            // إعادة توجيه المستخدم إلى الصفحة الرئيسية
                            Response.Redirect("Home.aspx");
                        }
                        else
                        {
                            ShowError("حدث خطأ أثناء تحميل الصفحة.");

                        }
                    }
                }
            }
        }

        protected void btnReturnToManager_Click(object sender, EventArgs e)
        {
            try
            {
                if (ddlOrderNumbers.SelectedIndex == 0)
                {
                    ShowError("يرجى اختيار رقم الطلب.");
                    return;
                }

                string returnReason = txtReturnReason.Text.Trim();
                if (string.IsNullOrEmpty(returnReason))
                {
                    ShowError("يرجى إدخال سبب الإعادة إلى مدير القسم.");
                    return;
                }

                string selectedOrderNumber = ddlOrderNumbers.SelectedValue;
                string confirmedBy = Session["Username"]?.ToString();
                string currentDate = DateTime.Now.ToString("yyyy-MM-dd");
                string returnStatus = $"{currentDate} | تمت الإعادة من مساعد المدير: {confirmedBy}";

                using (SqlConnection con = new SqlConnection(connectionString))
                {
                    con.Open();
                    using (var transaction = con.BeginTransaction())
                    {
                        try
                        {
                            // تحديث الطلب
                            string updateQuery = @"
                        UPDATE ordersTable 
                        SET [حالة الطلب] = '(DM)',
                            [تم التأكيد/الإلغاء من قبل المشرف] = @ReturnStatus,
                            [سبب الإلغاء/الإعادة] = @ReturnReason,
                            [تم التحويل/الإلغاء من قبل المنسق] = NULL
                        WHERE [رقم الطلب] = @OrderNumber";

                            using (var cmdUpdate = new SqlCommand(updateQuery, con, transaction))
                            {
                                cmdUpdate.Parameters.AddWithValue("@OrderNumber", selectedOrderNumber);
                                cmdUpdate.Parameters.AddWithValue("@ReturnStatus", returnStatus);
                                cmdUpdate.Parameters.AddWithValue("@ReturnReason", returnReason);
                                cmdUpdate.ExecuteNonQuery();
                            }

                            transaction.Commit();
                            ShowSuccess("تم إعادة الطلب إلى مدير القسم بنجاح.");
                            ClearFields();
                            UpdateUI();
                        }
                        catch (Exception )
                        {
                            transaction.Rollback();
                            throw;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogError(ex);
                ShowError("حدث خطأ أثناء إعادة الطلب: " + ex.Message);
            }
        }

        // دالة مساعدة لتنظيف الحقول
        private void ClearFields()
        {
            txtReturnReason.Text = string.Empty;
        }








        protected void btnConfirmOrder_Click(object sender, EventArgs e)
        {
            try
            {
                if (ddlOrderNumbers.SelectedIndex == 0)
                {
                    ShowError("يرجى اختيار رقم الطلب.");
                    return;
                }

                string selectedOrderNumber = ddlOrderNumbers.SelectedValue;
                string confirmedBy = Session["Username"]?.ToString();
                string currentDate = DateTime.Now.ToString("yyyy-MM-dd");
                string approvalText = $"{currentDate} | اعتماد بواسطة {confirmedBy} {currentDate}";

                using (SqlConnection con = new SqlConnection(connectionString))
                {
                    con.Open();
                    using (var transaction = con.BeginTransaction())
                    {
                        try
                        {
                            // الحصول على معلومات الطلب الحالية
                            string checkQuery = @"
                        SELECT [تم التأكيد/الإلغاء من قبل المشرف], [حالة الطلب]
                        FROM ordersTable 
                        WHERE [رقم الطلب] = @OrderNumber";

                            string currentSupervisorStatus;
                            using (var cmdCheck = new SqlCommand(checkQuery, con, transaction))
                            {
                                cmdCheck.Parameters.AddWithValue("@OrderNumber", selectedOrderNumber);
                                using (var reader = cmdCheck.ExecuteReader())
                                {
                                    if (!reader.Read())
                                    {
                                        throw new Exception("لم يتم العثور على الطلب");
                                    }
                                    currentSupervisorStatus = reader["تم التأكيد/الإلغاء من قبل المشرف"]?.ToString();
                                }
                            }

                            // تحديث حالة المشرف مع الحفاظ على التاريخ الأصلي
                            string newSupervisorStatus;
                            if (currentSupervisorStatus != null && currentSupervisorStatus.Contains("الطلب تحت التنفيذ"))
                            {
                                string originalDate = currentSupervisorStatus.Split(' ')[0];
                                newSupervisorStatus = $"{originalDate} | اعتماد بواسطة {confirmedBy} {currentDate}";
                            }
                            else
                            {
                                newSupervisorStatus = approvalText;
                            }

                            // تحديث الطلب - تغيير الحالة إلى (B) وتحديث حالة المشرف
                            string updateQuery = @"
                        UPDATE ordersTable 
                        SET [حالة الطلب] = '(B)',
                            [تم التأكيد/الإلغاء من قبل المشرف] = @SupervisorStatus
                        WHERE [رقم الطلب] = @OrderNumber";

                            using (var cmdUpdate = new SqlCommand(updateQuery, con, transaction))
                            {
                                cmdUpdate.Parameters.AddWithValue("@OrderNumber", selectedOrderNumber);
                                cmdUpdate.Parameters.AddWithValue("@SupervisorStatus", newSupervisorStatus);
                                cmdUpdate.ExecuteNonQuery();
                            }

                            transaction.Commit();
                            ShowSuccess("تم تحويل الطلب إلى منسق الموارد البشرية");
                            UpdateUI();
                        }
                        catch (Exception ex)
                        {
                            transaction.Rollback();
                            throw new Exception("حدث خطأ أثناء معالجة الطلب", ex);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogError(ex);
                ShowError("حدث خطأ أثناء معالجة الطلب: " + ex.Message);
            }
        }

        private string CleanJobTitle(string job)
        {
            job = job.Trim();
            return job.StartsWith("أخرى - Other:") ? "أخرى" : job;
        }

        private string ProcessNationality(string nationality)
        {
            if (string.IsNullOrWhiteSpace(nationality))
                return string.Empty;

            nationality = nationality.Trim();

            // معالجة الجنسية السعودية بمختلف صيغها
            string[] saudiVariations = new[] {
        "سعودي",
        "سعودية",
        "السعودية",
        "سعودي - Saudi",
        "Saudi"
    };

            if (saudiVariations.Any(v => nationality.Contains(v)))
            {
                return "سعودي - Saudi";
            }

            // معالجة جميع الجنسيات الأخرى باستثناء "غير سعودي - Non-Saudi"
            if (!nationality.Contains("غير سعودي - Non-Saudi") &&
                !nationality.Contains("Other") &&
                !saudiVariations.Any(v => nationality.Contains(v)))
            {
                return "غير سعودي - Non-Saudi";
            }

            return nationality;
        }


        private void ProcessOrder(string orderNumber, string approvalText, string job, string orderType, string nationality)
        {
            using (SqlConnection con = GetConnection())
            {
                con.Open();
                using (var transaction = con.BeginTransaction())
                {
                    try
                    {
                        bool isFastRoute = CheckFastRoute(con, transaction, orderType, nationality, job);
                        if (isFastRoute)
                        {
                            ProcessFastRoute(con, transaction, orderNumber, approvalText);
                        }
                        else
                        {
                            ProcessNormalRoute(con, transaction, orderNumber, approvalText);
                        }
                        transaction.Commit();
                        ShowSuccess(isFastRoute ? "تم التحويل إلى المشرفين." : "تم التحويل إلى منسق الموارد البشرية.");
                        UpdateUI();
                    }
                    catch (Exception ex)
                    {
                        transaction.Rollback();
                        throw new Exception("فشل في معالجة الطلب", ex);
                    }
                }
            }
        }

        private bool CheckFastRoute(SqlConnection con, SqlTransaction transaction, string orderType, string nationality, string job)
        {
            string query = @"
        SELECT المشرفين 
        FROM DirectRouting
        WHERE حالة_المسار = 1 
        AND نوع_الطلب = @OrderType
        AND الجنسية = @Nationality
        AND (
            الوظيفة = 'ALL' 
            OR الوظيفة = @Job
            OR (الوظيفة = 'ALL_EXCEPT_OTHER' AND @Job NOT LIKE 'أخرى - Other:%')
        )";

            using (var cmd = new SqlCommand(query, con, transaction))
            {
                cmd.Parameters.AddWithValue("@OrderType", orderType);
                cmd.Parameters.AddWithValue("@Nationality", nationality);
                cmd.Parameters.AddWithValue("@Job", job);

                var result = cmd.ExecuteScalar();
                return (result != null && result != DBNull.Value);
            }
        }

        private void ProcessFastRoute(SqlConnection con, SqlTransaction transaction, string orderNumber, string approvalText)
        {
            string nationality = ProcessNationality(LabelNationality.Text.Trim());
            string supervisorsQuery = @"
        SELECT المشرفين 
        FROM DirectRouting
        WHERE حالة_المسار = 1 
        AND نوع_الطلب = @OrderType
        AND الجنسية = @Nationality
        AND (
            الوظيفة = 'ALL' 
            OR الوظيفة = @Job
            OR (الوظيفة = 'ALL_EXCEPT_OTHER' AND @Job NOT LIKE 'أخرى - Other:%')
        )";

            using (var cmdSupervisors = new SqlCommand(supervisorsQuery, con, transaction))
            {
                cmdSupervisors.Parameters.AddWithValue("@OrderType", LabelOrderType.Text.Trim());
                cmdSupervisors.Parameters.AddWithValue("@Nationality", nationality); // قيمة الجنسية المعالجة
                cmdSupervisors.Parameters.AddWithValue("@Job", LabelJobTitle.Text.Trim());

                string supervisors = cmdSupervisors.ExecuteScalar()?.ToString() ?? "";

                // 2. نقوم بتحديث حالة كل مشرف
                foreach (string supervisor in supervisors.Split(';'))
                {
                    if (!string.IsNullOrWhiteSpace(supervisor))
                    {
                        string columnName = "مشرف " + supervisor.Trim();
                        string updateSupervisorQuery = $@"
                    UPDATE ordersTable 
                    SET [{columnName}] = @StatusWithDate 
                    WHERE [رقم الطلب] = @OrderNumber
                    AND (ISNULL([{columnName}], '') = '' 
                        OR [{columnName}] = '/' 
                        OR [{columnName}] LIKE 'تم الإعادة%')";

                        using (var cmdUpdateSupervisor = new SqlCommand(updateSupervisorQuery, con, transaction))
                        {
                            string statusWithDate = $"{DateTime.Now:yyyy-MM-dd} الطلب تحت التنفيذ";
                            cmdUpdateSupervisor.Parameters.AddWithValue("@StatusWithDate", statusWithDate);
                            cmdUpdateSupervisor.Parameters.AddWithValue("@OrderNumber", orderNumber);
                            cmdUpdateSupervisor.ExecuteNonQuery();
                        }
                    }
                }

                // 3. نقوم بتحديث حالة الطلب
                string updateOrderQuery = @"
            UPDATE ordersTable 
            SET [حالة الطلب] = '(C)', 
                [تم التأكيد/الإلغاء من قبل المشرف] = @ConfirmedBy, 
                [تم التحويل/الإلغاء من قبل المنسق] = '_',
                [تفاصيل المنسق] = ISNULL([تفاصيل المنسق], '') + @Rakam, 
                [سبب الإلغاء/الإعادة] = @RejectReason,
                [نوع التحويل] = 'سريع' 
            WHERE [رقم الطلب] = @OrderNumber";

                using (var cmdUpdateOrder = new SqlCommand(updateOrderQuery, con, transaction))
                {
                    cmdUpdateOrder.Parameters.AddWithValue("@OrderNumber", orderNumber);
                    cmdUpdateOrder.Parameters.AddWithValue("@ConfirmedBy", approvalText);
                    cmdUpdateOrder.Parameters.AddWithValue("@Rakam", "مسار سريع");
                    cmdUpdateOrder.Parameters.AddWithValue("@RejectReason", string.Empty);
                    cmdUpdateOrder.ExecuteNonQuery();
                }
            }
        }

        private void ProcessNormalRoute(SqlConnection con, SqlTransaction transaction, string orderNumber, string approvalText)
        {
            // تحديث الطلب مباشرة بحالة (B) للتحويل إلى منسق الموارد البشرية
            string updateQuery = @"
        UPDATE ordersTable 
        SET [حالة الطلب] = '(B)',
            [تم التأكيد/الإلغاء من مدير القسم] = @ApprovalText
        WHERE [رقم الطلب] = @OrderNumber";

            using (var cmd = new SqlCommand(updateQuery, con, transaction))
            {
                cmd.Parameters.AddWithValue("@OrderNumber", orderNumber);
                cmd.Parameters.AddWithValue("@ApprovalText", approvalText);
                cmd.ExecuteNonQuery();
            }
        }

        private string GetDestinationText(string assistantManagerId) // تغيير النوع إلى string
        {
            switch (assistantManagerId)
            {
                case "A1":
                case "A2":
                case "A3":
                case "A4":
                    return "منسق الموارد البشرية";
                case "B":
                    return "المدير العام";
                default:
                    return "غير معروف";
            }
        }

        private string GetAssistantManagerId(SqlConnection con, string orderNumber)
        {
            string query = @"
        SELECT d.AssistantManagerID 
        FROM ordersTable o
        JOIN Departments d ON o.[القسم] = d.[الأقسام]
        WHERE o.[رقم الطلب] = @OrderNumber";

            using (var cmd = new SqlCommand(query, con))
            {
                cmd.Parameters.AddWithValue("@OrderNumber", orderNumber);
                var result = cmd.ExecuteScalar()?.ToString();
                return result ?? "غير معروف";
            }
        }


        private void UpdateUI()
        {
            OrderDetailsPanel.Visible = false;
            PopulateOrderNumbers();
            if (Master is SiteMaster master)
            {
                master.UpdateNewOrdersCount();
            }
        }

        private void ShowError(string message)
        {
            LabelError.Text = message;
            LabelError.Visible = true;
            LabelMessage.Visible = false;
        }

        private void ShowSuccess(string message)
        {
            LabelMessage.Text = message;
            LabelMessage.Visible = true;
            LabelError.Visible = false;
        }

        private SqlConnection GetConnection()
        {
            string connectionString = ConfigurationManager.ConnectionStrings["MyConnectionString"].ConnectionString;
            return new SqlConnection(connectionString);
        }

        private void LogError(Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"=== خطأ ===");
            System.Diagnostics.Debug.WriteLine($"نوع الخطأ: {ex.GetType().Name}");
            System.Diagnostics.Debug.WriteLine($"رسالة الخطأ: {ex.Message}");
            System.Diagnostics.Debug.WriteLine($"تفاصيل الخطأ: {ex.StackTrace}");
        }







        // تقوم هذه الدالة بمعالجة إلغاء الطلب عند النقر على زر "Reject Order":
        // 1. التحقق مما إذا تم اختيار رقم طلب صحيح من القائمة المنسدلة.
        // 2. إذا لم يتم اختيار رقم طلب، يتم عرض رسالة خطأ.
        // 3. التحقق مما إذا تم إدخال سبب الإلغاء. إذا كان السبب فارغًا، يتم عرض رسالة خطأ.
        // 4. يتم استرجاع رقم الطلب المحدد واسم المستخدم من الجلسة.
        // 5. يتم تحديث حالة الطلب في قاعدة البيانات إلى "تم الإلغاء من قبل المشرف"، وتحديث حقل سبب الإلغاء وحقل المشرف الذي قام بالإلغاء.
        // 6. إذا تم التحديث بنجاح، يتم عرض رسالة تأكيد، إخفاء تفاصيل الطلب، وتحديث قائمة أرقام الطلبات.
        // 7. إذا فشل التحديث، يتم عرض رسالة خطأ.
        protected void btnRejectOrder_Click(object sender, EventArgs e)
        {
            // Check if a valid order number is selected
            if (ddlOrderNumbers.SelectedIndex == 0)
            {
                LabelError.Text = "يرجى اختيار رقم الطلب.";
                LabelError.Visible = true;
                LabelMessage.Visible = false;
                return;
            }
            string rejectReason = txtRejectReason.Text.Trim();

            if (string.IsNullOrEmpty(rejectReason))
            {
                LabelError.Text = "يرجى إدخال سبب الإلغاء.";
                LabelError.Visible = true;
                return;
            }
            else
            {

                string selectedOrderNumber = ddlOrderNumbers.SelectedValue; // Use SelectedValue to get the actual value
                string confirmedBy = Session["Username"]?.ToString(); // Retrieve the username from session
                string connectionString = ConfigurationManager.ConnectionStrings["MyConnectionString"].ConnectionString;

                using (SqlConnection con = new SqlConnection(connectionString))
                {
                    string query = "UPDATE ordersTable SET [حالة الطلب] = 'تم الإلغاء من قبل المشرف', [تم التأكيد/الإلغاء من قبل المشرف] = @ConfirmedBy , [سبب الإلغاء/الإعادة] = @RejectReason WHERE [رقم الطلب] = @OrderNumber";
                    using (SqlCommand cmd = new SqlCommand(query, con))
                    {
                        cmd.Parameters.AddWithValue("@OrderNumber", selectedOrderNumber);
                        cmd.Parameters.AddWithValue("@ConfirmedBy", confirmedBy);
                        cmd.Parameters.AddWithValue("@RejectReason", rejectReason);

                        con.Open();
                        int rowsAffected = cmd.ExecuteNonQuery();

                        if (rowsAffected > 0)
                        {



                            LabelMessage.Text = "تم إلغاء الطلب بنجاح.";
                            OrderDetailsPanel.Visible = false;
                            LabelMessage.Visible = true;
                            LabelError.Visible = false;
                            PopulateOrderNumbers(); // Refresh the list after updating
                            SiteMaster master = (SiteMaster)Master;
                            if (master != null)
                            {
                                master.UpdateNewOrdersCount();
                            }


                        }
                        else
                        {
                            LabelError.Text = "حدث خطأ أثناء إلغاء الطلب.";
                            LabelError.Visible = true;
                            LabelMessage.Visible = false;
                        }
                    }
                }
            }





        }
    }
}