@{
    ViewData["Title"] = "إعدادات النظام";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <div>
                    <h1 class="page-title">إعدادات النظام</h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="@Url.Action("Index", "Dashboard")">لوحة التحكم</a></li>
                            <li class="breadcrumb-item active" aria-current="page">الإعدادات</li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </div>

    <div class="row g-4">
        <!-- إدارة الأقسام -->
        <div class="col-lg-4 col-md-6">
            <div class="card border-0 shadow h-100">
                <div class="card-body text-center">
                    <div class="mb-3">
                        <i class="fas fa-building fa-3x text-primary"></i>
                    </div>
                    <h5 class="card-title">إدارة الأقسام</h5>
                    <p class="card-text">إضافة وتعديل وحذف الأقسام في النظام</p>
                    <a asp-controller="Department" asp-action="Index" class="btn btn-primary">
                        <i class="fas fa-cog me-2"></i>إدارة الأقسام
                    </a>
                </div>
            </div>
        </div>

        <!-- إدارة أنواع الوظائف -->
        <div class="col-lg-4 col-md-6">
            <div class="card border-0 shadow h-100">
                <div class="card-body text-center">
                    <div class="mb-3">
                        <i class="fas fa-user-tie fa-3x text-success"></i>
                    </div>
                    <h5 class="card-title">أنواع الوظائف</h5>
                    <p class="card-text">إدارة أنواع الوظائف والمسميات الوظيفية</p>
                    <a asp-controller="JobType" asp-action="Index" class="btn btn-success">
                        <i class="fas fa-cog me-2"></i>إدارة أنواع الوظائف
                    </a>
                </div>
            </div>
        </div>

        <!-- إدارة أنواع الطلبات -->
        <div class="col-lg-4 col-md-6">
            <div class="card border-0 shadow h-100">
                <div class="card-body text-center">
                    <div class="mb-3">
                        <i class="fas fa-file-alt fa-3x text-info"></i>
                    </div>
                    <h5 class="card-title">أنواع الطلبات</h5>
                    <p class="card-text">إدارة أنواع الطلبات المتاحة في النظام</p>
                    <a asp-controller="OrdersType" asp-action="Index" class="btn btn-info">
                        <i class="fas fa-cog me-2"></i>إدارة أنواع الطلبات
                    </a>
                </div>
            </div>
        </div>

        <!-- إدارة الجنسيات -->
        <div class="col-lg-4 col-md-6">
            <div class="card border-0 shadow h-100">
                <div class="card-body text-center">
                    <div class="mb-3">
                        <i class="fas fa-globe fa-3x text-warning"></i>
                    </div>
                    <h5 class="card-title">الجنسيات</h5>
                    <p class="card-text">إدارة قائمة الجنسيات المتاحة</p>
                    <a asp-controller="Nationality" asp-action="Index" class="btn btn-warning">
                        <i class="fas fa-cog me-2"></i>إدارة الجنسيات
                    </a>
                </div>
            </div>
        </div>

        <!-- إدارة أنواع التوظيف -->
        <div class="col-lg-4 col-md-6">
            <div class="card border-0 shadow h-100">
                <div class="card-body text-center">
                    <div class="mb-3">
                        <i class="fas fa-briefcase fa-3x text-danger"></i>
                    </div>
                    <h5 class="card-title">أنواع التوظيف</h5>
                    <p class="card-text">إدارة أنواع التوظيف المختلفة</p>
                    <a asp-controller="EmploymentType" asp-action="Index" class="btn btn-danger">
                        <i class="fas fa-cog me-2"></i>إدارة أنواع التوظيف
                    </a>
                </div>
            </div>
        </div>

        <!-- إدارة المؤهلات -->
        <div class="col-lg-4 col-md-6">
            <div class="card border-0 shadow h-100">
                <div class="card-body text-center">
                    <div class="mb-3">
                        <i class="fas fa-graduation-cap fa-3x text-secondary"></i>
                    </div>
                    <h5 class="card-title">المؤهلات</h5>
                    <p class="card-text">إدارة المؤهلات العلمية والمهنية</p>
                    <a asp-controller="Qualification" asp-action="Index" class="btn btn-secondary">
                        <i class="fas fa-cog me-2"></i>إدارة المؤهلات
                    </a>
                </div>
            </div>
        </div>

        <!-- إدارة بيانات الموظفين -->
        <div class="col-lg-4 col-md-6">
            <div class="card border-0 shadow h-100">
                <div class="card-body text-center">
                    <div class="mb-3">
                        <i class="fas fa-users fa-3x text-dark"></i>
                    </div>
                    <h5 class="card-title">إدارة بيانات الموظفين</h5>
                    <p class="card-text">إضافة وتعديل وحذف بيانات الموظفين</p>
                    <a asp-controller="Employee" asp-action="Index" class="btn btn-dark">
                        <i class="fas fa-users me-2"></i>إدارة بيانات الموظفين
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .card {
        border-radius: 1rem;
        transition: all 0.3s ease;
    }
    
    .card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.15) !important;
    }
    
    .card-title {
        font-weight: 600;
        margin-bottom: 1rem;
    }
    
    .card-text {
        color: #6c757d;
        margin-bottom: 1.5rem;
    }
    
    .btn {
        border-radius: 0.5rem;
        font-weight: 500;
    }
</style> 