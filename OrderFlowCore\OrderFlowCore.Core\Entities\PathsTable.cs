﻿
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;


namespace OrderFlowCore.Core.Models;


public partial class PathsTable
{
    public int Id { get; set; }

    [StringLength(50)]
    public string Path1 { get; set; }

    [StringLength(50)]
    public string Path2 { get; set; }

    [StringLength(50)]
    public string Path3 { get; set; }

    [StringLength(50)]
    public string Path4 { get; set; }

    [StringLength(50)]
    public string Path5 { get; set; }

    [StringLength(50)]
    public string Path6 { get; set; }
}