﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace OrderFlowCore.Application.Common
{
    public class ServiceResult<T>
    {
        public bool IsSuccess { get; set; }
        public string Message { get; set; } = string.Empty;
        public T? Data { get; set; }
        public List<string> Errors { get; set; } = new();

        public static ServiceResult<T> Success(T data, string message = "")
            => new() { IsSuccess = true, Data = data, Message = message };

        public static ServiceResult<T> Failure(string message, List<string>? errors = null)
            => new() { IsSuccess = false, Message = message, Errors = errors ?? new() };
    }

    public class ServiceResult
    {
        public bool IsSuccess { get; set; }
        public string Message { get; set; } = string.Empty;
        public List<string> Errors { get; set; } = new();

        public static ServiceResult Success(string message = "")
            => new() { IsSuccess = true, Message = message };

        public static ServiceResult Failure(string message, List<string>? errors = null)
            => new() { IsSuccess = false, Message = message, Errors = errors ?? new() };
    }
}
