using System.Threading.Tasks;
using OrderFlowCore.Application.Interfaces.Repositories;
using OrderFlowCore.Infrastructure.Repositories;

namespace OrderFlowCore.Infrastructure.Data
{
    public class UnitOfWork : IUnitOfWork
    {
        private readonly ApplicationDbContext _context;
        public IUserRepository Users { get; }
        public IOrderRepository Orders { get; }
        public IOrdersTypeRepository OrdersTypes { get; }
        public IEmploymentTypeRepository EmploymentTypes { get; }
        public IJobTypeRepository JobTypes { get; }
        public IQualificationRepository Qualifications { get; }
        public INationalityRepository Nationalities { get; }
        public IDepartmentRepository Departments { get; }
        public IEmployeeRepository Employees { get; }
        public IAutoRouteRepository AutoRoutes { get; }
        public IPredefinedPathRepository PredefinedPaths { get; }

        public UnitOfWork(
            ApplicationDbContext context,
            IUserRepository users,
            IOrderRepository orders,
            IOrdersTypeRepository ordersTypes,
            IEmploymentTypeRepository employmentTypes,
            IJobTypeRepository jobTypes,
            IQualificationRepository qualifications,
            INationalityRepository nationalities,
            IDepartmentRepository departments,
            IEmployeeRepository employees,
            IAutoRouteRepository autoRoutes,
            IPredefinedPathRepository predefinedPaths)
        {
            _context = context;
            Users = users;
            Orders = orders;
            OrdersTypes = ordersTypes;
            EmploymentTypes = employmentTypes;
            JobTypes = jobTypes;
            Qualifications = qualifications;
            Nationalities = nationalities;
            Departments = departments;
            Employees = employees;
            AutoRoutes = autoRoutes;
            PredefinedPaths = predefinedPaths;
        }

        public async Task<int> SaveChangesAsync()
        {
            return await _context.SaveChangesAsync();
        }
    }
} 