using OrderFlowCore.Application.DTOs;

namespace OrderFlowCore.Application.Interfaces.Repositories;

public interface IQualificationRepository
{
    Task<IEnumerable<QualificationDto>> GetAllAsync();
    Task<QualificationDto?> GetByIdAsync(int id);
    Task<bool> CreateAsync(QualificationDto dto);
    Task<bool> UpdateAsync(QualificationDto dto);
    Task<bool> DeleteAsync(int id);
    Task<bool> ExistsAsync(string name, int? excludeId = null);
}
