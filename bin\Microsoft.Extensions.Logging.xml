<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.Extensions.Logging</name>
    </assembly>
    <members>
        <member name="T:Microsoft.Extensions.Logging.ActivityTrackingOptions">
            <summary>
            Flags to indicate which trace context parts should be included with the logging scopes.
            </summary>
        </member>
        <member name="F:Microsoft.Extensions.Logging.ActivityTrackingOptions.None">
            <summary>
            None of the trace context part will be included in the logging.
            </summary>
        </member>
        <member name="F:Microsoft.Extensions.Logging.ActivityTrackingOptions.SpanId">
            <summary>
            Span Id will be included in the logging.
            </summary>
        </member>
        <member name="F:Microsoft.Extensions.Logging.ActivityTrackingOptions.TraceId">
            <summary>
            Trace Id will be included in the logging.
            </summary>
        </member>
        <member name="F:Microsoft.Extensions.Logging.ActivityTrackingOptions.ParentId">
            <summary>
            Parent Id will be included in the logging.
            </summary>
        </member>
        <member name="F:Microsoft.Extensions.Logging.ActivityTrackingOptions.TraceState">
            <summary>
            Trace State will be included in the logging.
            </summary>
        </member>
        <member name="F:Microsoft.Extensions.Logging.ActivityTrackingOptions.TraceFlags">
            <summary>
            Trace flags will be included in the logging.
            </summary>
        </member>
        <member name="F:Microsoft.Extensions.Logging.ActivityTrackingOptions.Tags">
            <summary>
            Tags will be included in the logging.
            </summary>
        </member>
        <member name="F:Microsoft.Extensions.Logging.ActivityTrackingOptions.Baggage">
            <summary>
            Items of baggage will be included in the logging.
            </summary>
        </member>
        <member name="T:Microsoft.Extensions.Logging.FilterLoggingBuilderExtensions">
            <summary>
            Extension methods for setting up logging services in an <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" />.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Logging.FilterLoggingBuilderExtensions.AddFilter(Microsoft.Extensions.Logging.ILoggingBuilder,System.Func{System.String,System.String,Microsoft.Extensions.Logging.LogLevel,System.Boolean})">
            <summary>
            Adds a log filter to the factory.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder"/> to add the filter to.</param>
            <param name="filter">The filter to be added.</param>
            <returns>The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder"/> so that additional calls can be chained.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Logging.FilterLoggingBuilderExtensions.AddFilter(Microsoft.Extensions.Logging.ILoggingBuilder,System.Func{System.String,Microsoft.Extensions.Logging.LogLevel,System.Boolean})">
            <summary>
            Adds a log filter to the factory.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder"/> to add the filter to.</param>
            <param name="categoryLevelFilter">The filter to be added.</param>
            <returns>The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder"/> so that additional calls can be chained.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Logging.FilterLoggingBuilderExtensions.AddFilter``1(Microsoft.Extensions.Logging.ILoggingBuilder,System.Func{System.String,Microsoft.Extensions.Logging.LogLevel,System.Boolean})">
            <summary>
            Adds a log filter for the given <see cref="T:Microsoft.Extensions.Logging.ILoggerProvider"/>.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder"/> to add the filter to.</param>
            <param name="categoryLevelFilter">The filter to be added.</param>
            <typeparam name="T">The <see cref="T:Microsoft.Extensions.Logging.ILoggerProvider"/> which this filter will be added for.</typeparam>
            <returns>The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder"/> so that additional calls can be chained.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Logging.FilterLoggingBuilderExtensions.AddFilter(Microsoft.Extensions.Logging.ILoggingBuilder,System.Func{Microsoft.Extensions.Logging.LogLevel,System.Boolean})">
            <summary>
            Adds a log filter to the factory.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder"/> to add the filter to.</param>
            <param name="levelFilter">The filter to be added.</param>
            <returns>The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder"/> so that additional calls can be chained.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Logging.FilterLoggingBuilderExtensions.AddFilter``1(Microsoft.Extensions.Logging.ILoggingBuilder,System.Func{Microsoft.Extensions.Logging.LogLevel,System.Boolean})">
            <summary>
            Adds a log filter for the given <see cref="T:Microsoft.Extensions.Logging.ILoggerProvider"/>.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder"/> to add the filter to.</param>
            <param name="levelFilter">The filter to be added.</param>
            <typeparam name="T">The <see cref="T:Microsoft.Extensions.Logging.ILoggerProvider"/> which this filter will be added for.</typeparam>
            <returns>The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder"/> so that additional calls can be chained.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Logging.FilterLoggingBuilderExtensions.AddFilter(Microsoft.Extensions.Logging.ILoggingBuilder,System.String,Microsoft.Extensions.Logging.LogLevel)">
            <summary>
            Adds a log filter to the factory.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder"/> to add the filter to.</param>
            <param name="category">The category to filter.</param>
            <param name="level">The level to filter.</param>
            <returns>The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder"/> so that additional calls can be chained.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Logging.FilterLoggingBuilderExtensions.AddFilter``1(Microsoft.Extensions.Logging.ILoggingBuilder,System.String,Microsoft.Extensions.Logging.LogLevel)">
            <summary>
            Adds a log filter for the given <see cref="T:Microsoft.Extensions.Logging.ILoggerProvider"/>.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder"/> to add the filter to.</param>
            <param name="category">The category to filter.</param>
            <param name="level">The level to filter.</param>
            <typeparam name="T">The <see cref="T:Microsoft.Extensions.Logging.ILoggerProvider"/> which this filter will be added for.</typeparam>
            <returns>The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder"/> so that additional calls can be chained.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Logging.FilterLoggingBuilderExtensions.AddFilter(Microsoft.Extensions.Logging.ILoggingBuilder,System.String,System.Func{Microsoft.Extensions.Logging.LogLevel,System.Boolean})">
            <summary>
            Adds a log filter to the factory.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder"/> to add the filter to.</param>
            <param name="category">The category to filter.</param>
            <param name="levelFilter">The filter function to apply.</param>
            <returns>The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder"/> so that additional calls can be chained.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Logging.FilterLoggingBuilderExtensions.AddFilter``1(Microsoft.Extensions.Logging.ILoggingBuilder,System.String,System.Func{Microsoft.Extensions.Logging.LogLevel,System.Boolean})">
            <summary>
            Adds a log filter for the given <see cref="T:Microsoft.Extensions.Logging.ILoggerProvider"/>.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder"/> to add the filter to.</param>
            <param name="category">The category to filter.</param>
            <param name="levelFilter">The filter function to apply.</param>
            <typeparam name="T">The <see cref="T:Microsoft.Extensions.Logging.ILoggerProvider"/> which this filter will be added for.</typeparam>
            <returns>The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder"/> so that additional calls can be chained.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Logging.FilterLoggingBuilderExtensions.AddFilter(Microsoft.Extensions.Logging.LoggerFilterOptions,System.Func{System.String,System.String,Microsoft.Extensions.Logging.LogLevel,System.Boolean})">
            <summary>
            Adds a log filter to the factory.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder"/> to add the filter to.</param>
            <param name="filter">The filter function to apply.</param>
            <returns>The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder"/> so that additional calls can be chained.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Logging.FilterLoggingBuilderExtensions.AddFilter(Microsoft.Extensions.Logging.LoggerFilterOptions,System.Func{System.String,Microsoft.Extensions.Logging.LogLevel,System.Boolean})">
            <summary>
            Adds a log filter to the factory.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.Extensions.Logging.LoggerFilterOptions"/> to add the filter to.</param>
            <param name="categoryLevelFilter">The filter function to apply.</param>
            <returns>The <see cref="T:Microsoft.Extensions.Logging.LoggerFilterOptions"/> so that additional calls can be chained.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Logging.FilterLoggingBuilderExtensions.AddFilter``1(Microsoft.Extensions.Logging.LoggerFilterOptions,System.Func{System.String,Microsoft.Extensions.Logging.LogLevel,System.Boolean})">
            <summary>
            Adds a log filter for the given <see cref="T:Microsoft.Extensions.Logging.ILoggerProvider"/>.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.Extensions.Logging.LoggerFilterOptions"/> to add the filter to.</param>
            <param name="categoryLevelFilter">The filter function to apply.</param>
            <typeparam name="T">The <see cref="T:Microsoft.Extensions.Logging.ILoggerProvider"/> which this filter will be added for.</typeparam>
            <returns>The <see cref="T:Microsoft.Extensions.Logging.LoggerFilterOptions"/> so that additional calls can be chained.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Logging.FilterLoggingBuilderExtensions.AddFilter(Microsoft.Extensions.Logging.LoggerFilterOptions,System.Func{Microsoft.Extensions.Logging.LogLevel,System.Boolean})">
            <summary>
            Adds a log filter to the factory.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.Extensions.Logging.LoggerFilterOptions"/> to add the filter to.</param>
            <param name="levelFilter">The filter function to apply.</param>
            <returns>The <see cref="T:Microsoft.Extensions.Logging.LoggerFilterOptions"/> so that additional calls can be chained.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Logging.FilterLoggingBuilderExtensions.AddFilter``1(Microsoft.Extensions.Logging.LoggerFilterOptions,System.Func{Microsoft.Extensions.Logging.LogLevel,System.Boolean})">
            <summary>
            Adds a log filter for the given <see cref="T:Microsoft.Extensions.Logging.ILoggerProvider"/>.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.Extensions.Logging.LoggerFilterOptions"/> to add the filter to.</param>
            <param name="levelFilter">The filter function to apply.</param>
            <typeparam name="T">The <see cref="T:Microsoft.Extensions.Logging.ILoggerProvider"/> which this filter will be added for.</typeparam>
            <returns>The <see cref="T:Microsoft.Extensions.Logging.LoggerFilterOptions"/> so that additional calls can be chained.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Logging.FilterLoggingBuilderExtensions.AddFilter(Microsoft.Extensions.Logging.LoggerFilterOptions,System.String,Microsoft.Extensions.Logging.LogLevel)">
            <summary>
            Adds a log filter to the factory.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.Extensions.Logging.LoggerFilterOptions"/> to add the filter to.</param>
            <param name="category">The category to filter.</param>
            <param name="level">The level to filter.</param>
            <returns>The <see cref="T:Microsoft.Extensions.Logging.LoggerFilterOptions"/> so that additional calls can be chained.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Logging.FilterLoggingBuilderExtensions.AddFilter``1(Microsoft.Extensions.Logging.LoggerFilterOptions,System.String,Microsoft.Extensions.Logging.LogLevel)">
            <summary>
            Adds a log filter for the given <see cref="T:Microsoft.Extensions.Logging.ILoggerProvider"/>.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.Extensions.Logging.LoggerFilterOptions"/> to add the filter to.</param>
            <param name="category">The category to filter.</param>
            <param name="level">The level to filter.</param>
            <typeparam name="T">The <see cref="T:Microsoft.Extensions.Logging.ILoggerProvider"/> which this filter will be added for.</typeparam>
            <returns>The <see cref="T:Microsoft.Extensions.Logging.LoggerFilterOptions"/> so that additional calls can be chained.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Logging.FilterLoggingBuilderExtensions.AddFilter(Microsoft.Extensions.Logging.LoggerFilterOptions,System.String,System.Func{Microsoft.Extensions.Logging.LogLevel,System.Boolean})">
            <summary>
            Adds a log filter to the factory.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.Extensions.Logging.LoggerFilterOptions"/> to add the filter to.</param>
            <param name="category">The category to filter.</param>
            <param name="levelFilter">The filter function to apply.</param>
            <returns>The <see cref="T:Microsoft.Extensions.Logging.LoggerFilterOptions"/> so that additional calls can be chained.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Logging.FilterLoggingBuilderExtensions.AddFilter``1(Microsoft.Extensions.Logging.LoggerFilterOptions,System.String,System.Func{Microsoft.Extensions.Logging.LogLevel,System.Boolean})">
            <summary>
            Adds a log filter for the given <see cref="T:Microsoft.Extensions.Logging.ILoggerProvider"/>.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder"/> to add the filter to.</param>
            <param name="category">The category to filter.</param>
            <param name="levelFilter">The filter function to apply.</param>
            <typeparam name="T">The <see cref="T:Microsoft.Extensions.Logging.ILoggerProvider"/> which this filter will be added for.</typeparam>
            <returns>The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder"/> so that additional calls can be chained.</returns>
        </member>
        <member name="T:Microsoft.Extensions.Logging.LoggerFactory">
            <summary>
            Produces instances of <see cref="T:Microsoft.Extensions.Logging.ILogger"/> classes based on the given providers.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Logging.LoggerFactory.#ctor">
            <summary>
            Creates a new <see cref="T:Microsoft.Extensions.Logging.LoggerFactory"/> instance.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Logging.LoggerFactory.#ctor(System.Collections.Generic.IEnumerable{Microsoft.Extensions.Logging.ILoggerProvider})">
            <summary>
            Creates a new <see cref="T:Microsoft.Extensions.Logging.LoggerFactory"/> instance.
            </summary>
            <param name="providers">The providers to use in producing <see cref="T:Microsoft.Extensions.Logging.ILogger"/> instances.</param>
        </member>
        <member name="M:Microsoft.Extensions.Logging.LoggerFactory.#ctor(System.Collections.Generic.IEnumerable{Microsoft.Extensions.Logging.ILoggerProvider},Microsoft.Extensions.Logging.LoggerFilterOptions)">
            <summary>
            Creates a new <see cref="T:Microsoft.Extensions.Logging.LoggerFactory"/> instance.
            </summary>
            <param name="providers">The providers to use in producing <see cref="T:Microsoft.Extensions.Logging.ILogger"/> instances.</param>
            <param name="filterOptions">The filter options to use.</param>
        </member>
        <member name="M:Microsoft.Extensions.Logging.LoggerFactory.#ctor(System.Collections.Generic.IEnumerable{Microsoft.Extensions.Logging.ILoggerProvider},Microsoft.Extensions.Options.IOptionsMonitor{Microsoft.Extensions.Logging.LoggerFilterOptions})">
            <summary>
            Creates a new <see cref="T:Microsoft.Extensions.Logging.LoggerFactory"/> instance.
            </summary>
            <param name="providers">The providers to use in producing <see cref="T:Microsoft.Extensions.Logging.ILogger"/> instances.</param>
            <param name="filterOption">The filter option to use.</param>
        </member>
        <member name="M:Microsoft.Extensions.Logging.LoggerFactory.#ctor(System.Collections.Generic.IEnumerable{Microsoft.Extensions.Logging.ILoggerProvider},Microsoft.Extensions.Options.IOptionsMonitor{Microsoft.Extensions.Logging.LoggerFilterOptions},Microsoft.Extensions.Options.IOptions{Microsoft.Extensions.Logging.LoggerFactoryOptions})">
            <summary>
            Creates a new <see cref="T:Microsoft.Extensions.Logging.LoggerFactory"/> instance.
            </summary>
            <param name="providers">The providers to use in producing <see cref="T:Microsoft.Extensions.Logging.ILogger"/> instances.</param>
            <param name="filterOption">The filter option to use.</param>
            <param name="options">The <see cref="T:Microsoft.Extensions.Logging.LoggerFactoryOptions"/>.</param>
        </member>
        <member name="M:Microsoft.Extensions.Logging.LoggerFactory.#ctor(System.Collections.Generic.IEnumerable{Microsoft.Extensions.Logging.ILoggerProvider},Microsoft.Extensions.Options.IOptionsMonitor{Microsoft.Extensions.Logging.LoggerFilterOptions},Microsoft.Extensions.Options.IOptions{Microsoft.Extensions.Logging.LoggerFactoryOptions},Microsoft.Extensions.Logging.IExternalScopeProvider)">
            <summary>
            Creates a new <see cref="T:Microsoft.Extensions.Logging.LoggerFactory"/> instance.
            </summary>
            <param name="providers">The providers to use in producing <see cref="T:Microsoft.Extensions.Logging.ILogger"/> instances.</param>
            <param name="filterOption">The filter option to use.</param>
            <param name="options">The <see cref="T:Microsoft.Extensions.Logging.LoggerFactoryOptions"/>.</param>
            <param name="scopeProvider">The <see cref="T:Microsoft.Extensions.Logging.IExternalScopeProvider"/>.</param>
        </member>
        <member name="M:Microsoft.Extensions.Logging.LoggerFactory.Create(System.Action{Microsoft.Extensions.Logging.ILoggingBuilder})">
            <summary>
            Creates new instance of <see cref="T:Microsoft.Extensions.Logging.ILoggerFactory"/> configured using provided <paramref name="configure"/> delegate.
            </summary>
            <param name="configure">A delegate to configure the <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder"/>.</param>
            <returns>The <see cref="T:Microsoft.Extensions.Logging.ILoggerFactory"/> that was created.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Logging.LoggerFactory.CreateLogger(System.String)">
            <summary>
            Creates an <see cref="T:Microsoft.Extensions.Logging.ILogger"/> with the given <paramref name="categoryName"/>.
            </summary>
            <param name="categoryName">The category name for messages produced by the logger.</param>
            <returns>The <see cref="T:Microsoft.Extensions.Logging.ILogger"/> that was created.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Logging.LoggerFactory.AddProvider(Microsoft.Extensions.Logging.ILoggerProvider)">
            <summary>
            Adds the given provider to those used in creating <see cref="T:Microsoft.Extensions.Logging.ILogger"/> instances.
            </summary>
            <param name="provider">The <see cref="T:Microsoft.Extensions.Logging.ILoggerProvider"/> to add.</param>
        </member>
        <member name="M:Microsoft.Extensions.Logging.LoggerFactory.CheckDisposed">
            <summary>
            Check if the factory has been disposed.
            </summary>
            <returns>True when <see cref="M:Microsoft.Extensions.Logging.LoggerFactory.Dispose"/> as been called</returns>
        </member>
        <member name="M:Microsoft.Extensions.Logging.LoggerFactory.Dispose">
            <inheritdoc/>
        </member>
        <member name="T:Microsoft.Extensions.Logging.LoggerFactoryOptions">
            <summary>
            The options for a LoggerFactory.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Logging.LoggerFactoryOptions.#ctor">
            <summary>
            Creates a new <see cref="T:Microsoft.Extensions.Logging.LoggerFactoryOptions"/> instance.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Logging.LoggerFactoryOptions.ActivityTrackingOptions">
            <summary>
            Gets or sets <see cref="T:Microsoft.Extensions.Logging.LoggerFactoryOptions"/> value to indicate which parts of the tracing context information should be included with the logging scopes.
            </summary>
        </member>
        <member name="T:Microsoft.Extensions.Logging.LoggerFactoryScopeProvider">
            <summary>
            Default implementation of <see cref="T:Microsoft.Extensions.Logging.IExternalScopeProvider"/>
            </summary>
        </member>
        <member name="T:Microsoft.Extensions.Logging.LoggerFilterOptions">
            <summary>
            The options for a LoggerFilter.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Logging.LoggerFilterOptions.#ctor">
            <summary>
            Creates a new <see cref="T:Microsoft.Extensions.Logging.LoggerFilterOptions"/> instance.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Logging.LoggerFilterOptions.CaptureScopes">
            <summary>
            Gets or sets value indicating whether logging scopes are being captured. Defaults to <c>true</c>
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Logging.LoggerFilterOptions.MinLevel">
            <summary>
            Gets or sets the minimum level of log messages if none of the rules match.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Logging.LoggerFilterOptions.Rules">
            <summary>
            Gets the collection of <see cref="T:Microsoft.Extensions.Logging.LoggerFilterRule"/> used for filtering log messages.
            </summary>
        </member>
        <member name="T:Microsoft.Extensions.Logging.LoggerFilterRule">
            <summary>
            Defines a rule used to filter log messages
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Logging.LoggerFilterRule.#ctor(System.String,System.String,System.Nullable{Microsoft.Extensions.Logging.LogLevel},System.Func{System.String,System.String,Microsoft.Extensions.Logging.LogLevel,System.Boolean})">
            <summary>
            Creates a new <see cref="T:Microsoft.Extensions.Logging.LoggerFilterRule"/> instance.
            </summary>
            <param name="providerName">The provider name to use in this filter rule.</param>
            <param name="categoryName">The category name to use in this filter rule.</param>
            <param name="logLevel">The <see cref="P:Microsoft.Extensions.Logging.LoggerFilterRule.LogLevel"/> to use in this filter rule.</param>
            <param name="filter">The filter to apply.</param>
        </member>
        <member name="P:Microsoft.Extensions.Logging.LoggerFilterRule.ProviderName">
            <summary>
            Gets the logger provider type or alias this rule applies to.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Logging.LoggerFilterRule.CategoryName">
            <summary>
            Gets the logger category this rule applies to.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Logging.LoggerFilterRule.LogLevel">
            <summary>
            Gets the minimum <see cref="P:Microsoft.Extensions.Logging.LoggerFilterRule.LogLevel"/> of messages.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Logging.LoggerFilterRule.Filter">
            <summary>
            Gets the filter delegate that would be applied to messages that passed the <see cref="P:Microsoft.Extensions.Logging.LoggerFilterRule.LogLevel"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Logging.LoggerFilterRule.ToString">
            <inheritdoc/>
        </member>
        <member name="T:Microsoft.Extensions.Logging.LoggingBuilderExtensions">
            <summary>
            Extension methods for setting up logging services in an <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder" />.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Logging.LoggingBuilderExtensions.SetMinimumLevel(Microsoft.Extensions.Logging.ILoggingBuilder,Microsoft.Extensions.Logging.LogLevel)">
            <summary>
            Sets a minimum <see cref="T:Microsoft.Extensions.Logging.LogLevel"/> requirement for log messages to be logged.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder"/> to set the minimum level on.</param>
            <param name="level">The <see cref="T:Microsoft.Extensions.Logging.LogLevel"/> to set as the minimum.</param>
            <returns>The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder"/> so that additional calls can be chained.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Logging.LoggingBuilderExtensions.AddProvider(Microsoft.Extensions.Logging.ILoggingBuilder,Microsoft.Extensions.Logging.ILoggerProvider)">
            <summary>
            Adds the given <see cref="T:Microsoft.Extensions.Logging.ILoggerProvider"/> to the <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder"/>
            </summary>
            <param name="builder">The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder"/> to add the <paramref name="provider"/> to.</param>
            <param name="provider">The <see cref="T:Microsoft.Extensions.Logging.ILoggerProvider"/> to add to the <paramref name="builder"/>.</param>
            <returns>The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder"/> so that additional calls can be chained.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Logging.LoggingBuilderExtensions.ClearProviders(Microsoft.Extensions.Logging.ILoggingBuilder)">
            <summary>
            Removes all <see cref="T:Microsoft.Extensions.Logging.ILoggerProvider"/>s from <paramref name="builder"/>.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder"/> to remove <see cref="T:Microsoft.Extensions.Logging.ILoggerProvider"/>s from.</param>
            <returns>The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder"/> so that additional calls can be chained.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Logging.LoggingBuilderExtensions.Configure(Microsoft.Extensions.Logging.ILoggingBuilder,System.Action{Microsoft.Extensions.Logging.LoggerFactoryOptions})">
            <summary>
            Configure the <paramref name="builder"/> with the <see cref="T:Microsoft.Extensions.Logging.LoggerFactoryOptions"/>.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder"/> to be configured with <see cref="T:Microsoft.Extensions.Logging.LoggerFactoryOptions"/></param>
            <param name="action">The action used to configure the logger factory</param>
            <returns>The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder"/> so that additional calls can be chained.</returns>
        </member>
        <member name="T:Microsoft.Extensions.Logging.ProviderAliasAttribute">
            <summary>
            Defines alias for <see cref="T:Microsoft.Extensions.Logging.ILoggerProvider"/> implementation to be used in filtering rules.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Logging.ProviderAliasAttribute.#ctor(System.String)">
            <summary>
            Creates a new <see cref="T:Microsoft.Extensions.Logging.ProviderAliasAttribute"/> instance.
            </summary>
            <param name="alias">The alias to set.</param>
        </member>
        <member name="P:Microsoft.Extensions.Logging.ProviderAliasAttribute.Alias">
            <summary>
            The alias of the provider.
            </summary>
        </member>
        <member name="T:Microsoft.Extensions.Logging.NullExternalScopeProvider">
            <summary>
            Scope provider that does nothing.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Logging.NullExternalScopeProvider.Instance">
            <summary>
            Returns a cached instance of <see cref="T:Microsoft.Extensions.Logging.NullExternalScopeProvider"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Logging.NullExternalScopeProvider.Microsoft#Extensions#Logging#IExternalScopeProvider#ForEachScope``1(System.Action{System.Object,``0},``0)">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.Extensions.Logging.NullExternalScopeProvider.Microsoft#Extensions#Logging#IExternalScopeProvider#Push(System.Object)">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.Extensions.Logging.NullScope">
            <summary>
            An empty scope without any logic
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Logging.NullScope.Dispose">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.Extensions.DependencyInjection.LoggingServiceCollectionExtensions">
            <summary>
            Extension methods for setting up logging services in an <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" />.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.LoggingServiceCollectionExtensions.AddLogging(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            Adds logging services to the specified <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" />.
            </summary>
            <param name="services">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" /> to add services to.</param>
            <returns>The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/> so that additional calls can be chained.</returns>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.LoggingServiceCollectionExtensions.AddLogging(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.Action{Microsoft.Extensions.Logging.ILoggingBuilder})">
            <summary>
            Adds logging services to the specified <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" />.
            </summary>
            <param name="services">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" /> to add services to.</param>
            <param name="configure">The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder"/> configuration delegate.</param>
            <returns>The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/> so that additional calls can be chained.</returns>
        </member>
        <member name="M:System.ThrowHelper.ThrowIfNull(System.Object,System.String)">
            <summary>Throws an <see cref="T:System.ArgumentNullException"/> if <paramref name="argument"/> is null.</summary>
            <param name="argument">The reference type argument to validate as non-null.</param>
            <param name="paramName">The name of the parameter with which <paramref name="argument"/> corresponds.</param>
        </member>
        <member name="M:System.ThrowHelper.IfNullOrWhitespace(System.String,System.String)">
            <summary>
            Throws either an <see cref="T:System.ArgumentNullException"/> or an <see cref="T:System.ArgumentException"/>
            if the specified string is <see langword="null"/> or whitespace respectively.
            </summary>
            <param name="argument">String to be checked for <see langword="null"/> or whitespace.</param>
            <param name="paramName">The name of the parameter being checked.</param>
            <returns>The original value of <paramref name="argument"/>.</returns>
        </member>
        <member name="T:System.Runtime.InteropServices.LibraryImportAttribute">
            <summary>
            Attribute used to indicate a source generator should create a function for marshalling
            arguments instead of relying on the runtime to generate an equivalent marshalling function at run-time.
            </summary>
            <remarks>
            This attribute is meaningless if the source generator associated with it is not enabled.
            The current built-in source generator only supports C# and only supplies an implementation when
            applied to static, partial, non-generic methods.
            </remarks>
        </member>
        <member name="M:System.Runtime.InteropServices.LibraryImportAttribute.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Runtime.InteropServices.LibraryImportAttribute"/>.
            </summary>
            <param name="libraryName">Name of the library containing the import.</param>
        </member>
        <member name="P:System.Runtime.InteropServices.LibraryImportAttribute.LibraryName">
            <summary>
            Gets the name of the library containing the import.
            </summary>
        </member>
        <member name="P:System.Runtime.InteropServices.LibraryImportAttribute.EntryPoint">
            <summary>
            Gets or sets the name of the entry point to be called.
            </summary>
        </member>
        <member name="P:System.Runtime.InteropServices.LibraryImportAttribute.StringMarshalling">
            <summary>
            Gets or sets how to marshal string arguments to the method.
            </summary>
            <remarks>
            If this field is set to a value other than <see cref="F:System.Runtime.InteropServices.StringMarshalling.Custom" />,
            <see cref="P:System.Runtime.InteropServices.LibraryImportAttribute.StringMarshallingCustomType" /> must not be specified.
            </remarks>
        </member>
        <member name="P:System.Runtime.InteropServices.LibraryImportAttribute.StringMarshallingCustomType">
            <summary>
            Gets or sets the <see cref="T:System.Type"/> used to control how string arguments to the method are marshalled.
            </summary>
            <remarks>
            If this field is specified, <see cref="P:System.Runtime.InteropServices.LibraryImportAttribute.StringMarshalling" /> must not be specified
            or must be set to <see cref="F:System.Runtime.InteropServices.StringMarshalling.Custom" />.
            </remarks>
        </member>
        <member name="P:System.Runtime.InteropServices.LibraryImportAttribute.SetLastError">
            <summary>
            Gets or sets whether the callee sets an error (SetLastError on Windows or errno
            on other platforms) before returning from the attributed method.
            </summary>
        </member>
        <member name="T:System.Runtime.InteropServices.StringMarshalling">
            <summary>
            Specifies how strings should be marshalled for generated p/invokes
            </summary>
        </member>
        <member name="F:System.Runtime.InteropServices.StringMarshalling.Custom">
            <summary>
            Indicates the user is suppling a specific marshaller in <see cref="P:System.Runtime.InteropServices.LibraryImportAttribute.StringMarshallingCustomType"/>.
            </summary>
        </member>
        <member name="F:System.Runtime.InteropServices.StringMarshalling.Utf8">
            <summary>
            Use the platform-provided UTF-8 marshaller.
            </summary>
        </member>
        <member name="F:System.Runtime.InteropServices.StringMarshalling.Utf16">
            <summary>
            Use the platform-provided UTF-16 marshaller.
            </summary>
        </member>
        <member name="P:System.SR.InvalidActivityTrackingOptions">
            <summary>{0} is invalid ActivityTrackingOptions value.</summary>
        </member>
        <member name="P:System.SR.MoreThanOneWildcard">
            <summary>Only one wildcard character is allowed in category name.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.AllowNullAttribute">
            <summary>Specifies that null is allowed as an input even if the corresponding type disallows it.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.DisallowNullAttribute">
            <summary>Specifies that null is disallowed as an input even if the corresponding type allows it.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.MaybeNullAttribute">
            <summary>Specifies that an output may be null even if the corresponding type disallows it.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.NotNullAttribute">
            <summary>Specifies that an output will not be null even if the corresponding type allows it. Specifies that an input argument was not null when the call returns.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.MaybeNullWhenAttribute">
            <summary>Specifies that when a method returns <see cref="P:System.Diagnostics.CodeAnalysis.MaybeNullWhenAttribute.ReturnValue"/>, the parameter may be null even if the corresponding type disallows it.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MaybeNullWhenAttribute.#ctor(System.Boolean)">
            <summary>Initializes the attribute with the specified return value condition.</summary>
            <param name="returnValue">
            The return value condition. If the method returns this value, the associated parameter may be null.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.MaybeNullWhenAttribute.ReturnValue">
            <summary>Gets the return value condition.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.NotNullWhenAttribute">
            <summary>Specifies that when a method returns <see cref="P:System.Diagnostics.CodeAnalysis.NotNullWhenAttribute.ReturnValue"/>, the parameter will not be null even if the corresponding type allows it.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.NotNullWhenAttribute.#ctor(System.Boolean)">
            <summary>Initializes the attribute with the specified return value condition.</summary>
            <param name="returnValue">
            The return value condition. If the method returns this value, the associated parameter will not be null.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.NotNullWhenAttribute.ReturnValue">
            <summary>Gets the return value condition.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.NotNullIfNotNullAttribute">
            <summary>Specifies that the output will be non-null if the named parameter is non-null.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.NotNullIfNotNullAttribute.#ctor(System.String)">
            <summary>Initializes the attribute with the associated parameter name.</summary>
            <param name="parameterName">
            The associated parameter name.  The output will be non-null if the argument to the parameter specified is non-null.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.NotNullIfNotNullAttribute.ParameterName">
            <summary>Gets the associated parameter name.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.DoesNotReturnAttribute">
            <summary>Applied to a method that will never return under any circumstance.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.DoesNotReturnIfAttribute">
            <summary>Specifies that the method will not return if the associated Boolean parameter is passed the specified value.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.DoesNotReturnIfAttribute.#ctor(System.Boolean)">
            <summary>Initializes the attribute with the specified parameter value.</summary>
            <param name="parameterValue">
            The condition parameter value. Code after the method will be considered unreachable by diagnostics if the argument to
            the associated parameter matches this value.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.DoesNotReturnIfAttribute.ParameterValue">
            <summary>Gets the condition parameter value.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.MemberNotNullAttribute">
            <summary>Specifies that the method or property will ensure that the listed field and property members have not-null values.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MemberNotNullAttribute.#ctor(System.String)">
            <summary>Initializes the attribute with a field or property member.</summary>
            <param name="member">
            The field or property member that is promised to be not-null.
            </param>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MemberNotNullAttribute.#ctor(System.String[])">
            <summary>Initializes the attribute with the list of field and property members.</summary>
            <param name="members">
            The list of field and property members that are promised to be not-null.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.MemberNotNullAttribute.Members">
            <summary>Gets field or property member names.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.MemberNotNullWhenAttribute">
            <summary>Specifies that the method or property will ensure that the listed field and property members have not-null values when returning with the specified return value condition.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MemberNotNullWhenAttribute.#ctor(System.Boolean,System.String)">
            <summary>Initializes the attribute with the specified return value condition and a field or property member.</summary>
            <param name="returnValue">
            The return value condition. If the method returns this value, the associated parameter will not be null.
            </param>
            <param name="member">
            The field or property member that is promised to be not-null.
            </param>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MemberNotNullWhenAttribute.#ctor(System.Boolean,System.String[])">
            <summary>Initializes the attribute with the specified return value condition and list of field and property members.</summary>
            <param name="returnValue">
            The return value condition. If the method returns this value, the associated parameter will not be null.
            </param>
            <param name="members">
            The list of field and property members that are promised to be not-null.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.MemberNotNullWhenAttribute.ReturnValue">
            <summary>Gets the return value condition.</summary>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.MemberNotNullWhenAttribute.Members">
            <summary>Gets field or property member names.</summary>
        </member>
    </members>
</doc>
