using System.Collections.Generic;
using System.Threading.Tasks;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Application.Common;

namespace OrderFlowCore.Application.Interfaces.Services
{
    public interface IEmployeeService
    {
        Task<ServiceResult<EmployeeDto>> GetByCivilNumberAsync(string civilNumber);
        Task<ServiceResult<IEnumerable<EmployeeDto>>> GetAllAsync();
        Task<ServiceResult<EmployeeDto>> GetByIdAsync(int id);
        Task<ServiceResult> CreateAsync(EmployeeDto dto);
        Task<ServiceResult> UpdateAsync(EmployeeDto dto);
        Task<ServiceResult> DeleteAsync(int id);
    }
} 