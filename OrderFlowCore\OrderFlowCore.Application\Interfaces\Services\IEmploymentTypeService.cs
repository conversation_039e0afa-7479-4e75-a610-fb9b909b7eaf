using System.Collections.Generic;
using System.Threading.Tasks;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Application.Common;

namespace OrderFlowCore.Application.Interfaces.Services;

public interface IEmploymentTypeService
{
    Task<ServiceResult<IEnumerable<EmploymentTypeDto>>> GetAllAsync();
    Task<ServiceResult<EmploymentTypeDto>> GetByIdAsync(int id);
    Task<ServiceResult> CreateAsync(EmploymentTypeDto dto);
    Task<ServiceResult> UpdateAsync(EmploymentTypeDto dto);
    Task<ServiceResult> DeleteAsync(int id);
}
