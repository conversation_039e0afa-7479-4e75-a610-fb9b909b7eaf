using Microsoft.EntityFrameworkCore;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Application.Interfaces.Repositories;
using OrderFlowCore.Core.Models;
using OrderFlowCore.Infrastructure.Data;
using System.Linq;
using System.Threading.Tasks;

namespace OrderFlowCore.Infrastructure.Repositories
{
    public class AutoRouteRepository : IAutoRouteRepository
    {
        private readonly ApplicationDbContext _context;

        public AutoRouteRepository(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<AutoRouting> GetAutoRouteAsync(string orderType, string nationality, string job)
        {
            try
            {
                var autoRoute = await _context.AutoRoutings
                    .Where(ar => ar.OrderType == orderType 
                              && ar.Nationality == nationality 
                              && ar.Status == true
                              && (ar.Job == "ALL" 
                                  || ar.Job == job
                                  || (ar.Job == "ALL_EXCEPT_OTHER" && !job.StartsWith("أخرى - Other:"))))
                    .FirstOrDefaultAsync();

                return autoRoute;
            }
            catch
            {
                return null;
            }
        }

        public async Task<AutoRouteDto> GetByIdAsync(int id)
        {
            try
            {
                var autoRoute = await _context.AutoRoutings
                    .FirstOrDefaultAsync(ar => ar.Id == id);

                return autoRoute != null ? MapToDto(autoRoute) : null;
            }
            catch
            {
                return null;
            }
        }

        public async Task<bool> CreateAsync(AutoRouteDto dto)
        {
            try
            {
                var autoRoute = MapToEntity(dto);
                autoRoute.CreatedAt = DateTime.Now;
                
                _context.AutoRoutings.Add(autoRoute);
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> UpdateAsync(AutoRouteDto dto)
        {
            try
            {
                var autoRoute = await _context.AutoRoutings.FindAsync(dto.Id);
                if (autoRoute == null) return false;

                autoRoute.OrderType = dto.RequestType;
                autoRoute.Nationality = dto.Nationality;
                autoRoute.Job = dto.Job;
                autoRoute.Supervisors = string.Join(";", dto.Supervisors);
                autoRoute.Status = dto.Status;
                autoRoute.Notes = dto.Notes;
                autoRoute.ModifiedAt = DateTime.Now;

                _context.AutoRoutings.Update(autoRoute);
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> DeleteAsync(int id)
        {
            try
            {
                var autoRoute = await _context.AutoRoutings.FindAsync(id);
                if (autoRoute == null) return false;

                _context.AutoRoutings.Remove(autoRoute);
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> ExistsAsync(string orderType, string nationality, string job, int? excludeId = null)
        {
            try
            {
                var query = _context.AutoRoutings
                    .Where(ar => ar.OrderType == orderType 
                              && ar.Nationality == nationality 
                              && ar.Status == true);

                if (excludeId.HasValue)
                {
                    query = query.Where(ar => ar.Id != excludeId.Value);
                }

                // Check for conflicts with job matching logic
                var exists = await query
                    .Where(ar => ar.Job == "ALL" 
                              || ar.Job == job
                              || (ar.Job == "ALL_EXCEPT_OTHER" && !job.StartsWith("أخرى - Other:"))
                              || (job == "ALL" && ar.Job != "ALL")
                              || (job == "ALL_EXCEPT_OTHER" && ar.Job != "ALL" && ar.Job != "ALL_EXCEPT_OTHER"))
                    .AnyAsync();

                return exists;
            }
            catch
            {
                return false;
            }
        }

        private AutoRouteDto MapToDto(AutoRouting entity)
        {
            return new AutoRouteDto
            {
                Id = entity.Id,
                RequestType = entity.OrderType,
                Nationality = entity.Nationality,
                Job = entity.Job,
                Supervisors = entity.Supervisors?.Split(';', StringSplitOptions.RemoveEmptyEntries).ToList() ?? new List<string>(),
                Status = entity.Status,
                Notes = entity.Notes,
                CreatedAt = entity.CreatedAt ?? DateTime.Now,
                CreatedBy = entity.CreatedBy
            };
        }

        private AutoRouting MapToEntity(AutoRouteDto dto)
        {
            return new AutoRouting
            {
                Id = dto.Id,
                OrderType = dto.RequestType,
                Nationality = dto.Nationality,
                Job = dto.Job,
                Supervisors = string.Join(";", dto.Supervisors),
                Status = dto.Status,
                Notes = dto.Notes,
                CreatedBy = dto.CreatedBy
            };
        }
    }
}
