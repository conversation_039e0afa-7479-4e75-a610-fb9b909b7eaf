<?xml version="1.0" encoding="utf-8"?>
<!--
  For more information on how to configure your ASP.NET application, please visit
  https://go.microsoft.com/fwlink/?LinkId=169433
  -->
<configuration>
	<appSettings>
  <add key="AutoDeleteAttachments" value="True" />
 </appSettings>
	<connectionStrings>
		<add name="MyConnectionString" connectionString="Data Source=.\SQLEXPRESS01;Initial Catalog=aboZyad;Integrated Security=True" providerName="System.Data.SqlClient" />
	</connectionStrings>
	<system.web>
		<compilation debug="true" targetFramework="4.8" />
		<httpRuntime targetFramework="4.8"
      maxRequestLength="102400"
      executionTimeout="3600" />
		<pages>
			<namespaces>
				<add namespace="System.Web.Optimization" />
			</namespaces>
			<controls>
				<add assembly="Microsoft.AspNet.Web.Optimization.WebForms" namespace="Microsoft.AspNet.Web.Optimization.WebForms" tagPrefix="webopt" />
			</controls>
		</pages>
	</system.web>
	<runtime>
	<assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
	  <dependentAssembly>
		<assemblyIdentity name="Antlr3.Runtime" publicKeyToken="eb42632606e9261f" />
		<bindingRedirect oldVersion="0.0.0.0-3.5.0.2" newVersion="3.5.0.2" />
	  </dependentAssembly>
	  <dependentAssembly>
		<assemblyIdentity name="Microsoft.Web.Infrastructure" publicKeyToken="31bf3856ad364e35" />
		<bindingRedirect oldVersion="0.0.0.0-2.0.1.0" newVersion="2.0.1.0" />
	  </dependentAssembly>
	  <dependentAssembly>
		<assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" />
		<bindingRedirect oldVersion="0.0.0.0-13.0.0.0" newVersion="13.0.0.0" />
	  </dependentAssembly>
	  <dependentAssembly>
		<assemblyIdentity name="WebGrease" publicKeyToken="31bf3856ad364e35" />
		<bindingRedirect oldVersion="0.0.0.0-1.6.5135.21930" newVersion="1.6.5135.21930" />
	  </dependentAssembly>
	  <dependentAssembly>
		<assemblyIdentity name="System.Runtime.CompilerServices.Unsafe" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
		<bindingRedirect oldVersion="0.0.0.0-6.0.0.0" newVersion="6.0.0.0" />
	  </dependentAssembly>
	  <dependentAssembly>
		<assemblyIdentity name="System.Memory" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
		<bindingRedirect oldVersion="0.0.0.0-4.0.1.2" newVersion="4.0.1.2" />
	  </dependentAssembly>
	  <dependentAssembly>
		<assemblyIdentity name="System.Buffers" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
		<bindingRedirect oldVersion="0.0.0.0-4.0.3.0" newVersion="4.0.3.0" />
	  </dependentAssembly>
	  <dependentAssembly>
		<assemblyIdentity name="Microsoft.Extensions.Primitives" publicKeyToken="adb9793829ddae60" culture="neutral" />
		<bindingRedirect oldVersion="0.0.0.0-8.0.0.0" newVersion="8.0.0.0" />
	  </dependentAssembly>
	  <dependentAssembly>
		<assemblyIdentity name="Microsoft.Bcl.AsyncInterfaces" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
		<bindingRedirect oldVersion="0.0.0.0-8.0.0.0" newVersion="8.0.0.0" />
		  <dependentAssembly>
			  <assemblyIdentity name="Microsoft.Extensions.Configuration.FileExtensions" publicKeyToken="adb9793829ddae60" culture="neutral" />
			  <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
		  </dependentAssembly>
		  <dependentAssembly>
			  <assemblyIdentity name="Microsoft.Extensions.DependencyInjection.Abstractions" publicKeyToken="adb9793829ddae60" culture="neutral" />
			  <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
		  </dependentAssembly>
		  <dependentAssembly>
			  <assemblyIdentity name="Microsoft.Extensions.Logging" publicKeyToken="adb9793829ddae60" culture="neutral" />
			  <bindingRedirect oldVersion="0.0.0.0-8.0.0.0" newVersion="8.0.0.0" />
		  </dependentAssembly>
		  <dependentAssembly>
			  <assemblyIdentity name="Microsoft.Extensions.Logging.Abstractions" publicKeyToken="adb9793829ddae60" culture="neutral" />
			  <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
		  </dependentAssembly>
		  <dependentAssembly>
			  <assemblyIdentity name="Microsoft.Extensions.Options" publicKeyToken="adb9793829ddae60" culture="neutral" />
			  <bindingRedirect oldVersion="0.0.0.0-8.0.0.2" newVersion="8.0.0.2" />
		  </dependentAssembly>
		  <dependentAssembly>
			  <assemblyIdentity name="System.Diagnostics.DiagnosticSource" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
			  <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
		  </dependentAssembly>
		  <dependentAssembly>
			  <assemblyIdentity name="System.Text.Json" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
			  <bindingRedirect oldVersion="0.0.0.0-8.0.0.4" newVersion="8.0.0.4" />
		  </dependentAssembly>
	  </dependentAssembly>
		<dependentAssembly>
			<assemblyIdentity name="Microsoft.Extensions.Logging.Abstractions" publicKeyToken="adb9793829ddae60" culture="neutral" />
			<bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
		</dependentAssembly>
		<dependentAssembly>
			<assemblyIdentity name="Microsoft.Extensions.Logging" publicKeyToken="adb9793829ddae60" culture="neutral" />
			<bindingRedirect oldVersion="0.0.0.0-8.0.0.0" newVersion="8.0.0.0" />
		</dependentAssembly>
		<dependentAssembly>
			<assemblyIdentity name="System.Text.Json" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
			<bindingRedirect oldVersion="0.0.0.0-8.0.0.4" newVersion="8.0.0.4" />
		</dependentAssembly>
		<dependentAssembly>
			<assemblyIdentity name="Microsoft.Extensions.Configuration.FileExtensions" publicKeyToken="adb9793829ddae60" culture="neutral" />
			<bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
		</dependentAssembly>
		<dependentAssembly>
			<assemblyIdentity name="Microsoft.Extensions.DependencyInjection.Abstractions" publicKeyToken="adb9793829ddae60" culture="neutral" />
			<bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
		</dependentAssembly>
		<dependentAssembly>
			<assemblyIdentity name="Microsoft.Extensions.Options" publicKeyToken="adb9793829ddae60" culture="neutral" />
			<bindingRedirect oldVersion="0.0.0.0-8.0.0.2" newVersion="8.0.0.2" />
		</dependentAssembly>
		<dependentAssembly>
			<assemblyIdentity name="System.Diagnostics.DiagnosticSource" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
			<bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
		</dependentAssembly>
	</assemblyBinding>
  </runtime>
  <system.codedom>
	<compilers>
	  <compiler language="c#;cs;csharp" extension=".cs" warningLevel="4" compilerOptions="/langversion:default /nowarn:1659;1699;1701;612;618" type="Microsoft.CodeDom.Providers.DotNetCompilerPlatform.CSharpCodeProvider, Microsoft.CodeDom.Providers.DotNetCompilerPlatform, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" />
	  <compiler language="vb;vbs;visualbasic;vbscript" extension=".vb" warningLevel="4" compilerOptions="/langversion:default /nowarn:41008,40000,40008 /define:_MYTYPE=\&quot;Web\&quot; /optionInfer+" type="Microsoft.CodeDom.Providers.DotNetCompilerPlatform.VBCodeProvider, Microsoft.CodeDom.Providers.DotNetCompilerPlatform, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" />
	</compilers>
  </system.codedom>
</configuration>