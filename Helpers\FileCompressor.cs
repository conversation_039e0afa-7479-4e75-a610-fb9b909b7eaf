﻿using System;
using System.IO;
using SevenZip;

namespace abozyad.Helpers
{
    public class FileCompressor
    {
        private static bool _isInitialized = false;

        private static void InitializeSevenZip()
        {
            if (!_isInitialized)
            {
                try
                {
                    string dllPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "7z.dll");
                    if (!File.Exists(dllPath))
                    {
                        throw new FileNotFoundException($"لم يتم العثور على ملف 7z.dll في المسار: {dllPath}");
                    }
                    SevenZipCompressor.SetLibraryPath(dllPath);
                    _isInitialized = true;
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"❌ خطأ في تهيئة SevenZip: {ex.Message}");
                    throw;
                }
            }
        }

        public static byte[] CompressFile(Stream inputStream, string extension)
        {
            if (inputStream == null || inputStream.Length == 0 || extension?.ToLower() != ".pdf")
            {
                System.Diagnostics.Debug.WriteLine("❌ الملف فارغ أو ليس بصيغة PDF");
                return null;
            }

            try
            {
                InitializeSevenZip();

                // قراءة محتوى ملف PDF
                byte[] pdfData;
                using (var ms = new MemoryStream())
                {
                    inputStream.CopyTo(ms);
                    pdfData = ms.ToArray();
                }

                // التأكد من أن الملف هو PDF فعلاً
                if (pdfData.Length < 4 ||
                    pdfData[0] != 0x25 || // %
                    pdfData[1] != 0x50 || // P
                    pdfData[2] != 0x44 || // D
                    pdfData[3] != 0x46)   // F
                {
                    System.Diagnostics.Debug.WriteLine("❌ الملف ليس بصيغة PDF صالحة");
                    return null;
                }

                System.Diagnostics.Debug.WriteLine($"📊 حجم PDF قبل الضغط: {pdfData.Length:N0} bytes");

                // ضغط الملف
                var compressor = new SevenZipCompressor
                {
                    CompressionMethod = CompressionMethod.Lzma2,
                    CompressionLevel = CompressionLevel.Ultra
                };

                using (var outputStream = new MemoryStream())
                {
                    compressor.CompressStream(new MemoryStream(pdfData), outputStream);
                    var compressedData = outputStream.ToArray();

                    System.Diagnostics.Debug.WriteLine($"📊 حجم الملف بعد الضغط: {compressedData.Length:N0} bytes");
                    System.Diagnostics.Debug.WriteLine($"📊 نسبة الضغط: {100 - ((double)compressedData.Length / pdfData.Length * 100):F2}%");

                    return compressedData;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في ضغط الملف: {ex.Message}");
                return null;
            }
        }
        public static byte[] ExtractFile(byte[] compressedData)
        {
            try
            {
                InitializeSevenZip();

                using (var compressedStream = new MemoryStream(compressedData))
                using (var outputStream = new MemoryStream())
                {
                    SevenZipExtractor extractor = new SevenZipExtractor(compressedStream);
                    extractor.ExtractFile(0, outputStream);  // استخراج الملف الأول
                    return outputStream.ToArray();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في فك الضغط: {ex.Message}");
                return null;
            }
        }
    }

}