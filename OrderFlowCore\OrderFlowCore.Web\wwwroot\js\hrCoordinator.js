// HR Coordinator JavaScript Module
const HRCoordinator = {
    // Initialize the module
    init: function() {
        this.bindEvents();
        this.hideLoading();
    },

    // Bind event handlers
    bindEvents: function() {
        // Add enter key support for search
        const searchInput = document.getElementById('txtRestoreSearch');
        if (searchInput) {
            searchInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    HRCoordinator.searchRestorableOrders();
                }
            });
        }

        // Auto-hide alerts after 5 seconds
        const alerts = document.querySelectorAll('.alert');
        alerts.forEach(alert => {
            if (alert.classList.contains('alert-success')) {
                setTimeout(() => {
                    alert.style.display = 'none';
                }, 5000);
            }
        });
    },

    // Validation functions
    validateSelection: function() {
        const ddl = document.getElementById('orderSelect');
        if (!ddl || ddl.value === '') {
            this.showError('الرجاء اختيار رقم الطلب أولاً');
            return false;
        }
        return true;
    },

    validateForm: function() {
        const requiredFields = document.querySelectorAll('[required]');
        let isValid = true;

        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                field.classList.add('border-warning');
                isValid = false;
            } else {
                field.classList.remove('border-warning');
            }
        });

        return isValid;
    },

    // Loading functions
    showLoading: function() {
        const loadingIndicator = document.getElementById('loading');
        if (loadingIndicator) {
            loadingIndicator.style.display = 'flex';
        }
    },

    hideLoading: function() {
        const loadingIndicator = document.getElementById('loading');
        if (loadingIndicator) {
            loadingIndicator.style.display = 'none';
        }
    },

    // Message functions
    showSuccess: function(message) {
        this.hideLoading();
        Swal.fire({
            title: 'تم بنجاح',
            text: message,
            icon: 'success',
            confirmButtonText: 'حسناً'
        });
    },

    showError: function(message) {
        this.hideLoading();
        Swal.fire({
            title: 'خطأ',
            text: message,
            icon: 'error',
            confirmButtonText: 'حسناً'
        });
    },

    // Confirmation dialogs
    confirmSubmit: function() {
        return Swal.fire({
            title: 'تأكيد التحويل',
            text: 'هل أنت متأكد من تحويل الطلب؟',
            icon: 'question',
            showCancelButton: true,
            confirmButtonText: 'نعم، تحويل',
            cancelButtonText: 'إلغاء'
        }).then((result) => {
            if (result.isConfirmed) {
                this.showLoading();
            }
            return result.isConfirmed;
        });
    },

    confirmAutoPath: function() {
        return Swal.fire({
            title: 'تأكيد التوجيه التلقائي',
            text: 'هل أنت متأكد من تطبيق التوجيه التلقائي؟',
            icon: 'question',
            showCancelButton: true,
            confirmButtonText: 'نعم',
            cancelButtonText: 'لا'
        }).then((result) => {
            if (result.isConfirmed) {
                this.showLoading();
            }
            return result.isConfirmed;
        });
    },

    confirmDirectToManager: function() {
        return Swal.fire({
            title: 'تأكيد التحويل المباشر',
            html: '<div class="text-right">' +
                '<strong class="text-danger">تنبيه هام جداً:</strong><br>' +
                'هذا الإجراء سيقوم بتحويل الطلب مباشرة إلى مدير الموارد البشرية،<br>' +
                'متجاوزاً جميع المشرفين.<br><br>' +
                'هل أنت متأكد من المتابعة؟</div>',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'نعم، متابعة',
            cancelButtonText: 'إلغاء',
            customClass: {
                title: 'text-right',
                htmlContainer: 'text-right',
                popup: 'swal2-rtl'
            }
        }).then((result) => {
            return result.isConfirmed;
        });
    },

    // Utility functions
    formatDate: function(dateString) {
        if (!dateString) return '';
        const date = new Date(dateString);
        return date.toLocaleDateString('ar-SA');
    },

    clearForm: function() {
        document.querySelectorAll('input[type="text"], textarea').forEach(input => {
            input.value = '';
        });

        document.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
            checkbox.checked = false;
        });

        document.querySelectorAll('select').forEach(select => {
            select.selectedIndex = 0;
        });

        // Hide panels
        const panels = ['OrderDetailsPanel', 'AutoPathInfoPanel', 'RejectionAlertPanel', 'RestoreDetailsPanel'];
        panels.forEach(panelId => {
            const panel = document.getElementById(panelId);
            if (panel) panel.style.display = 'none';
        });
    }
};

// Global functions for backward compatibility
function validateSelection() {
    return HRCoordinator.validateSelection();
}

function showError(message) {
    HRCoordinator.showError(message);
}

function showSuccess(message) {
    HRCoordinator.showSuccess(message);
}

function showLoading() {
    HRCoordinator.showLoading();
}

function hideLoading() {
    HRCoordinator.hideLoading();
}

function confirmSubmit() {
    return HRCoordinator.confirmSubmit();
}

function confirmAutoPath() {
    return HRCoordinator.confirmAutoPath();
}

function confirmDirectToManager() {
    return HRCoordinator.confirmDirectToManager();
}

// Supervisor selection functions
function selectAllSupervisors() {
    if (!validateSelection()) return;

    const checkboxes = document.querySelectorAll('input[name="SelectedSupervisors"]');
    checkboxes.forEach(checkbox => {
        checkbox.checked = true;
    });
}

function unselectAllSupervisors() {
    if (!validateSelection()) return;

    const checkboxes = document.querySelectorAll('input[name="SelectedSupervisors"]');
    checkboxes.forEach(checkbox => {
        checkbox.checked = false;
    });
}

function getSelectedSupervisors() {
    const checkboxes = document.querySelectorAll('input[name="SelectedSupervisors"]:checked');
    return Array.from(checkboxes).map(checkbox => checkbox.value);
}

// Order management functions
function loadOrderDetails(orderId) {
    if (!orderId || orderId === '') {
        const panels = ['OrderDetailsPanel', 'AutoPathInfoPanel', 'RejectionAlertPanel'];
        panels.forEach(panelId => {
            const panel = document.getElementById(panelId);
            if (panel) panel.style.display = 'none';
        });
        return;
    }

    fetch('/HRCoordinator/GetOrderDetails', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]').value
        },
        body: JSON.stringify({ orderId: parseInt(orderId) })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            populateOrderDetails(data.data);
            const orderDetailsPanel = document.getElementById('OrderDetailsPanel');
            if (orderDetailsPanel) orderDetailsPanel.style.display = 'block';

            // Update auto-routing info
            if (data.data.autoRoutingInfo) {
                updateAutoRoutingInfo(data.data.autoRoutingInfo);
            }

            // Show supervisor rejections if any
            if (data.data.supervisorRejections && data.data.supervisorRejections.length > 0) {
                showSupervisorRejections(data.data.supervisorRejections);
            }
        } else {
            showError(data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showError('حدث خطأ أثناء جلب تفاصيل الطلب');
    });
}

function populateOrderDetails(data) {
    const fields = [
        'lblOrderNumber', 'lblOrderDate', 'lblOrderStatus', 'lblOrderType',
        'lblEmployeeName', 'lblDepartment', 'lblJobTitle', 'lblEmploymentType',
        'lblQualification', 'lblEmployeeNumber', 'lblCivilRegistry', 'lblNationality',
        'lblMobileNumber', 'lblNotes', 'lblManagerApproval', 'lblSupervisorApproval',
        'lblCoordinatorApproval', 'lblCancellationReason', 'lblCoordinatorDetails',
        'lblHRManagerApproval'
    ];

    fields.forEach(fieldId => {
        const element = document.getElementById(fieldId);
        const fieldName = fieldId.replace('lbl', '').charAt(0).toLowerCase() + fieldId.replace('lbl', '').slice(1);
        if (element && data[fieldName] !== undefined) {
            element.textContent = data[fieldName] || '';
        }
    });

    // Supervisor permissions
    const permissions = [
        'MedicalServicesPermission', 'HRPlanningPermission', 'ITPermission',
        'AttendanceControlPermission', 'MedicalRecordsPermission', 'PayrollPermission',
        'LegalCompliancePermission', 'HRServicesPermission', 'HousingPermission',
        'FilesSectionPermission', 'OutpatientPermission', 'SocialInsurancePermission',
        'InventoryControlPermission', 'SelfResourcesPermission', 'NursingPermission',
        'EmployeeServicesPermission'
    ];

    permissions.forEach(permission => {
        const element = document.getElementById('lbl' + permission);
        const fieldName = permission.charAt(0).toLowerCase() + permission.slice(1);
        if (element && data[fieldName] !== undefined) {
            element.textContent = data[fieldName] || '/';
        }
    });
}

function updateAutoRoutingInfo(autoRoutingInfo) {
    const autoPathBtn = document.getElementById('btnAutoPath');
    const autoPathPanel = document.getElementById('AutoPathInfoPanel');

    if (autoPathBtn) {
        if (autoRoutingInfo.isAvailable) {
            autoPathBtn.className = 'btn btn-success w-100';
            autoPathBtn.disabled = false;
        } else {
            autoPathBtn.className = 'btn btn-secondary w-100';
            autoPathBtn.disabled = true;
        }
    }

    if (autoPathPanel) {
        autoPathPanel.innerHTML = autoRoutingInfo.message;
        autoPathPanel.style.display = 'block';
    }
}

function showSupervisorRejections(rejections) {
    let html = '<div class="alert alert-danger text-right" role="alert">';
    html += '<h5 class="alert-heading">⚠️ تحذير شديد!</h5><hr>';
    html += '<strong>هذا الطلب تمت إعادته من قبل المشرفين:</strong>';
    html += '<ul class="mt-2 list-unstyled">';

    rejections.forEach(rejection => {
        html += `<li class="text-danger mb-2"><i class="fas fa-exclamation-triangle"></i> ${rejection.rejectionDetails}</li>`;
    });

    html += '</ul><hr>';
    html += '<small class="text-danger">يرجى مراجعة سبب الإعادة قبل تحويل الطلب للمدير</small>';
    html += '</div>';

    const rejectionPanel = document.getElementById('RejectionAlertPanel');
    if (rejectionPanel) {
        rejectionPanel.innerHTML = html;
        rejectionPanel.style.display = 'block';
    }
}

// Action functions
function submitOrder() {
    if (!validateSelection()) return;

    const selectedSupervisors = getSelectedSupervisors();
    if (selectedSupervisors.length === 0) {
        showError('يجب اختيار قسم على الأقل للاعتماد');
        return;
    }

    const details = document.querySelector('textarea[name="Details"]').value.trim();
    if (!details) {
        showError('يرجى كتابة التفاصيل/الرقم');
        return;
    }

    confirmSubmit().then(confirmed => {
        if (!confirmed) return;

        const formData = new FormData();
        formData.append('orderId', document.getElementById('orderSelect').value);
        formData.append('details', details);
        selectedSupervisors.forEach(supervisor => {
            formData.append('selectedSupervisors', supervisor);
        });
        formData.append('__RequestVerificationToken', document.querySelector('input[name="__RequestVerificationToken"]').value);

        fetch('/HRCoordinator/SubmitOrder', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            if (response.ok) {
                window.location.reload();
            } else {
                showError('حدث خطأ أثناء تحويل الطلب');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showError('حدث خطأ أثناء تحويل الطلب');
        });
    });
}

function autoRouteOrder() {
    if (!validateSelection()) return;

    confirmAutoPath().then(confirmed => {
        if (!confirmed) return;

        const formData = new FormData();
        formData.append('orderId', document.getElementById('orderSelect').value);
        formData.append('__RequestVerificationToken', document.querySelector('input[name="__RequestVerificationToken"]').value);

        fetch('/HRCoordinator/AutoRouteOrder', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            if (response.ok) {
                window.location.reload();
            } else {
                showError('حدث خطأ أثناء التوجيه التلقائي');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showError('حدث خطأ أثناء التوجيه التلقائي');
        });
    });
}

function applyPath(pathNumber) {
    if (!validateSelection()) return;

    const formData = new FormData();
    formData.append('orderId', document.getElementById('orderSelect').value);
    formData.append('pathNumber', pathNumber);
    formData.append('__RequestVerificationToken', document.querySelector('input[name="__RequestVerificationToken"]').value);

    fetch('/HRCoordinator/ApplyPath', {
        method: 'POST',
        body: formData
    })
    .then(response => {
        if (response.ok) {
            window.location.reload();
        } else {
            showError(`حدث خطأ أثناء تطبيق المسار ${pathNumber}`);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showError(`حدث خطأ أثناء تطبيق المسار ${pathNumber}`);
    });
}

function directToManager() {
    if (!validateSelection()) return;

    confirmDirectToManager().then(confirmed => {
        if (!confirmed) return;

        const details = document.querySelector('textarea[name="Details"]').value.trim();

        const formData = new FormData();
        formData.append('orderId', document.getElementById('orderSelect').value);
        formData.append('details', details);
        formData.append('__RequestVerificationToken', document.querySelector('input[name="__RequestVerificationToken"]').value);

        fetch('/HRCoordinator/DirectToManager', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            if (response.ok) {
                window.location.reload();
            } else {
                showError('حدث خطأ أثناء تحويل الطلب للمدير');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showError('حدث خطأ أثناء تحويل الطلب للمدير');
        });
    });
}

function markNeedsAction() {
    if (!validateSelection()) return;

    const actionRequired = document.querySelector('input[name="ActionRequired"]').value.trim();
    if (!actionRequired) {
        showError('يرجى إدخال الإجراءات المطلوبة');
        return;
    }

    const formData = new FormData();
    formData.append('orderId', document.getElementById('orderSelect').value);
    formData.append('actionRequired', actionRequired);
    formData.append('__RequestVerificationToken', document.querySelector('input[name="__RequestVerificationToken"]').value);

    fetch('/HRCoordinator/MarkOrderNeedsAction', {
        method: 'POST',
        body: formData
    })
    .then(response => {
        if (response.ok) {
            window.location.reload();
        } else {
            showError('حدث خطأ أثناء حفظ الإجراءات');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showError('حدث خطأ أثناء حفظ الإجراءات');
    });
}

function returnOrder() {
    if (!validateSelection()) return;

    const returnReason = document.querySelector('textarea[name="ReturnReason"]').value.trim();
    if (!returnReason) {
        showError('يرجى إدخال سبب الإعادة');
        return;
    }

    const formData = new FormData();
    formData.append('orderId', document.getElementById('orderSelect').value);
    formData.append('returnReason', returnReason);
    formData.append('__RequestVerificationToken', document.querySelector('input[name="__RequestVerificationToken"]').value);

    fetch('/HRCoordinator/ReturnOrder', {
        method: 'POST',
        body: formData
    })
    .then(response => {
        if (response.ok) {
            window.location.reload();
        } else {
            showError('حدث خطأ أثناء إعادة الطلب');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showError('حدث خطأ أثناء إعادة الطلب');
    });
}

function rejectOrder() {
    if (!validateSelection()) return;

    const rejectReason = document.querySelector('textarea[name="ReturnReason"]').value.trim();
    if (!rejectReason) {
        showError('يرجى إدخال سبب الإلغاء');
        return;
    }

    const formData = new FormData();
    formData.append('orderId', document.getElementById('orderSelect').value);
    formData.append('rejectReason', rejectReason);
    formData.append('__RequestVerificationToken', document.querySelector('input[name="__RequestVerificationToken"]').value);

    fetch('/HRCoordinator/RejectOrder', {
        method: 'POST',
        body: formData
    })
    .then(response => {
        if (response.ok) {
            window.location.reload();
        } else {
            showError('حدث خطأ أثناء إلغاء الطلب');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showError('حدث خطأ أثناء إلغاء الطلب');
    });
}

function downloadAttachments() {
    if (!validateSelection()) return;

    const orderId = document.getElementById('orderSelect').value;
    window.open('/HRCoordinator/DownloadAttachments?orderId=' + orderId, '_blank');
}

// Restore functionality
function toggleRestoreSection() {
    const restoreSection = document.getElementById('RestoreSection');
    const toggleBtn = document.querySelector('button[onclick="toggleRestoreSection()"]');

    if (restoreSection.style.display === 'none' || restoreSection.style.display === '') {
        restoreSection.style.display = 'block';
        if (toggleBtn) toggleBtn.textContent = 'إخفاء قسم الاستعادة';
        loadRestorableOrders();
    } else {
        restoreSection.style.display = 'none';
        if (toggleBtn) toggleBtn.textContent = 'إظهار قسم الاستعادة';
    }
}

function loadRestorableOrders(searchTerm = '', filter = 'today') {
    fetch('/HRCoordinator/GetRestorableOrders', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]').value
        },
        body: JSON.stringify({ searchTerm: searchTerm, filter: filter })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const dropdown = document.getElementById('ddlRestorableOrders');
            dropdown.innerHTML = '<option value="">-- اختر الطلب للاستعادة --</option>';

            data.data.forEach(order => {
                const option = document.createElement('option');
                option.value = order.value;
                option.textContent = order.text;
                dropdown.appendChild(option);
            });
        } else {
            showError(data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showError('حدث خطأ أثناء جلب الطلبات');
    });
}

function loadRestoreOrderDetails(orderId) {
    if (!orderId || orderId === '') {
        const restoreDetailsPanel = document.getElementById('RestoreDetailsPanel');
        if (restoreDetailsPanel) restoreDetailsPanel.style.display = 'none';
        return;
    }

    fetch('/HRCoordinator/GetRestoreOrderDetails', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]').value
        },
        body: JSON.stringify({ orderId: parseInt(orderId) })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const statusElement = document.getElementById('lblRestoreCurrentStatus');
            const dateElement = document.getElementById('lblRestoreTransferDate');
            const supervisorsElement = document.getElementById('lblRestoreAssignedSupervisors');

            if (statusElement) statusElement.textContent = data.data.currentStatus || '';
            if (dateElement) dateElement.textContent = data.data.transferDate || '';
            if (supervisorsElement) supervisorsElement.textContent = data.data.assignedSupervisors || '';

            const restoreDetailsPanel = document.getElementById('RestoreDetailsPanel');
            if (restoreDetailsPanel) restoreDetailsPanel.style.display = 'block';
        } else {
            showError(data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showError('حدث خطأ أثناء جلب تفاصيل الطلب');
    });
}

function restoreOrder() {
    const orderId = document.getElementById('ddlRestorableOrders').value;
    if (!orderId) {
        showError('يرجى اختيار طلب للاستعادة');
        return;
    }

    const restoreNotes = document.querySelector('textarea[name="RestoreNotes"]').value.trim();

    const formData = new FormData();
    formData.append('orderId', orderId);
    formData.append('restoreNotes', restoreNotes);
    formData.append('__RequestVerificationToken', document.querySelector('input[name="__RequestVerificationToken"]').value);

    fetch('/HRCoordinator/RestoreOrder', {
        method: 'POST',
        body: formData
    })
    .then(response => {
        if (response.ok) {
            window.location.reload();
        } else {
            showError('حدث خطأ أثناء استعادة الطلب');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showError('حدث خطأ أثناء استعادة الطلب');
    });
}

function searchRestorableOrders() {
    const searchTerm = document.getElementById('txtRestoreSearch').value.trim();
    const filter = document.getElementById('ddlRestoreFilter').value;
    loadRestorableOrders(searchTerm, filter);
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    HRCoordinator.init();
});
