{"ContentRoots": ["E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\"], "Root": {"Children": {"css": {"Children": {"dashbord": {"Children": {"dashbord.css": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "css/dashbord/dashbord.css"}, "Patterns": null}}, "Asset": null, "Patterns": null}, "MainSite.css": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "css/MainSite.css"}, "Patterns": null}}, "Asset": null, "Patterns": null}, "favicon.ico": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "favicon.ico"}, "Patterns": null}, "img": {"Children": {"Breada1.png": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "img/Breada1.png"}, "Patterns": null}, "Breada2.jpg": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "img/Breada2.jpg"}, "Patterns": null}, "Breada3.jpg": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "img/Breada3.jpg"}, "Patterns": null}, "Breada4.png": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "img/Breada4.png"}, "Patterns": null}, "CardImage.jpg": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "img/CardImage.jpg"}, "Patterns": null}, "draw2.webp": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "img/draw2.webp"}, "Patterns": null}, "hero-svg-illustration.svg": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "img/hero-svg-illustration.svg"}, "Patterns": null}, "star.png": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "img/star.png"}, "Patterns": null}}, "Asset": null, "Patterns": null}, "js": {"Children": {".eslintrc.json": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "js/.eslintrc.json"}, "Patterns": null}, "assistantManager.js": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "js/assistantManager.js"}, "Patterns": null}, "dashboard-charts.js": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "js/dashboard-charts.js"}, "Patterns": null}, "dashboard.js": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "js/dashboard.js"}, "Patterns": null}, "directManager.js": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "js/directManager.js"}, "Patterns": null}, "Notification.js": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "js/Notification.js"}, "Patterns": null}, "orderDetailsModule.js": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "js/orderDetailsModule.js"}, "Patterns": null}, "orderDetailsModule.md": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "js/orderDetailsModule.md"}, "Patterns": null}, "OrderDetals.js": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "js/OrderDetals.js"}, "Patterns": null}, "README.md": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "js/README.md"}, "Patterns": null}, "shared-utils.js": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "js/shared-utils.js"}, "Patterns": null}, "site.js": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "js/site.js"}, "Patterns": null}, "test-orderDetailsModule.html": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "js/test-orderDetailsModule.html"}, "Patterns": null}}, "Asset": null, "Patterns": null}, "lib": {"Children": {"bootstrap": {"Children": {"dist": {"Children": {"css": {"Children": {"bootstrap-grid.css": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "lib/bootstrap/dist/css/bootstrap-grid.css"}, "Patterns": null}, "bootstrap-grid.css.map": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "lib/bootstrap/dist/css/bootstrap-grid.css.map"}, "Patterns": null}, "bootstrap-grid.min.css": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "lib/bootstrap/dist/css/bootstrap-grid.min.css"}, "Patterns": null}, "bootstrap-grid.min.css.map": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map"}, "Patterns": null}, "bootstrap-grid.rtl.css": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css"}, "Patterns": null}, "bootstrap-grid.rtl.css.map": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map"}, "Patterns": null}, "bootstrap-grid.rtl.min.css": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css"}, "Patterns": null}, "bootstrap-grid.rtl.min.css.map": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map"}, "Patterns": null}, "bootstrap-reboot.css": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "lib/bootstrap/dist/css/bootstrap-reboot.css"}, "Patterns": null}, "bootstrap-reboot.css.map": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "lib/bootstrap/dist/css/bootstrap-reboot.css.map"}, "Patterns": null}, "bootstrap-reboot.min.css": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css"}, "Patterns": null}, "bootstrap-reboot.min.css.map": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map"}, "Patterns": null}, "bootstrap-reboot.rtl.css": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css"}, "Patterns": null}, "bootstrap-reboot.rtl.css.map": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map"}, "Patterns": null}, "bootstrap-reboot.rtl.min.css": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css"}, "Patterns": null}, "bootstrap-reboot.rtl.min.css.map": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map"}, "Patterns": null}, "bootstrap-utilities.css": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "lib/bootstrap/dist/css/bootstrap-utilities.css"}, "Patterns": null}, "bootstrap-utilities.css.map": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "lib/bootstrap/dist/css/bootstrap-utilities.css.map"}, "Patterns": null}, "bootstrap-utilities.min.css": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css"}, "Patterns": null}, "bootstrap-utilities.min.css.map": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map"}, "Patterns": null}, "bootstrap-utilities.rtl.css": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css"}, "Patterns": null}, "bootstrap-utilities.rtl.css.map": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map"}, "Patterns": null}, "bootstrap-utilities.rtl.min.css": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css"}, "Patterns": null}, "bootstrap-utilities.rtl.min.css.map": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map"}, "Patterns": null}, "bootstrap.css": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "lib/bootstrap/dist/css/bootstrap.css"}, "Patterns": null}, "bootstrap.css.map": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "lib/bootstrap/dist/css/bootstrap.css.map"}, "Patterns": null}, "bootstrap.min.css": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "lib/bootstrap/dist/css/bootstrap.min.css"}, "Patterns": null}, "bootstrap.min.css.map": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "lib/bootstrap/dist/css/bootstrap.min.css.map"}, "Patterns": null}, "bootstrap.rtl.css": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "lib/bootstrap/dist/css/bootstrap.rtl.css"}, "Patterns": null}, "bootstrap.rtl.css.map": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "lib/bootstrap/dist/css/bootstrap.rtl.css.map"}, "Patterns": null}, "bootstrap.rtl.min.css": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css"}, "Patterns": null}, "bootstrap.rtl.min.css.map": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map"}, "Patterns": null}}, "Asset": null, "Patterns": null}, "js": {"Children": {"bootstrap.bundle.js": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "lib/bootstrap/dist/js/bootstrap.bundle.js"}, "Patterns": null}, "bootstrap.bundle.js.map": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "lib/bootstrap/dist/js/bootstrap.bundle.js.map"}, "Patterns": null}, "bootstrap.bundle.min.js": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js"}, "Patterns": null}, "bootstrap.bundle.min.js.map": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map"}, "Patterns": null}, "bootstrap.esm.js": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "lib/bootstrap/dist/js/bootstrap.esm.js"}, "Patterns": null}, "bootstrap.esm.js.map": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "lib/bootstrap/dist/js/bootstrap.esm.js.map"}, "Patterns": null}, "bootstrap.esm.min.js": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "lib/bootstrap/dist/js/bootstrap.esm.min.js"}, "Patterns": null}, "bootstrap.esm.min.js.map": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map"}, "Patterns": null}, "bootstrap.js": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "lib/bootstrap/dist/js/bootstrap.js"}, "Patterns": null}, "bootstrap.js.map": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "lib/bootstrap/dist/js/bootstrap.js.map"}, "Patterns": null}, "bootstrap.min.js": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "lib/bootstrap/dist/js/bootstrap.min.js"}, "Patterns": null}, "bootstrap.min.js.map": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "lib/bootstrap/dist/js/bootstrap.min.js.map"}, "Patterns": null}}, "Asset": null, "Patterns": null}}, "Asset": null, "Patterns": null}, "LICENSE": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "lib/bootstrap/LICENSE"}, "Patterns": null}}, "Asset": null, "Patterns": null}, "jquery-validation-unobtrusive": {"Children": {"jquery.validate.unobtrusive.js": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.js"}, "Patterns": null}, "jquery.validate.unobtrusive.min.js": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js"}, "Patterns": null}, "LICENSE.txt": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "lib/jquery-validation-unobtrusive/LICENSE.txt"}, "Patterns": null}}, "Asset": null, "Patterns": null}, "jquery-validation": {"Children": {"dist": {"Children": {"additional-methods.js": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "lib/jquery-validation/dist/additional-methods.js"}, "Patterns": null}, "additional-methods.min.js": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "lib/jquery-validation/dist/additional-methods.min.js"}, "Patterns": null}, "jquery.validate.js": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "lib/jquery-validation/dist/jquery.validate.js"}, "Patterns": null}, "jquery.validate.min.js": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "lib/jquery-validation/dist/jquery.validate.min.js"}, "Patterns": null}}, "Asset": null, "Patterns": null}, "LICENSE.md": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "lib/jquery-validation/LICENSE.md"}, "Patterns": null}}, "Asset": null, "Patterns": null}, "jquery": {"Children": {"dist": {"Children": {"jquery.js": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "lib/jquery/dist/jquery.js"}, "Patterns": null}, "jquery.min.js": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "lib/jquery/dist/jquery.min.js"}, "Patterns": null}, "jquery.min.map": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "lib/jquery/dist/jquery.min.map"}, "Patterns": null}}, "Asset": null, "Patterns": null}, "LICENSE.txt": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "lib/jquery/LICENSE.txt"}, "Patterns": null}}, "Asset": null, "Patterns": null}}, "Asset": null, "Patterns": null}, "uploads": {"Children": {"order_1234567890_file1_20250704150713.pdf": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "uploads/order_1234567890_file1_20250704150713.pdf"}, "Patterns": null}, "order_1_bf69c15d-ff5e-41b2-bfb8-74eb832baa59_order_1_file1_20250627233043.pdf": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "uploads/order_1_bf69c15d-ff5e-41b2-bfb8-74eb832baa59_order_1_file1_20250627233043.pdf"}, "Patterns": null}}, "Asset": null, "Patterns": null}}, "Asset": null, "Patterns": [{"ContentRootIndex": 0, "Pattern": "**", "Depth": 0}]}}