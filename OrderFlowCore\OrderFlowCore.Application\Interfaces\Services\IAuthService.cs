using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Application.Common;

namespace OrderFlowCore.Application.Interfaces.Services;

public interface IAuthService
{
    Task<ServiceResult> ValidateUserAsync(string username, string password);
    Task<ServiceResult<UserDto>> GetUserByUsernameAsync(string username);
    string HashPassword(string password);
    bool VerifyPassword(string password, string hashedPassword);
} 