@model IEnumerable<OrderFlowCore.Application.DTOs.EmployeeDto>
@{
    ViewData["Title"] = "إدارة بيانات الموظفين";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="page-title">إدارة بيانات الموظفين</h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="@Url.Action("Index", "Dashboard")">لوحة التحكم</a></li>
                            <li class="breadcrumb-item"><a href="@Url.Action("Index", "Settings")">الإعدادات</a></li>
                            <li class="breadcrumb-item active" aria-current="page">إدارة بيانات الموظفين</li>
                        </ol>
                    </nav>
                </div>
                <a asp-action="Create" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>إضافة موظف جديد
                </a>
            </div>
        </div>
    </div>

    @if (TempData["SuccessMessage"] != null)
    {
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            @TempData["SuccessMessage"]
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }

    @if (TempData["ErrorMessage"] != null)
    {
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>
            @TempData["ErrorMessage"]
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }

    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-users me-2"></i>قائمة الموظفين
                    </h5>
                </div>
                <div class="card-body">
                    @if (Model != null && Model.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>#</th>
                                        <th>اسم الموظف</th>
                                        <th>الوظيفة</th>
                                        <th>رقم الموظف</th>
                                        <th>السجل المدني</th>
                                        <th>الجنسية</th>
                                        <th>رقم الجوال</th>
                                        <th>نوع التوظيف</th>
                                        <th>المؤهل</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var employee in Model)
                                    {
                                        <tr>
                                            <td>@employee.Id</td>
                                            <td>@employee.Name</td>
                                            <td>@employee.Job</td>
                                            <td>@employee.EmployeeNumber</td>
                                            <td>@employee.CivilNumber</td>
                                            <td>@employee.Nationality</td>
                                            <td>@employee.Mobile</td>
                                            <td>@employee.EmploymentType</td>
                                            <td>@employee.Qualification</td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a asp-action="Edit" asp-route-id="@employee.Id" 
                                                       class="btn btn-sm btn-outline-primary" 
                                                       title="تعديل">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <button type="button" 
                                                            class="btn btn-sm btn-outline-danger" 
                                                            onclick="confirmDelete(@employee.Id, '@employee.Name')"
                                                            title="حذف">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-users fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد بيانات موظفين</h5>
                            <p class="text-muted">قم بإضافة موظف جديد للبدء</p>
                            <a asp-action="Create" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>إضافة موظف جديد
                            </a>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function confirmDelete(id, name) {
            if (confirm(`هل أنت متأكد من حذف الموظف "${name}"؟`)) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = '@Url.Action("Delete")';
                
                const token = document.querySelector('input[name="__RequestVerificationToken"]').value;
                const tokenInput = document.createElement('input');
                tokenInput.type = 'hidden';
                tokenInput.name = '__RequestVerificationToken';
                tokenInput.value = token;
                
                const idInput = document.createElement('input');
                idInput.type = 'hidden';
                idInput.name = 'id';
                idInput.value = id;
                
                form.appendChild(tokenInput);
                form.appendChild(idInput);
                document.body.appendChild(form);
                form.submit();
            }
        }
    </script>
} 