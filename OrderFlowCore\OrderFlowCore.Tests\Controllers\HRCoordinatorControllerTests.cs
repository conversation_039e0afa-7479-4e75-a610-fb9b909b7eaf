using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Moq;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Application.Interfaces.Services;
using OrderFlowCore.Web.Controllers;
using OrderFlowCore.Web.ViewModels;
using System.Collections.Generic;
using System.Security.Claims;
using System.Threading.Tasks;
using Xunit;
using Microsoft.AspNetCore.Http;
using FluentAssertions;

namespace OrderFlowCore.Tests.Controllers
{
    public class HRCoordinatorControllerTests
    {
        private readonly Mock<IOrderService> _mockOrderService;
        private readonly Mock<ILogger<HRCoordinatorController>> _mockLogger;
        private readonly HRCoordinatorController _controller;

        public HRCoordinatorControllerTests()
        {
            _mockOrderService = new Mock<IOrderService>();
            _mockLogger = new Mock<ILogger<HRCoordinatorController>>();
            _controller = new HRCoordinatorController(_mockOrderService.Object, _mockLogger.Object);

            // Setup user context
            var claims = new List<Claim>
            {
                new Claim("Permission", "منسق الموارد البشرية"),
                new Claim(ClaimTypes.Name, "TestUser")
            };
            var identity = new ClaimsIdentity(claims, "TestAuthType");
            var principal = new ClaimsPrincipal(identity);

            _controller.ControllerContext = new ControllerContext
            {
                HttpContext = new DefaultHttpContext
                {
                    User = principal
                }
            };
        }

        [Fact]
        public async Task Index_WithValidPermission_ReturnsViewWithModel()
        {
            // Arrange
            var orders = new List<OrderSummaryDto>
            {
                new OrderSummaryDto { Id = 1, EmployeeName = "Test Employee" }
            };
            var restorableOrders = new List<RestorableOrderDto>
            {
                new RestorableOrderDto { Id = 1, EmployeeName = "Test Employee", Department = "Test Dept" }
            };

            _mockOrderService.Setup(x => x.GetHRCoordinatorOrdersAsync())
                .ReturnsAsync(ServiceResult<List<OrderSummaryDto>>.Success(orders));
            _mockOrderService.Setup(x => x.GetRestorableOrdersAsync("", "today"))
                .ReturnsAsync(ServiceResult<List<RestorableOrderDto>>.Success(restorableOrders));

            // Act
            var result = await _controller.Index();

            // Assert
            result.Should().BeOfType<ViewResult>();
            var viewResult = result as ViewResult;
            viewResult.Model.Should().BeOfType<HRCoordinatorViewModel>();
            
            var model = viewResult.Model as HRCoordinatorViewModel;
            model.OrderNumbers.Should().HaveCount(1);
            model.RestorableOrders.Should().HaveCount(1);
        }

        [Fact]
        public async Task Index_WithInvalidPermission_RedirectsToAccessDenied()
        {
            // Arrange
            var claims = new List<Claim>
            {
                new Claim("Permission", "Invalid Permission"),
                new Claim(ClaimTypes.Name, "TestUser")
            };
            var identity = new ClaimsIdentity(claims, "TestAuthType");
            var principal = new ClaimsPrincipal(identity);

            _controller.ControllerContext.HttpContext.User = principal;

            // Act
            var result = await _controller.Index();

            // Assert
            result.Should().BeOfType<RedirectToActionResult>();
            var redirectResult = result as RedirectToActionResult;
            redirectResult.ActionName.Should().Be("AccessDenied");
            redirectResult.ControllerName.Should().Be("Auth");
        }

        [Fact]
        public async Task GetOrderDetails_WithValidOrderId_ReturnsSuccessJson()
        {
            // Arrange
            var orderDetails = new OrderDetailsDto
            {
                Id = 1,
                EmployeeName = "Test Employee",
                OrderType = "Test Type"
            };
            var autoRoutingInfo = new AutoRoutingInfoDto
            {
                IsAvailable = true,
                Message = "Auto routing available"
            };

            _mockOrderService.Setup(x => x.GetOrderDetailsAsync(1))
                .ReturnsAsync(ServiceResult<OrderDetailsDto>.Success(orderDetails));
            _mockOrderService.Setup(x => x.GetAutoRoutingInfoAsync(1))
                .ReturnsAsync(ServiceResult<AutoRoutingInfoDto>.Success(autoRoutingInfo));
            _mockOrderService.Setup(x => x.GetSupervisorRejectionsAsync(1))
                .ReturnsAsync(ServiceResult<List<SupervisorRejectionDto>>.Success(new List<SupervisorRejectionDto>()));

            // Act
            var result = await _controller.GetOrderDetails(1);

            // Assert
            result.Should().BeOfType<JsonResult>();
            var jsonResult = result as JsonResult;
            // Additional assertions can be added here to verify the JSON structure
        }

        [Fact]
        public async Task SubmitOrder_WithValidData_ReturnsRedirectToIndex()
        {
            // Arrange
            var selectedSupervisors = new List<string> { "خدمات الموظفين", "إدارة تقنية المعلومات" };
            _mockOrderService.Setup(x => x.SubmitOrderToSupervisorsAsync(1, "Test details", selectedSupervisors, "TestUser"))
                .ReturnsAsync(ServiceResult.Success("Order submitted successfully"));

            // Act
            var result = await _controller.SubmitOrder(1, "Test details", selectedSupervisors);

            // Assert
            result.Should().BeOfType<RedirectToActionResult>();
            var redirectResult = result as RedirectToActionResult;
            redirectResult.ActionName.Should().Be("Index");
        }

        [Fact]
        public async Task AutoRouteOrder_WithValidOrderId_ReturnsRedirectToIndex()
        {
            // Arrange
            _mockOrderService.Setup(x => x.AutoRouteOrderAsync(1, "TestUser"))
                .ReturnsAsync(ServiceResult.Success("Auto routing successful"));

            // Act
            var result = await _controller.AutoRouteOrder(1);

            // Assert
            result.Should().BeOfType<RedirectToActionResult>();
            var redirectResult = result as RedirectToActionResult;
            redirectResult.ActionName.Should().Be("Index");
        }

        [Fact]
        public async Task RestoreOrder_WithValidData_ReturnsRedirectToIndex()
        {
            // Arrange
            _mockOrderService.Setup(x => x.RestoreOrderFromSupervisorsAsync(1, "Restore notes", "TestUser"))
                .ReturnsAsync(ServiceResult.Success("Order restored successfully"));

            // Act
            var result = await _controller.RestoreOrder(1, "Restore notes");

            // Assert
            result.Should().BeOfType<RedirectToActionResult>();
            var redirectResult = result as RedirectToActionResult;
            redirectResult.ActionName.Should().Be("Index");
        }

        [Fact]
        public async Task DownloadAttachments_WithValidOrderId_ReturnsFileResult()
        {
            // Arrange
            var fileData = new byte[] { 1, 2, 3, 4, 5 };
            var orderDetails = new OrderDetailsDto
            {
                Id = 1,
                EmployeeName = "Test Employee"
            };

            _mockOrderService.Setup(x => x.DownloadOrderAttachmentsZipAsync(1))
                .ReturnsAsync(ServiceResult<byte[]>.Success(fileData));
            _mockOrderService.Setup(x => x.GetOrderDetailsAsync(1))
                .ReturnsAsync(ServiceResult<OrderDetailsDto>.Success(orderDetails));

            // Act
            var result = await _controller.DownloadAttachments(1);

            // Assert
            result.Should().BeOfType<FileContentResult>();
            var fileResult = result as FileContentResult;
            fileResult.ContentType.Should().Be("application/zip");
            fileResult.FileContents.Should().Equal(fileData);
        }
    }
}
