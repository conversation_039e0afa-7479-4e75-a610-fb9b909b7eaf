@model UserProfileViewModel
@{
    ViewData["Title"] = "تعديل الملف الشخصي";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <div>
                    <h1 class="page-title">تعديل الملف الشخصي</h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="@Url.Action("Index", "Dashboard")">لوحة التحكم</a></li>
                            <li class="breadcrumb-item"><a href="@Url.Action("Profile", "User")">الملف الشخصي</a></li>
                            <li class="breadcrumb-item active" aria-current="page">تعديل</li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </div>

    <div class="row justify-content-center">
        <div class="col-lg-8 col-md-10">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-gradient-primary text-white">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-user-edit fa-2x me-3"></i>
                        <div>
                            <h4 class="mb-0">تعديل الملف الشخصي</h4>
                            <small>قم بتحديث معلوماتك الشخصية</small>
                        </div>
                    </div>
                </div>
                <div class="card-body p-4">
                    @if (TempData["ToastrSuccess"] != null)
                    {
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i>
                            @TempData["ToastrSuccess"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    }

                    @if (TempData["ToastrError"] != null)
                    {
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            @TempData["ToastrError"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    }

                    <form asp-action="EditProfile" method="post" id="editProfileForm">
                        <div asp-validation-summary="ModelOnly" class="alert alert-danger"></div>
                        
                        <input type="hidden" asp-for="Id" />
                        <input type="hidden" asp-for="Username" />
                        <input type="hidden" asp-for="Role" />

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-4">
                                    <label asp-for="Username" class="form-label fw-bold">
                                        <i class="fas fa-user me-2"></i>اسم المستخدم
                                    </label>
                                    <input asp-for="Username" class="form-control form-control-lg" readonly />
                                    <div class="form-text">لا يمكن تغيير اسم المستخدم</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-4">
                                    <label asp-for="Role" class="form-label fw-bold">
                                        <i class="fas fa-user-tag me-2"></i>الدور
                                    </label>
                                    <input asp-for="Role" class="form-control form-control-lg" readonly />
                                    <div class="form-text">لا يمكن تغيير الدور</div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-4">
                                    <label asp-for="Email" class="form-label fw-bold">
                                        <i class="fas fa-envelope me-2"></i>البريد الإلكتروني
                                    </label>
                                    <input asp-for="Email" class="form-control form-control-lg" 
                                           placeholder="أدخل بريدك الإلكتروني" />
                                    <span asp-validation-for="Email" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-4">
                                    <label asp-for="Phone" class="form-label fw-bold">
                                        <i class="fas fa-phone me-2"></i>رقم الهاتف
                                    </label>
                                    <input asp-for="Phone" class="form-control form-control-lg" 
                                           placeholder="أدخل رقم هاتفك" />
                                    <span asp-validation-for="Phone" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <hr class="my-4">

                        <div class="d-flex justify-content-between align-items-center">
                            <a asp-action="Profile" class="btn btn-outline-secondary btn-lg">
                                <i class="fas fa-arrow-right me-2"></i>العودة للملف الشخصي
                            </a>
                            <div>
                                <button type="button" class="btn btn-outline-warning btn-lg me-2" onclick="resetForm()">
                                    <i class="fas fa-undo me-2"></i>إعادة تعيين
                                </button>
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-save me-2"></i>حفظ التغييرات
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>



@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    
    <script>
        // إعادة تعيين النموذج
        function resetForm() {
            if (confirm('هل أنت متأكد من إعادة تعيين النموذج؟')) {
                document.getElementById('editProfileForm').reset();
            }
        }

        // تأكيد قبل الإرسال
        document.getElementById('editProfileForm').addEventListener('submit', function(e) {
            const submitBtn = this.querySelector('button[type="submit"]');
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الحفظ...';
            submitBtn.disabled = true;
        });

        // إخفاء رسائل التنبيه تلقائياً
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);
    </script>
} 