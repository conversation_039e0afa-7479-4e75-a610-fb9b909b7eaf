using Microsoft.AspNetCore.Mvc;
using OrderFlowCore.Application.Interfaces.Services;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Web.ViewModels;
using Microsoft.AspNetCore.Mvc.Rendering;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq;
using Microsoft.Extensions.Logging;
using System.IO.Compression;
using System.IO;
using System.Security.Claims;
using Microsoft.AspNetCore.Authorization;
using OrderFlowCore.Core.Entities;

namespace OrderFlowCore.Web.Controllers
{
    [Authorize]
    public class HRCoordinatorController : Controller
    {
        private readonly IOrderService _orderService;
        private readonly ILogger<HRCoordinatorController> _logger;

        public HRCoordinatorController(IOrderService orderService, ILogger<HRCoordinatorController> logger)
        {
            _orderService = orderService;
            _logger = logger;
        }

        [HttpGet]
        public async Task<IActionResult> Index()
        {
            try
            {
                // Check user permission for HR Coordinator
                var userPermission = User.FindFirst("Permission")?.Value;
                if (userPermission != "منسق الموارد البشرية")
                {
                    TempData["ErrorMessage"] = "ليس لديك صلاحية للوصول إلى هذه الصفحة";
                    return RedirectToAction("AccessDenied", "Auth");
                }

                var viewModel = new HRCoordinatorViewModel();
                
                // Get orders for HR coordinator
                var ordersResult = await _orderService.GetHRCoordinatorOrdersAsync();
                if (ordersResult.IsSuccess)
                {
                    viewModel.OrderNumbers = ordersResult.Data.Select(o => new SelectListItem
                    {
                        Value = o.Id.ToString(),
                        Text = string.IsNullOrEmpty(o.EmployeeName) 
                            ? o.Id.ToString() 
                            : $"{o.Id} | {o.EmployeeName}"
                    }).ToList();
                }
                else
                {
                    TempData["ErrorMessage"] = ordersResult.Message;
                }

                // Get restorable orders
                var restorableOrdersResult = await _orderService.GetRestorableOrdersAsync("", "today");
                if (restorableOrdersResult.IsSuccess)
                {
                    viewModel.RestorableOrders = restorableOrdersResult.Data.Select(o => new SelectListItem
                    {
                        Value = o.Id.ToString(),
                        Text = $"{o.Id} | {o.EmployeeName} | {o.Department}"
                    }).ToList();
                }
                
                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in HRCoordinator Index");
                TempData["ErrorMessage"] = "حدث خطأ أثناء تحميل الصفحة";
                return RedirectToAction("Index", "Home");
            }
        }

        [HttpPost]
        public async Task<IActionResult> GetOrderDetails(int orderId)
        {
            try
            {
                var result = await _orderService.GetOrderDetailsAsync(orderId);
                if (!result.IsSuccess)
                {
                    return Json(new { success = false, message = result.Message });
                }
                
                var viewModel = HRCoordinatorViewModel.FromOrderDetails(result.Data);
                
                // Get auto-routing information
                var autoRouteResult = await _orderService.GetAutoRoutingInfoAsync(orderId);
                if (autoRouteResult.IsSuccess)
                {
                    viewModel.AutoRoutingInfo = autoRouteResult.Data;
                }

                // Check for supervisor rejections
                var rejectionResult = await _orderService.GetSupervisorRejectionsAsync(orderId);
                if (rejectionResult.IsSuccess)
                {
                    viewModel.SupervisorRejections = rejectionResult.Data;
                }

                return Json(new { success = true, data = viewModel });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting order details");
                return Json(new { success = false, message = "حدث خطأ أثناء جلب تفاصيل الطلب" });
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> SubmitOrder(int orderId, string details, List<string> selectedSupervisors)
        {
            try
            {
                if (orderId <= 0)
                {
                    TempData["ErrorMessage"] = "يرجى اختيار رقم الطلب.";
                    return RedirectToAction(nameof(Index));
                }

                if (string.IsNullOrWhiteSpace(details))
                {
                    TempData["ErrorMessage"] = "يرجى كتابة التفاصيل/الرقم.";
                    return RedirectToAction(nameof(Index));
                }

                if (selectedSupervisors == null || !selectedSupervisors.Any())
                {
                    TempData["ErrorMessage"] = "يجب اختيار قسم على الأقل للاعتماد.";
                    return RedirectToAction(nameof(Index));
                }

                var userName = User.Identity?.Name;
                var result = await _orderService.SubmitOrderToSupervisorsAsync(orderId, details, selectedSupervisors, userName);

                if (result.IsSuccess)
                {
                    TempData["SuccessMessage"] = result.Message;
                }
                else
                {
                    TempData["ErrorMessage"] = result.Message;
                }
                
                return RedirectToAction(nameof(Index));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error submitting order");
                TempData["ErrorMessage"] = "حدث خطأ أثناء تحويل الطلب";
                return RedirectToAction(nameof(Index));
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> AutoRouteOrder(int orderId)
        {
            try
            {
                if (orderId <= 0)
                {
                    TempData["ErrorMessage"] = "يرجى اختيار رقم الطلب.";
                    return RedirectToAction(nameof(Index));
                }

                var userName = User.Identity?.Name;
                var result = await _orderService.AutoRouteOrderAsync(orderId, userName);

                if (result.IsSuccess)
                {
                    TempData["SuccessMessage"] = result.Message;
                }
                else
                {
                    TempData["ErrorMessage"] = result.Message;
                }
                
                return RedirectToAction(nameof(Index));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error auto-routing order");
                TempData["ErrorMessage"] = "حدث خطأ أثناء التوجيه التلقائي";
                return RedirectToAction(nameof(Index));
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> ReturnOrder(int orderId, string returnReason)
        {
            try
            {
                if (orderId <= 0)
                {
                    TempData["ErrorMessage"] = "يرجى اختيار رقم الطلب.";
                    return RedirectToAction(nameof(Index));
                }

                if (string.IsNullOrWhiteSpace(returnReason))
                {
                    TempData["ErrorMessage"] = "يرجى إدخال سبب الإعادة.";
                    return RedirectToAction(nameof(Index));
                }

                var userName = User.Identity?.Name;
                var result = await _orderService.ReturnOrderAsync(orderId, returnReason, userName);

                if (result.IsSuccess)
                {
                    TempData["SuccessMessage"] = result.Message;
                }
                else
                {
                    TempData["ErrorMessage"] = result.Message;
                }
                
                return RedirectToAction(nameof(Index));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error returning order");
                TempData["ErrorMessage"] = "حدث خطأ أثناء إعادة الطلب";
                return RedirectToAction(nameof(Index));
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> MarkOrderNeedsAction(int orderId, string actionRequired)
        {
            try
            {
                if (orderId <= 0)
                {
                    TempData["ErrorMessage"] = "يرجى اختيار رقم الطلب.";
                    return RedirectToAction(nameof(Index));
                }

                if (string.IsNullOrWhiteSpace(actionRequired))
                {
                    TempData["ErrorMessage"] = "يرجى إدخال الإجراءات المطلوبة.";
                    return RedirectToAction(nameof(Index));
                }

                var userName = User.Identity?.Name;
                var result = await _orderService.MarkOrderNeedsActionAsync(orderId, actionRequired, userName);

                if (result.IsSuccess)
                {
                    TempData["SuccessMessage"] = result.Message;
                }
                else
                {
                    TempData["ErrorMessage"] = result.Message;
                }
                
                return RedirectToAction(nameof(Index));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error marking order as needs action");
                TempData["ErrorMessage"] = "حدث خطأ أثناء حفظ الإجراءات";
                return RedirectToAction(nameof(Index));
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> RejectOrder(int orderId, string rejectReason)
        {
            try
            {
                if (orderId <= 0)
                {
                    TempData["ErrorMessage"] = "يرجى اختيار رقم الطلب.";
                    return RedirectToAction(nameof(Index));
                }

                if (string.IsNullOrWhiteSpace(rejectReason))
                {
                    TempData["ErrorMessage"] = "يرجى إدخال سبب الإلغاء.";
                    return RedirectToAction(nameof(Index));
                }

                var userName = User.Identity?.Name;
                var result = await _orderService.RejectOrderByHRCoordinatorAsync(orderId, rejectReason, userName);

                if (result.IsSuccess)
                {
                    TempData["SuccessMessage"] = result.Message;
                }
                else
                {
                    TempData["ErrorMessage"] = result.Message;
                }
                
                return RedirectToAction(nameof(Index));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error rejecting order");
                TempData["ErrorMessage"] = "حدث خطأ أثناء إلغاء الطلب";
                return RedirectToAction(nameof(Index));
            }
        }

        [HttpGet]
        public async Task<IActionResult> DownloadAttachments(int orderId)
        {
            try
            {
                var result = await _orderService.DownloadOrderAttachmentsZipAsync(orderId);
                if (!result.IsSuccess)
                {
                    TempData["ErrorMessage"] = result.Message;
                    return RedirectToAction(nameof(Index));
                }

                var orderDetailsResult = await _orderService.GetOrderDetailsAsync(orderId);
                if (!orderDetailsResult.IsSuccess)
                {
                    TempData["ErrorMessage"] = "لم يتم العثور على الطلب";
                    return RedirectToAction(nameof(Index));
                }

                var order = orderDetailsResult.Data;
                string zipFileName = CleanFileName($"مرفقات_طلب_{orderId}_{order.EmployeeName}.zip");

                return File(result.Data, "application/zip", zipFileName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error downloading attachments");
                TempData["ErrorMessage"] = "حدث خطأ أثناء تحميل الملفات";
                return RedirectToAction(nameof(Index));
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DirectToManager(int orderId, string details)
        {
            try
            {
                if (orderId <= 0)
                {
                    TempData["ErrorMessage"] = "يرجى اختيار رقم الطلب.";
                    return RedirectToAction(nameof(Index));
                }

                var userName = User.Identity?.Name;
                var result = await _orderService.DirectOrderToManagerAsync(orderId, details, userName);

                if (result.IsSuccess)
                {
                    TempData["SuccessMessage"] = result.Message;
                }
                else
                {
                    TempData["ErrorMessage"] = result.Message;
                }

                return RedirectToAction(nameof(Index));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error directing order to manager");
                TempData["ErrorMessage"] = "حدث خطأ أثناء تحويل الطلب للمدير";
                return RedirectToAction(nameof(Index));
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> RestoreOrder(int orderId, string restoreNotes)
        {
            try
            {
                if (orderId <= 0)
                {
                    TempData["ErrorMessage"] = "يرجى اختيار رقم الطلب.";
                    return RedirectToAction(nameof(Index));
                }

                var userName = User.Identity?.Name;
                var result = await _orderService.RestoreOrderFromSupervisorsAsync(orderId, restoreNotes, userName);

                if (result.IsSuccess)
                {
                    TempData["SuccessMessage"] = result.Message;
                }
                else
                {
                    TempData["ErrorMessage"] = result.Message;
                }

                return RedirectToAction(nameof(Index));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error restoring order");
                TempData["ErrorMessage"] = "حدث خطأ أثناء استعادة الطلب";
                return RedirectToAction(nameof(Index));
            }
        }

        [HttpPost]
        public async Task<IActionResult> GetRestorableOrders(string searchTerm = "", string filter = "today")
        {
            try
            {
                var result = await _orderService.GetRestorableOrdersAsync(searchTerm, filter);
                if (!result.IsSuccess)
                {
                    return Json(new { success = false, message = result.Message });
                }

                var orders = result.Data.Select(o => new SelectListItem
                {
                    Value = o.Id.ToString(),
                    Text = $"{o.Id} | {o.EmployeeName} | {o.Department}"
                }).ToList();

                return Json(new { success = true, data = orders });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting restorable orders");
                return Json(new { success = false, message = "حدث خطأ أثناء جلب الطلبات" });
            }
        }

        [HttpPost]
        public async Task<IActionResult> GetRestoreOrderDetails(int orderId)
        {
            try
            {
                var result = await _orderService.GetRestoreOrderDetailsAsync(orderId);
                if (!result.IsSuccess)
                {
                    return Json(new { success = false, message = result.Message });
                }

                return Json(new { success = true, data = result.Data });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting restore order details");
                return Json(new { success = false, message = "حدث خطأ أثناء جلب تفاصيل الطلب" });
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> ApplyPath(int orderId, int pathNumber)
        {
            try
            {
                if (orderId <= 0)
                {
                    TempData["ErrorMessage"] = "يرجى اختيار رقم الطلب.";
                    return RedirectToAction(nameof(Index));
                }

                var userName = User.Identity?.Name;
                var result = await _orderService.ApplyPredefinedPathAsync(orderId, pathNumber, userName);

                if (result.IsSuccess)
                {
                    TempData["SuccessMessage"] = result.Message;
                }
                else
                {
                    TempData["ErrorMessage"] = result.Message;
                }

                return RedirectToAction(nameof(Index));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error applying predefined path");
                TempData["ErrorMessage"] = "حدث خطأ أثناء تطبيق المسار";
                return RedirectToAction(nameof(Index));
            }
        }

        private string CleanFileName(string fileName)
        {
            char[] invalidChars = Path.GetInvalidFileNameChars();
            foreach (char c in invalidChars)
            {
                fileName = fileName.Replace(c, '_');
            }

            const int maxLength = 100;
            if (fileName.Length > maxLength)
            {
                string extension = Path.GetExtension(fileName);
                fileName = fileName.Substring(0, maxLength - extension.Length) + extension;
            }

            return fileName;
        }
    }
}
