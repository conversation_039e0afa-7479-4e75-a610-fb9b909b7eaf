# JavaScript Organization

This directory contains all JavaScript files for the OrderFlowCore application, organized for maintainability and reusability.

## File Structure

### Core Modules
- **`orderDetailsModule.js`** - Reusable module for order details functionality
- **`shared-utils.js`** - Common utility functions used across views

### View-Specific Files
- **`directManager.js`** - JavaScript for DirectManager view
- **`assistantManager.js`** - JavaScript for AssistantManager view

### Documentation
- **`orderDetailsModule.md`** - Detailed documentation for the OrderDetailsModule
- **`test-orderDetailsModule.html`** - Test file for OrderDetailsModule functionality

## Module Dependencies

```
Views
├── shared-utils.js (Base utilities)
├── orderDetailsModule.js (Depends on shared-utils.js)
└── view-specific files (Depends on both above)
```

## Usage in Views

### Basic Setup
```html
@section Scripts {
    <script src="~/js/shared-utils.js"></script>
    <script src="~/js/orderDetailsModule.js"></script>
    <script src="~/js/your-view.js"></script>
    <script>
        // TempData messages (if any)
        @if (TempData["SuccessMessage"] != null)
        {
            <text>OrderDetailsModule.showMessage('@TempData["SuccessMessage"]', 'success');</text>
        }
    </script>
}
```

### Using Shared Utilities
```javascript
// Initialize auto-expanding textareas
SharedUtils.initAutoExpandTextareas();

// Show toast notifications
SharedUtils.showToast('Operation completed successfully', 'success');

// Format dates and numbers
const formattedDate = SharedUtils.formatDate(new Date());
const formattedNumber = SharedUtils.formatNumber(1234.56);
```

### Using OrderDetailsModule
```javascript
// Initialize with custom configuration
OrderDetailsModule.init({
    showLoading: function() { /* custom loading logic */ },
    hideLoading: function() { /* custom hide logic */ },
    showMessage: function(message, type) { /* custom message display */ }
});

// Load order details
OrderDetailsModule.loadOrderDetails(orderId, '/Controller/GetOrderDetails');

// Confirm or reject orders
OrderDetailsModule.confirmOrder(orderId, '/Controller/ConfirmOrder', 'Success message', 'Error message');
OrderDetailsModule.rejectOrder(orderId, reason, '/Controller/RejectOrder', 'Success message', 'Error message');
```

## Benefits of This Organization

1. **Separation of Concerns**: Each file has a specific responsibility
2. **Reusability**: Core modules can be used across multiple views
3. **Maintainability**: Changes to common functionality only need to be made in one place
4. **Testability**: Each module can be tested independently
5. **Performance**: Files are loaded only when needed
6. **Scalability**: Easy to add new views without duplicating code

## Adding New Views

1. Create a new view-specific JavaScript file (e.g., `newView.js`)
2. Include the required dependencies in the view:
   ```html
   <script src="~/js/shared-utils.js"></script>
   <script src="~/js/orderDetailsModule.js"></script>
   <script src="~/js/newView.js"></script>
   ```
3. Use the shared modules and utilities as needed

## Best Practices

1. **Always include shared-utils.js first** - Other modules depend on it
2. **Use the module pattern** - Keep code organized and avoid global pollution
3. **Document your code** - Include JSDoc comments for functions
4. **Handle errors gracefully** - Use try-catch blocks and provide user feedback
5. **Test your code** - Create test files for complex functionality
6. **Follow naming conventions** - Use camelCase for functions and variables
7. **Minimize DOM queries** - Cache frequently used elements

## Common Patterns

### Event Handler Setup
```javascript
document.addEventListener("DOMContentLoaded", function () {
    // Initialize modules
    OrderDetailsModule.init(config);
    
    // Set up event handlers
    document.getElementById('someButton').addEventListener('click', function() {
        // Handle click
    });
});
```

### Form Submission
```javascript
function submitForm(url, data) {
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = url;
    
    // Add anti-forgery token
    const token = document.querySelector('input[name="__RequestVerificationToken"]');
    if (token) {
        const tokenInput = document.createElement('input');
        tokenInput.type = 'hidden';
        tokenInput.name = '__RequestVerificationToken';
        tokenInput.value = token.value;
        form.appendChild(tokenInput);
    }
    
    // Add data fields
    for (const key in data) {
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = key;
        input.value = data[key];
        form.appendChild(input);
    }
    
    document.body.appendChild(form);
    form.submit();
}
```

### Loading States
```javascript
function showLoading() {
    document.getElementById('loading').style.display = '';
    document.getElementById('content').style.display = 'none';
}

function hideLoading() {
    document.getElementById('loading').style.display = 'none';
    document.getElementById('content').style.display = '';
}
``` 