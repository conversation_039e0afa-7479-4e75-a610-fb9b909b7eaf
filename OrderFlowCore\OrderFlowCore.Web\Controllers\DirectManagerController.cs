using Microsoft.AspNetCore.Mvc;
using OrderFlowCore.Application.Interfaces.Services;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Web.ViewModels;
using Microsoft.AspNetCore.Mvc.Rendering;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq;
using Microsoft.Extensions.Logging;
using System.IO.Compression;
using System.IO;

namespace OrderFlowCore.Web.Controllers
{
    public class DirectManagerController : Controller
    {
        private readonly IOrderService _orderService;
        private readonly ILogger<DirectManagerController> _logger;

        public DirectManagerController(IOrderService orderService, ILogger<DirectManagerController> logger)
        {
            _orderService = orderService;
            _logger = logger;
        }

        [HttpGet]
        public async Task<IActionResult> Index()
        {
            var viewModel = new DirectManagerViewModel();
            
            // Get pending orders for dropdown
            var ordersResult = await _orderService.GetPendingOrdersForDirectMangerAsync();
            if (ordersResult.IsSuccess)
            {
                viewModel.OrderNumbers = ordersResult.Data.Select(o => new SelectListItem
                {
                    Value = o.Id.ToString(),
                    Text = $"طلب #{o.Id} - {o.EmployeeName}"
                }).ToList();
            }
            
            return View(viewModel);
        }

        [HttpPost]
        public async Task<IActionResult> GetOrderDetails(int orderId)
        {
            var result = await _orderService.GetOrderDetailsAsync(orderId);
            if (!result.IsSuccess)
            {
                return Json(new { success = false, message = result.Message });
            }
            
            var viewModel = DirectManagerViewModel.FromOrderDetails(result.Data);
            return Json(new { success = true, data = viewModel });
        }

        [HttpPost]
        public async Task<IActionResult> ConfirmOrder(int orderId)
        {
            var userName = User.Identity?.Name;
            var result = await _orderService.ConfirmOrderByDirectManagerAsync(orderId, userName);
            if (result.IsSuccess)
            {
                TempData["SuccessMessage"] = result.Message;
            }
            else
            {
                TempData["ErrorMessage"] = result.Message;
            }
            
            return RedirectToAction(nameof(Index));
        }

        [HttpPost]
        public async Task<IActionResult> RejectOrder(int orderId, string reason)
        {
            if (string.IsNullOrWhiteSpace(reason))
            {
                TempData["ErrorMessage"] = "يجب إدخال سبب الإلغاء";
                return RedirectToAction(nameof(Index));
            }
            var userName = User.Identity?.Name;
            var result = await _orderService.RejectOrderByDirectManagerAsync(orderId, reason, userName);
            if (result.IsSuccess)
            {
                TempData["SuccessMessage"] = result.Message;
            }
            else
            {
                TempData["ErrorMessage"] = result.Message;
            }
            
            return RedirectToAction(nameof(Index));
        }

        [HttpGet]
        public async Task<IActionResult> DownloadAttachments(int orderId)
        {
            try
            {
                var result = await _orderService.GetOrderDetailsAsync(orderId);
                if (!result.IsSuccess)
                {
                    return NotFound("لم يتم العثور على الطلب");
                }

                var order = result.Data;
                bool filesFound = false;

                using (MemoryStream zipStream = new MemoryStream())
                {
                    using (ZipArchive zipArchive = new ZipArchive(zipStream, ZipArchiveMode.Create, true))
                    {
                        // Process attachments
                        var attachments = new[] { order.File1Url, order.File2Url, order.File3Url, order.File4Url };
                        
                        for (int i = 0; i < attachments.Length; i++)
                        {
                            if (!string.IsNullOrEmpty(attachments[i]))
                            {
                                try
                                {
                                    var filePath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "uploads", attachments[i]);
                                    if (System.IO.File.Exists(filePath))
                                    {
                                        var fileData = await System.IO.File.ReadAllBytesAsync(filePath);
                                        if (IsPdfFile(fileData))
                                        {
                                            filesFound = true;
                                            string fileName = CleanFileName($"مرفق_{i + 1}_طلب_{orderId}_{order.EmployeeName}.pdf");

                                            ZipArchiveEntry zipEntry = zipArchive.CreateEntry(fileName);
                                            using (Stream entryStream = zipEntry.Open())
                                            {
                                                await entryStream.WriteAsync(fileData, 0, fileData.Length);
                                            }
                                        }
                                    }
                                }
                                catch (Exception ex)
                                {
                                    _logger.LogError(ex, $"Error processing attachment {i + 1} for order {orderId}");
                                    continue;
                                }
                            }
                        }
                    }

                    if (filesFound)
                    {
                        string zipFileName = CleanFileName($"مرفقات_طلب_{orderId}_{order.EmployeeName}.zip");
                        return File(zipStream.ToArray(), "application/zip", zipFileName);
                    }
                    else
                    {
                        TempData["ErrorMessage"] = "لا توجد ملفات صالحة لتحميلها";
                        return RedirectToAction(nameof(Index));
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error downloading attachments for order {OrderId}", orderId);
                TempData["ErrorMessage"] = "حدث خطأ أثناء تحميل الملفات";
                return RedirectToAction(nameof(Index));
            }
        }

        private bool IsPdfFile(byte[] data)
        {
            if (data == null || data.Length < 4)
                return false;

            return data[0] == 0x25 && // %
                   data[1] == 0x50 && // P
                   data[2] == 0x44 && // D
                   data[3] == 0x46;   // F
        }

        private string CleanFileName(string fileName)
        {
            char[] invalidChars = Path.GetInvalidFileNameChars();
            foreach (char c in invalidChars)
            {
                fileName = fileName.Replace(c, '_');
            }

            const int maxLength = 100;
            if (fileName.Length > maxLength)
            {
                string extension = Path.GetExtension(fileName);
                fileName = fileName.Substring(0, maxLength - extension.Length) + extension;
            }

            return fileName;
        }
    }
} 