﻿using System;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Security.Cryptography;
using System.Web.UI.WebControls;
using System.Text;
using System.Linq;
using System.IO;
using System.Collections.Generic;
using System.Web.UI;



namespace abozyad
{



    public partial class ManageAccountsAndDepartments : System.Web.UI.Page
    {
        private string connectionString = ConfigurationManager.ConnectionStrings["MyConnectionString"].ConnectionString;

        protected void Page_Load(object sender, EventArgs e)
        {
            Session["UserPermission"] = "مدير حسابات";
            if (Session["UserPermission"] == null || Session["UserPermission"].ToString() != "مدير حسابات")
            {
                // Redirect to an access denied page or display an error message
                Response.Redirect("AccessDenied.aspx");
            }
            if (Session.IsNewSession)
            {
                CheckDatabaseConnection();
            }
            if (!IsPostBack)
            {
                BindAccountsGrid();
                BindDepartmentsGrid();
                PopulatePermissionDropDownList();
                UpdateStatistics(); // إضافة تحديث الإحصائيات
                CheckDatabaseConnection();
                LoadDepartments();
                LoadDistributionData();



            }
        }

        // هذه الدالة تقوم بربط البيانات في جدول الحسابات (GridViewAccounts).
        // أولاً، يتم إنشاء اتصال بقاعدة البيانات باستخدام سلسلة الاتصال (connectionString).
        // بعد ذلك، يتم استخدام استعلام SQL لجلب بيانات "اسم المستخدم"، "كلمة المرور"، و"الصلاحية" من جدول dbo.login.
        // يتم استخدام SqlDataAdapter لملء البيانات في كائن DataTable.
        // بعد ملء الجدول بالبيانات، يتم ربطه بـ GridViewAccounts لعرض البيانات في واجهة المستخدم.

        private void BindAccountsGrid(string sortExpression = "permission ASC")
        {
            try
            {
                using (SqlConnection con = new SqlConnection(connectionString))
                {
                    // تحديث الاستعلام لتضمين الحقول الجديدة
                    string query = @"SELECT 
                                username, 
                                password, 
                                permission, 
                                email, 
                                phone 
                             FROM dbo.login";

                    // إضافة ORDER الديناميكي من الباراميتر
                    if (!string.IsNullOrEmpty(sortExpression))
                    {
                        query += " ORDER BY " + sortExpression;
                    }

                    using (SqlCommand cmd = new SqlCommand(query, con))
                    {
                        con.Open();
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            DataTable dt = new DataTable();
                            dt.Load(reader);

                            GridViewAccounts.DataSource = dt;
                            GridViewAccounts.DataBind();
                        }
                    }
                }
            }
            catch (SqlException ex)
            {
                ShowError($"خطأ في قاعدة البيانات: {ex.Message}");
                // إضافة تسجيل الخطأ في السجل (Log)
            }
            catch (Exception ex)
            {
                ShowError($"خطأ عام: {ex.Message}");
            }
        }
      
        // هذه الدالة تقوم بربط البيانات في جدول الأقسام (GridViewDepartments).
        // أولاً، يتم إنشاء اتصال بقاعدة البيانات باستخدام سلسلة الاتصال (connectionString).
        // يتم استخدام استعلام SQL لجلب بيانات "الأقسام" من جدول dbo.Departments، مع تجاهل الأقسام الفارغة.
        // يتم استخدام SqlDataAdapter لملء البيانات في كائن DataTable.
        // بعد ملء الجدول بالبيانات، يتم ربطه بـ GridViewDepartments لعرض الأقسام في واجهة المستخدم.

        private void BindDepartmentsGrid()
        {
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                using (SqlCommand cmd = new SqlCommand("SELECT [الأقسام] FROM dbo.Departments WHERE [الأقسام] <> ''", con))
                {
                    using (SqlDataAdapter sda = new SqlDataAdapter(cmd))
                    {
                        DataTable dt = new DataTable();
                        sda.Fill(dt);
                        GridViewDepartments.DataSource = dt;
                        GridViewDepartments.DataBind();
                    }
                }
            }
        }

        // تقوم هذه الدالة بملء قائمة منسدلة (DropDownList) بالصلاحيات المتاحة للمستخدمين.
        // تبدأ الدالة بتفريغ العناصر الحالية في DropDownListPermission.
        // ثم تضيف عنصرًا اختياريًا يُظهر "اختر القسم" كعينة.
        // يتم إضافة خيارات مختلفة مثل "مدير الموارد البشرية" و"منسق الموارد البشرية" وغيرها من الصلاحيات المحددة.
        // بعد ذلك، يتم الاتصال بقاعدة البيانات لاسترداد قائمة الأقسام المتاحة من جدول Departments.
        // يتم استخدام استعلام SQL لجلب القيم الفريدة من عمود "الأقسام".
        // يتم إضافة كل قسم إلى DropDownList كخيار متاح للمستخدم.
        // تُستخدم هذه الدالة لتهيئة قائمة الصلاحيات عند تحميل الصفحة.

        private void PopulatePermissionDropDownList()
        {
            DropDownListPermission.Items.Clear();
            DropDownListPermission.Items.Add(new ListItem("اختر القسم", "")); // Optional: Placeholder
            DropDownListPermission.Items.Add(new ListItem("مدير الموارد البشرية", "مدير الموارد البشرية")); // Optional: Placeholder
            DropDownListPermission.Items.Add(new ListItem("منسق الموارد البشرية", "منسق الموارد البشرية")); // Optional: Placeholder
            DropDownListPermission.Items.Add(new ListItem("مساعد المدير للخدمات الطبية", "مساعد المدير للخدمات الطبية")); // Optional: Placeholder
            DropDownListPermission.Items.Add(new ListItem("مساعد المدير لخدمات التمريض", "مساعد المدير لخدمات التمريض")); // Optional: Placeholder
            DropDownListPermission.Items.Add(new ListItem("مساعد المدير للخدمات الإدارية والتشغيل", "مساعد المدير للخدمات الإدارية والتشغيل")); // Optional: Placeholder
            DropDownListPermission.Items.Add(new ListItem("مساعد المدير للموارد البشرية", "مساعد المدير للموارد البشرية")); // Optional: Placeholder
            DropDownListPermission.Items.Add(new ListItem("مدير حسابات", "مدير حسابات"));
            DropDownListPermission.Items.Add(new ListItem("", ""));
            DropDownListPermission.Items.Add(new ListItem("مشرف خدمات الموظفين", "مشرف خدمات الموظفين")); // Optional: Placeholder
            DropDownListPermission.Items.Add(new ListItem("مشرف إدارة تخطيط الموارد البشرية", "مشرف إدارة تخطيط الموارد البشرية"));
            DropDownListPermission.Items.Add(new ListItem("مشرف إدارة تقنية المعلومات", "مشرف إدارة تقنية المعلومات"));
            DropDownListPermission.Items.Add(new ListItem("مشرف مراقبة الدوام", "مشرف مراقبة الدوام"));
            DropDownListPermission.Items.Add(new ListItem("مشرف السجلات الطبية", "مشرف السجلات الطبية"));
            DropDownListPermission.Items.Add(new ListItem("مشرف إدارة الرواتب والاستحقاقات", "مشرف إدارة الرواتب والاستحقاقات"));
            DropDownListPermission.Items.Add(new ListItem("مشرف إدارة القانونية والالتزام", "مشرف إدارة القانونية والالتزام"));
            DropDownListPermission.Items.Add(new ListItem("مشرف خدمات الموارد البشرية", "مشرف خدمات الموارد البشرية"));
            DropDownListPermission.Items.Add(new ListItem("مشرف إدارة الإسكان", "مشرف إدارة الإسكان"));
            DropDownListPermission.Items.Add(new ListItem("مشرف قسم الملفات", "مشرف قسم الملفات"));
            DropDownListPermission.Items.Add(new ListItem("مشرف العيادات الخارجية", "مشرف العيادات الخارجية"));
            DropDownListPermission.Items.Add(new ListItem("مشرف التأمينات الاجتماعية", "مشرف التأمينات الاجتماعية"));
            DropDownListPermission.Items.Add(new ListItem("مشرف وحدة مراقبة المخزون", "مشرف وحدة مراقبة المخزون"));
            DropDownListPermission.Items.Add(new ListItem("مشرف إدارة تنمية الإيرادات", "مشرف إدارة تنمية الإيرادات"));
            DropDownListPermission.Items.Add(new ListItem("مشرف إدارة الأمن و السلامة", "مشرف إدارة الأمن و السلامة"));
            DropDownListPermission.Items.Add(new ListItem("مشرف الطب الاتصالي", "مشرف الطب الاتصالي"));






            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                string query = "SELECT DISTINCT [الأقسام] FROM Departments";
                using (SqlCommand command = new SqlCommand(query, connection))
                {
                    connection.Open();
                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            DropDownListPermission.Items.Add(new ListItem(reader["الأقسام"].ToString()));
                        }
                    }
                }
            }
        }




        // تقوم هذه الدالة بمعالجة حدث النقر على زر حفظ المستخدم.
        // يتم استرداد اسم المستخدم وكلمة المرور والصلاحية من حقول الإدخال في الواجهة.
        // تتحقق الدالة من ملء جميع الحقول، وإذا كان أحدها فارغًا، تعرض رسالة خطأ.
        // يتم الاتصال بقاعدة البيانات للتحقق مما إذا كان اسم المستخدم موجودًا بالفعل.
        // إذا كان اسم المستخدم موجودًا، تعرض رسالة خطأ توضح ذلك.
        // إذا كان اسم المستخدم غير موجود، يتم إدخاله مع كلمة المرور والصلاحية في جدول المستخدمين.
        // بعد ذلك، تقوم الدالة بتحديث الشبكة (Grid) التي تعرض قائمة المستخدمين وتقوم بتفريغ الحقول في واجهة المستخدم.
        // كما تقوم بإخفاء أي رسائل خطأ سابقة.

        // دالة تشفير كلمة المرور مع إضافة salt
        private string HashPassword(string password)
        {
            // إنشاء salt عشوائي
            byte[] salt = new byte[16];
            using (var rng = new RNGCryptoServiceProvider())
            {
                rng.GetBytes(salt);
            }

            // تشفير كلمة المرور مع الـ salt
            using (var pbkdf2 = new Rfc2898DeriveBytes(password, salt, 10000))
            {
                byte[] hash = pbkdf2.GetBytes(32);

                // دمج الـ salt مع الـ hash
                byte[] hashBytes = new byte[48];
                Array.Copy(salt, 0, hashBytes, 0, 16);
                Array.Copy(hash, 0, hashBytes, 16, 32);

                return Convert.ToBase64String(hashBytes);
            }
        }

        // دالة التحقق من كلمة المرور
        private bool VerifyPassword(string password, string storedHash)
        {
            try
            {
                byte[] hashBytes = Convert.FromBase64String(storedHash);

                byte[] salt = new byte[16];
                Array.Copy(hashBytes, 0, salt, 0, 16);

                using (var pbkdf2 = new Rfc2898DeriveBytes(password, salt, 10000))
                {
                    byte[] hash = pbkdf2.GetBytes(32);

                    for (int i = 0; i < 32; i++)
                    {
                        if (hashBytes[i + 16] != hash[i])
                            return false;
                    }
                }
                return true;
            }
            catch
            {
                return false;
            }
        }

        // دالة إضافة مستخدم جديد
        protected void ButtonSaveUser_Click(object sender, EventArgs e)
        {
            try
            {
                string username = TextBoxUsername.Text.Trim();
                string email = TextBoxEmail.Text.Trim();
                string phone = TextBoxPhone.Text.Trim();
                string password = TextBoxPassword.Text.Trim();
                string permission = DropDownListPermission.SelectedValue;

                // التحقق من الحقول المطلوبة - البريد والهاتف غير مطلوبين
                if (string.IsNullOrEmpty(username) || string.IsNullOrEmpty(password) || string.IsNullOrEmpty(permission))
                {
                    ShowError("يرجى ملء جميع الحقول المطلوبة (*)");
                    return;
                }

                // التحقق من طول كلمة المرور
                if (password.Length < 6)
                {
                    ShowError("يجب أن تكون كلمة المرور 6 أحرف على الأقل");
                    return;
                }

                // التحقق من البريد الإلكتروني فقط إذا تم إدخاله
                if (!string.IsNullOrEmpty(email) && !IsValidEmail(email))
                {
                    ShowError("يجب أن يكون البريد الإلكتروني بصيغة صحيحة وينتهي بـ @moh.gov.sa");
                    return;
                }

                // التحقق من رقم الهاتف فقط إذا تم إدخاله
                if (!string.IsNullOrEmpty(phone) && !phone.All(char.IsDigit))
                {
                    ShowError("يجب أن يحتوي رقم الهاتف على أرقام فقط");
                    return;
                }

                // التحقق من تكرار اسم المستخدم أو البريد الإلكتروني
                if (IsUserExists(username, email))
                {
                    ShowError("اسم المستخدم أو البريد الإلكتروني موجود مسبقاً");
                    return;
                }

                using (SqlConnection con = new SqlConnection(connectionString))
                {
                    // تشفير كلمة المرور
                    string hashedPassword = HashPassword(password);

                    // إدخال المستخدم
                    string insertQuery = @"
                INSERT INTO dbo.login 
                (username, password, permission, email, phone) 
                VALUES 
                (@Username, @Password, @Permission, 
                 CASE WHEN @Email = '' THEN NULL ELSE @Email END, 
                 CASE WHEN @Phone = '' THEN NULL ELSE @Phone END)";

                    using (SqlCommand cmd = new SqlCommand(insertQuery, con))
                    {
                        cmd.Parameters.AddWithValue("@Username", username);
                        cmd.Parameters.AddWithValue("@Password", hashedPassword);
                        cmd.Parameters.AddWithValue("@Permission", permission);
                        cmd.Parameters.AddWithValue("@Email", email); // سيتم التعامل معها في الاستعلام
                        cmd.Parameters.AddWithValue("@Phone", phone); // سيتم التعامل معها في الاستعلام

                        con.Open();
                        int rowsAffected = cmd.ExecuteNonQuery();
                        con.Close();

                        if (rowsAffected > 0)
                        {
                            // إظهار رسالة نجاح مع معلومات إضافية
                            string successMessage = "تم إضافة المستخدم بنجاح";

                            // إضافة تنبيه إذا كانت البيانات غير مكتملة
                            if (string.IsNullOrEmpty(email) || string.IsNullOrEmpty(phone))
                            {
                                successMessage += " (بيانات غير مكتملة)";
                            }

                            ShowSuccess(successMessage);
                            BindAccountsGrid();
                            ClearUserPanel();
                            UpdateStatistics();
                        }
                        else
                        {
                            ShowError("لم يتم إضافة المستخدم. لم تتأثر أي صفوف.");
                        }
                    }
                }
            }
            catch (SqlException sqlEx)
            {
                ShowError($"خطأ في قاعدة البيانات: {sqlEx.Message}");
                System.Diagnostics.Debug.WriteLine($"SQL خطأ: {sqlEx.Message}\n{sqlEx.StackTrace}");
            }
            catch (Exception ex)
            {
                ShowError($"خطأ غير متوقع: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"خطأ غير متوقع: {ex.Message}\n{ex.StackTrace}");
            }
        }

        //  دالة التحقق من التكرار
        /// <summary>
        /// التحقق من وجود مستخدم باسم المستخدم أو البريد الإلكتروني
        /// </summary>
        /// <param name="username">اسم المستخدم</param>
        /// <param name="email">البريد الإلكتروني (اختياري)</param>
        /// <returns>صحيح إذا كان المستخدم موجوداً، خطأ إذا لم يكن موجوداً</returns>
        private bool IsUserExists(string username, string email)
        {
            try
            {
                using (SqlConnection con = new SqlConnection(connectionString))
                {
                    string query;
                    SqlCommand cmd;

                    if (string.IsNullOrEmpty(email))
                    {
                        query = "SELECT COUNT(*) FROM dbo.login WHERE username = @Username";
                        cmd = new SqlCommand(query, con);
                        cmd.Parameters.AddWithValue("@Username", username);
                    }
                    else
                    {
                        query = "SELECT COUNT(*) FROM dbo.login WHERE username = @Username OR email = @Email";
                        cmd = new SqlCommand(query, con);
                        cmd.Parameters.AddWithValue("@Username", username);
                        cmd.Parameters.AddWithValue("@Email", email);
                    }

                    con.Open();
                    int count = (int)cmd.ExecuteScalar();
                    return count > 0;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في التحقق من وجود المستخدم: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// التحقق من صحة صيغة البريد الإلكتروني وأنه ينتهي بـ @moh.gov.sa
        /// </summary>
        /// <param name="email">البريد الإلكتروني المراد التحقق منه</param>
        /// <returns>صحيح إذا كان البريد صحيحاً، خطأ إذا لم يكن صحيحاً</returns>
        private bool IsValidEmail(string email)
        {
            try
            {
                if (string.IsNullOrEmpty(email))
                    return false;

                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address.Equals(email, StringComparison.OrdinalIgnoreCase)
                       && email.EndsWith("@moh.gov.sa", StringComparison.OrdinalIgnoreCase);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في التحقق من صحة البريد: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// التحقق من وجود البريد الإلكتروني في قاعدة البيانات
        /// </summary>
        /// <param name="email">البريد الإلكتروني المراد التحقق منه</param>
        /// <param name="currentUsername">اسم المستخدم الحالي (للاستثناء عند التحديث)</param>
        /// <returns>صحيح إذا كان البريد موجوداً، خطأ إذا لم يكن موجوداً</returns>
        private bool IsEmailExists(string email, string currentUsername = null)
        {
            try
            {
                if (string.IsNullOrEmpty(email))
                    return false;

                using (SqlConnection con = new SqlConnection(connectionString))
                {
                    string query;
                    SqlCommand cmd;

                    if (string.IsNullOrEmpty(currentUsername))
                    {
                        query = "SELECT COUNT(*) FROM dbo.login WHERE email = @Email";
                        cmd = new SqlCommand(query, con);
                        cmd.Parameters.AddWithValue("@Email", email);
                    }
                    else
                    {
                        query = "SELECT COUNT(*) FROM dbo.login WHERE email = @Email AND username != @Username";
                        cmd = new SqlCommand(query, con);
                        cmd.Parameters.AddWithValue("@Email", email);
                        cmd.Parameters.AddWithValue("@Username", currentUsername);
                    }

                    con.Open();
                    int count = (int)cmd.ExecuteScalar();
                    return count > 0;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في التحقق من وجود البريد: {ex.Message}");
                return false;
            }
        }
        protected void BtnEdit_Click(object sender, EventArgs e)
        {
            string username = ((Button)sender).CommandArgument;

            // جلب بيانات المستخدم
            DataTable userData = GetUserData(username);

            if (userData.Rows.Count > 0)
            {
                // تعبئة الحقول في الـ Modal
                HiddenUsername.Value = username;
                EditUsername.Text = username; 
                EditEmail.Text = userData.Rows[0]["email"].ToString();
                EditPhone.Text = userData.Rows[0]["phone"].ToString();

                // فتح النافذة عبر JavaScript
                ScriptManager.RegisterStartupScript(
        this,
        GetType(),
        "ShowEditModal",
        "var modal = new bootstrap.Modal(document.getElementById('editModal'), {}); modal.show();",
        true
    );
            }
        }
        private DataTable GetUserData(string username)
        {
            string query = "SELECT email, phone FROM dbo.login WHERE username = @Username";

            using (SqlConnection con = new SqlConnection(connectionString))
            using (SqlCommand cmd = new SqlCommand(query, con))
            {
                cmd.Parameters.AddWithValue("@Username", username);
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                DataTable dt = new DataTable();
                da.Fill(dt);
                return dt;
            }
        }
        protected void BtnSaveEdit_Click(object sender, EventArgs e)
        {
            try
            {
                string oldUsername = HiddenUsername.Value;
                string newUsername = EditUsername.Text.Trim();
                string email = EditEmail.Text.Trim();
                string phone = EditPhone.Text.Trim();

                // التحقق من وجود اسم المستخدم
                if (string.IsNullOrEmpty(newUsername))
                {
                    ShowError("يجب إدخال اسم المستخدم");
                    return;
                }

                // التحقق من البريد الإلكتروني (جعله اختيارياً)
                if (!string.IsNullOrEmpty(email) && !IsValidEmail(email))
                {
                    ShowError("يجب أن يكون البريد الإلكتروني بصيغة صحيحة");
                    return;
                }

                // التحقق من رقم الهاتف (جعله اختيارياً)
                if (!string.IsNullOrEmpty(phone) && !phone.All(char.IsDigit))
                {
                    ShowError("يجب أن يحتوي رقم الهاتف على أرقام فقط");
                    return;
                }

                // التحقق من عدم وجود اسم مستخدم مكرر (إذا تم تغييره)
                if (newUsername != oldUsername && IsUsernameExists(newUsername))
                {
                    ShowError("اسم المستخدم موجود بالفعل");
                    return;
                }

                // التحقق من عدم وجود بريد إلكتروني مكرر
                if (!string.IsNullOrEmpty(email) && IsEmailExists(email, oldUsername))
                {
                    ShowError("البريد الإلكتروني مستخدم من قبل مستخدم آخر");
                    return;
                }

                using (SqlConnection con = new SqlConnection(connectionString))
                {
                    string updateQuery = @"UPDATE dbo.login 
                      SET username = @NewUsername, 
                          email = @Email, 
                          phone = @Phone 
                      WHERE username = @OldUsername";

                    using (SqlCommand cmd = new SqlCommand(updateQuery, con))
                    {
                        cmd.Parameters.AddWithValue("@NewUsername", newUsername);
                        cmd.Parameters.AddWithValue("@Email", string.IsNullOrEmpty(email) ? DBNull.Value : (object)email);
                        cmd.Parameters.AddWithValue("@Phone", string.IsNullOrEmpty(phone) ? DBNull.Value : (object)phone);
                        cmd.Parameters.AddWithValue("@OldUsername", oldUsername);

                        con.Open();
                        int rowsAffected = cmd.ExecuteNonQuery();
                        con.Close();

                        if (rowsAffected > 0)
                        {
                            ShowSuccess("تم التحديث بنجاح");
                            BindAccountsGrid(); // إعادة تحميل الجدول
                        }
                        else
                        {
                            ShowError("لم يتم تحديث أي بيانات");
                        }
                    }
                }
            }
            catch (SqlException sqlEx)
            {
                ShowError($"خطأ في قاعدة البيانات: {sqlEx.Message}");
            }
            catch (Exception ex)
            {
                ShowError($"خطأ غير متوقع: {ex.Message}");
            }
        }
        // للتحقق من وجود اسم المستخدم مكرر
        private bool IsUsernameExists(string username)
        {
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                string query = "SELECT COUNT(*) FROM dbo.login WHERE username = @Username";
                using (SqlCommand cmd = new SqlCommand(query, con))
                {
                    cmd.Parameters.AddWithValue("@Username", username);
                    con.Open();
                    int count = (int)cmd.ExecuteScalar();
                    return count > 0;
                }
            }
        }
        private void CheckDatabaseConnection()
        {
            try
            {
                using (SqlConnection con = new SqlConnection(connectionString))
                {
                    con.Open();
                    ShowSuccess("تم الاتصال بقاعدة البيانات بنجاح");
                }
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في الاتصال بقاعدة البيانات: {ex.Message}");
            }
        }
        // دالة إظهار رسالة خطأ
        private void ShowError(string message)
        {
            litReport.Text = $"<div class='text-danger'>{message}</div>";
            LabelError.Text = message.Replace("\n", "<br/>");
            LabelError.CssClass = "alert alert-error";
            LabelError.Visible = true;
        }

        // دالة إظهار رسالة نجاح
        private void ShowSuccess(string message)
        {
            litReport.Text = $"<div class='text-success'>{message}</div>";
            LabelError.Text = message.Replace("\n", "<br/>");
            LabelError.CssClass = "alert alert-success";
            LabelError.Visible = true;
        }


        // دالة مسح حقول النموذج
        private void ClearUserPanel()
        {
            TextBoxUsername.Text = "";
            TextBoxEmail.Text = ""; 
            TextBoxPhone.Text = ""; 
            TextBoxPassword.Text = "";
            DropDownListPermission.SelectedIndex = 0;
        }



        // تقوم هذه الدالة بمعالجة حدث النقر على زر حفظ القسم.
        // يتم استرداد اسم القسم من حقل الإدخال في الواجهة.
        // تتحقق الدالة من أن حقل القسم غير فارغ، وإذا كان فارغًا، تعرض رسالة خطأ.
        // تتصل الدالة بقاعدة البيانات للتحقق مما إذا كان القسم موجودًا بالفعل.
        // إذا كان القسم موجودًا، تعرض رسالة خطأ توضح ذلك.
        // إذا كان القسم غير موجود، يتم إدخاله في جدول الأقسام في قاعدة البيانات.
        // بعد ذلك، تقوم الدالة بتحديث الشبكة (Grid) التي تعرض قائمة الأقسام وتقوم بتفريغ الحقول في واجهة المستخدم.
        // كما تقوم بإخفاء أي رسائل خطأ سابقة.

        protected void ButtonSaveDepartment_Click(object sender, EventArgs e)
        {
            string dep = TextBoxDepartment.Text.Trim();

            // التحقق من إدخال القسم
            if (string.IsNullOrEmpty(dep))
            {
                LabelDepartmentError.Text = "يرجى ملء جميع الحقول.";
                LabelDepartmentError.Style["display"] = "block";
                return;
            }

            using (SqlConnection con = new SqlConnection(connectionString))
            {
                // التحقق مما إذا كان القسم موجودًا
                using (SqlCommand checkCmd = new SqlCommand("SELECT COUNT(*) FROM dbo.Departments WHERE [الأقسام] = @Dep", con))
                {
                    checkCmd.Parameters.AddWithValue("@Dep", dep);
                    con.Open();
                    int departmentCount = (int)checkCmd.ExecuteScalar();
                    con.Close();

                    if (departmentCount > 0)
                    {
                        LabelDepartmentError.Text = "القسم موجود بالفعل.";
                        LabelDepartmentError.Style["display"] = "block";
                        return;
                    }
                }

                // إضافة القسم الجديد مع تعيين AssistantManagerID إلى NULL
                string query = "INSERT INTO dbo.Departments ([الأقسام], [AssistantManagerID]) VALUES (@Dep, NULL)";
                using (SqlCommand cmd = new SqlCommand(query, con))
                {
                    cmd.Parameters.AddWithValue("@Dep", dep);
                    con.Open();
                    cmd.ExecuteNonQuery();
                    con.Close();
                }
            }

            // إعادة تحميل الصفحة لتحديث القوائم المنسدلة
            Response.Redirect(Request.RawUrl);
        }


        // تقوم هذه الدالة بمعالجة حدث حذف صف من GridView لحسابات المستخدمين.
        // تستخرج اسم المستخدم من البيانات المخزنة في الصف المحذوف.
        // تتصل الدالة بقاعدة البيانات وتنفذ استعلام حذف لإزالة المستخدم من جدول تسجيل الدخول.
        // بعد تنفيذ عملية الحذف، تقوم بتحديث الشبكة (Grid) التي تعرض قائمة الحسابات لتظهر التغييرات الجديدة.

        protected void GridViewAccounts_RowDeleting(object sender, GridViewDeleteEventArgs e)
        {
            string username = GridViewAccounts.DataKeys[e.RowIndex].Value.ToString();

            using (SqlConnection con = new SqlConnection(connectionString))
            {
                using (SqlCommand cmd = new SqlCommand("DELETE FROM dbo.login WHERE username=@Username", con))
                {
                    cmd.Parameters.AddWithValue("@Username", username);
                    con.Open();
                    cmd.ExecuteNonQuery();
                    con.Close();
                }
            }

            BindAccountsGrid();
        }

        // تقوم هذه الدالة بمعالجة حدث حذف صف من GridView الخاص بالأقسام.
        // تستخرج اسم القسم من البيانات المخزنة في الصف المحذوف.
        // تتصل الدالة بقاعدة البيانات وتنفذ استعلام حذف لإزالة القسم من جدول الأقسام.
        // بعد تنفيذ عملية الحذف، تقوم بتحديث الشبكة (Grid) التي تعرض قائمة الأقسام لتظهر التغييرات الجديدة.

        protected void GridViewDepartments_RowDeleting(object sender, GridViewDeleteEventArgs e)
        {
            string dep = GridViewDepartments.DataKeys[e.RowIndex].Value.ToString();

            using (SqlConnection con = new SqlConnection(connectionString))
            {
                // إعداد استعلام حذف القسم من جدول Departments
                using (SqlCommand cmd = new SqlCommand("DELETE FROM dbo.Departments WHERE [الأقسام]=@Dep", con))
                {
                    // إضافة اسم القسم كمعامل للاستعلام
                    cmd.Parameters.AddWithValue("@Dep", dep);
                    con.Open();// فتح الاتصال
                    cmd.ExecuteNonQuery();// تنفيذ الاستعلام
                    con.Close();// إغلاق الاتصال
                }
            }
            // تحديث GridView لعرض البيانات بعد الحذف
            BindDepartmentsGrid();
            // إعادة تحميل الصفحة لتحديث المعلومات
            Response.Redirect(Request.RawUrl);
        }



        private void ClearDepartmentPanel()
        {
            TextBoxDepartment.Text = "";
        }

        // تقوم هذه الدالة بالبحث في جدول معين باستخدام مصطلح بحث محدد.
        // تأخذ اسم الجدول، اسم العمود، مصطلح البحث، GridView لعرض النتائج، وتسمية Label لعرض رسائل عدم وجود نتائج.
        // إذا كان مصطلح البحث غير فارغ، يتم إنشاء استعلام SQL للبحث عن الصفوف التي تحتوي على مصطلح البحث في العمود المحدد.
        // بعد تنفيذ الاستعلام، يتم تعبئة GridView بالنتائج. 
        // إذا لم توجد أي نتائج، يتم إظهار رسالة تفيد بعدم وجود نتائج.
        // إذا كان مصطلح البحث فارغًا، يتم إظهار رسالة تطلب إدخال نص للبحث.

        private void SearchInTable(string tableName, string columnName, string searchTerm, GridView gridView, Label noResultsLabel)
        {
            if (!string.IsNullOrEmpty(searchTerm))
            {
                using (SqlConnection con = new SqlConnection(connectionString))
                {
                    string query = $"SELECT * FROM {tableName} WHERE {columnName} LIKE '%' + @SearchTerm + '%'";
                    using (SqlCommand cmd = new SqlCommand(query, con))
                    {
                        cmd.Parameters.AddWithValue("@SearchTerm", searchTerm);
                        using (SqlDataAdapter sda = new SqlDataAdapter(cmd))
                        {
                            DataTable dt = new DataTable();
                            sda.Fill(dt);

                            if (dt.Rows.Count > 0)
                            {
                                gridView.DataSource = dt;
                                gridView.DataBind();
                                noResultsLabel.Visible = false; // إخفاء رسالة "لا توجد نتائج"
                            }
                            else
                            {
                                gridView.DataSource = null;
                                gridView.DataBind();
                                noResultsLabel.Text = "لا توجد نتائج.";
                                noResultsLabel.Visible = true; // إظهار رسالة "لا توجد نتائج"
                            }
                        }
                    }
                }
            }
            else
            {
                gridView.DataSource = null;
                gridView.DataBind();
                noResultsLabel.Text = "يرجى إدخال نص للبحث.";
                noResultsLabel.Visible = true; // إظهار رسالة "يرجى إدخال نص"
            }
        }







        protected void GridViewAccounts_Sorting(object sender, GridViewSortEventArgs e)
        {
            string sortExpression = e.SortExpression;

            // التبديل بين الترتيب التصاعدي والتنازلي
            if (ViewState["SortDir"] == null || ViewState["SortDir"].ToString() == "ASC")
            {
                ViewState["SortDir"] = "DESC";
            }
            else
            {
                ViewState["SortDir"] = "ASC";
            }

            BindAccountsGrid($"{sortExpression} {ViewState["SortDir"]}");
        }

        // تحديث دالة البحث لتحافظ على الترتيب
        protected void ButtonSearch_Click(object sender, EventArgs e)
        {
            string searchTerm = TextBoxSearch.Text.Trim();
            if (!string.IsNullOrEmpty(searchTerm))
            {
                using (SqlConnection con = new SqlConnection(connectionString))
                {
                    string query = @"SELECT username, password, permission 
                           FROM dbo.login 
                           WHERE username LIKE '%' + @SearchTerm + '%' 
                           ORDER BY permission ASC";

                    using (SqlCommand cmd = new SqlCommand(query, con))
                    {
                        cmd.Parameters.AddWithValue("@SearchTerm", searchTerm);
                        using (SqlDataAdapter sda = new SqlDataAdapter(cmd))
                        {
                            DataTable dt = new DataTable();
                            sda.Fill(dt);

                            if (dt.Rows.Count > 0)
                            {
                                GridViewAccounts.DataSource = dt;
                                GridViewAccounts.DataBind();
                                LabelNoResultsUser.Visible = false;
                            }
                            else
                            {
                                GridViewAccounts.DataSource = null;
                                GridViewAccounts.DataBind();
                                LabelNoResultsUser.Text = "لا توجد نتائج.";
                                LabelNoResultsUser.Visible = true;
                            }
                        }
                    }
                }
            }
            else
            {
                BindAccountsGrid(); // استخدام الترتيب الافتراضي
            }
        }


        // دالة إلغاء البحث للمستخدمين
        protected void ButtonClearSearch_Click(object sender, EventArgs e)
        {
            TextBoxSearch.Text = string.Empty;
            BindAccountsGrid();
            LabelNoResultsUser.Visible = false;
        }


        // دالة البحث للأقسام
        protected void ButtonSearchDepartment_Click(object sender, EventArgs e)
        {
            string searchTerm = TextBoxSearchDepartment.Text.Trim();
            SearchInTable("dbo.Departments", "[الأقسام]", searchTerm, GridViewDepartments, LabelNoResultsDepartment);
        }


        // دالة إلغاء البحث للأقسام
        protected void ButtonClearSearchDepartment_Click(object sender, EventArgs e)
        {
            TextBoxSearchDepartment.Text = string.Empty;
            BindDepartmentsGrid();
            LabelNoResultsDepartment.Visible = false;
        }
        private void UpdateStatistics()
        {
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                con.Open();

                // إجمالي المستخدمين
                using (SqlCommand cmd = new SqlCommand(@"
            SELECT COUNT(*) 
            FROM dbo.login 
            WHERE username != 'Super Admin'", con))
                {
                    int totalUsers = (int)cmd.ExecuteScalar();
                    LabelTotalUsers.Text = totalUsers.ToString();
                }

                // إجمالي الأقسام
                using (SqlCommand cmd = new SqlCommand(@"
            SELECT COUNT(*) 
            FROM dbo.Departments 
            WHERE [الأقسام] <> ''", con))
                {
                    int totalDepartments = (int)cmd.ExecuteScalar();
                    LabelTotalDepartments.Text = totalDepartments.ToString();
                }
                // إجمالي المشرفين
                using (SqlCommand cmd = new SqlCommand("SELECT COUNT(*) FROM dbo.login WHERE permission LIKE N'مشرف%' AND username != 'Super Admin'", con))
                {
                    int totalSupervisors = (int)cmd.ExecuteScalar();
                    LabelTotalSupervisors.Text = totalSupervisors.ToString();
                }



                // إحصائيات القسم والمستخدمين مع إضافة مساعد المدير
                string statsQuery = @"
WITH AllDepartments AS (
    -- الحصول على جميع الأقسام الفريدة من كلا الجدولين
    SELECT [الأقسام] as DepartmentName
    FROM dbo.Departments
    WHERE [الأقسام] <> ''
    UNION
    SELECT DISTINCT permission as DepartmentName
    FROM dbo.login
    WHERE permission <> '' AND username != 'Super Admin'
)
SELECT 
    d.DepartmentName as DepartmentName,
    CASE 
        WHEN dept.AssistantManagerID = 'A1' THEN N'مساعد المدير للخدمات الطبية'
        WHEN dept.AssistantManagerID = 'A2' THEN N'مساعد المدير لخدمات التمريض'
        WHEN dept.AssistantManagerID = 'A3' THEN N'مساعد المدير للخدمات الإدارية والتشغيل'
        WHEN dept.AssistantManagerID = 'A4' THEN N'مساعد المدير للموارد البشرية'
        WHEN dept.AssistantManagerID = 'B' THEN N'المدير العام'
        ELSE N'غير معين'
    END AS AssistantManager,
    COUNT(DISTINCT l.username) as UserCount,
    COALESCE(
        STUFF(( 
            SELECT ', ' + l2.username
            FROM dbo.login l2
            WHERE l2.permission = d.DepartmentName
                AND l2.username != 'Super Admin'
            FOR XML PATH(''), TYPE).value('.', 'NVARCHAR(MAX)'), 1, 2, ''),
        N'لا يوجد'
    ) as UserNames
FROM AllDepartments d
LEFT JOIN dbo.Departments dept ON d.DepartmentName = dept.[الأقسام]
LEFT JOIN dbo.login l ON d.DepartmentName = l.permission
    AND l.username != 'Super Admin'
GROUP BY d.DepartmentName, dept.AssistantManagerID
ORDER BY d.DepartmentName";

                using (SqlCommand cmd = new SqlCommand(statsQuery, con))
                {
                    using (SqlDataAdapter sda = new SqlDataAdapter(cmd))
                    {
                        DataTable dt = new DataTable();
                        sda.Fill(dt);
                        GridViewDepartmentStats.DataSource = dt;
                        GridViewDepartmentStats.DataBind();
                    }
                }

                // إحصائيات المشرفين - استعلام محسن يظهر جميع أدوار المشرفين
                string supervisorStatsQuery = @"
WITH AllSupervisorRoles AS (
    -- الحصول على جميع أدوار المشرفين المحتملة
    SELECT permission as SupervisorRole
    FROM dbo.login 
    WHERE permission LIKE N'مشرف%'
        AND username != 'Super Admin'
    UNION
    -- إضافة قائمة ثابتة من أدوار المشرفين المعروفة
    SELECT value FROM (VALUES
        (N'مشرف خدمات الموظفين'),
        (N'مشرف إدارة تخطيط الموارد البشرية'),
        (N'مشرف إدارة تقنية المعلومات'),
        (N'مشرف مراقبة الدوام'),
        (N'مشرف السجلات الطبية'),
        (N'مشرف إدارة الرواتب والاستحقاقات'),
        (N'مشرف إدارة القانونية والالتزام'),
        (N'مشرف خدمات الموارد البشرية'),
        (N'مشرف إدارة الإسكان'),
        (N'مشرف قسم الملفات'),
        (N'مشرف العيادات الخارجية'),
        (N'مشرف التأمينات الاجتماعية'),
        (N'مشرف وحدة مراقبة المخزون'),
        (N'مشرف إدارة تنمية الإيرادات'),
        (N'مشرف إدارة الأمن و السلامة'),
        (N'مشرف الطب الاتصالي')
    ) AS StaticRoles(value)
)
SELECT 
    r.SupervisorRole,
    COUNT(DISTINCT l.username) as SupervisorCount,
    COALESCE(
        STUFF(( 
            SELECT ', ' + username
            FROM dbo.login l2
            WHERE l2.permission = r.SupervisorRole
                AND l2.username != 'Super Admin'
            FOR XML PATH(''), TYPE).value('.', 'NVARCHAR(MAX)'), 1, 2, ''),
        N'لا يوجد'
    ) as SupervisorNames
FROM AllSupervisorRoles r
LEFT JOIN dbo.login l ON r.SupervisorRole = l.permission
    AND l.username != 'Super Admin'
GROUP BY r.SupervisorRole
ORDER BY r.SupervisorRole";

                using (SqlCommand cmd = new SqlCommand(supervisorStatsQuery, con))
                {
                    using (SqlDataAdapter sda = new SqlDataAdapter(cmd))
                    {
                        DataTable dt = new DataTable();
                        sda.Fill(dt);
                        GridViewSupervisorStats.DataSource = dt;
                        GridViewSupervisorStats.DataBind();
                    }
                }
            }
        }
        protected void btnSave_Click(object sender, EventArgs e)
        {
            try
            {
                // تحديد مصدر الطلب (زر التعديل الفردي أو الجماعي)
                Button clickedButton = (Button)sender;
                bool isBulkAssign = (clickedButton.ID == "btnBulkAssign");

                // التحقق من اختيار مساعد المدير المناسب
                string assistantManagerId;
                if (isBulkAssign)
                {
                    assistantManagerId = ddlAssistantManagersBulk.SelectedValue;
                }
                else
                {
                    assistantManagerId = ddlAssistantManagers.SelectedValue;
                }

                if (assistantManagerId == "0")
                {
                    lblMessage.Text = "يرجى اختيار مساعد مدير!";
                    lblMessage.CssClass = "text-danger";
                    return;
                }

                using (SqlConnection conn = new SqlConnection(connectionString))
                {
                    conn.Open();

                    if (isBulkAssign)
                    {
                        // معالجة التعديل الجماعي
                        string[] selectedDepartments = lstDepartments.GetSelectedIndices()
                            .Select(i => lstDepartments.Items[i].Value)
                            .ToArray();

                        if (selectedDepartments.Length == 0)
                        {
                            lblMessage.Text = "يرجى اختيار قسم واحد على الأقل";
                            lblMessage.CssClass = "text-danger";
                            return;
                        }

                        foreach (string dept in selectedDepartments)
                        {
                            UpdateDepartmentManager(conn, dept, assistantManagerId);
                        }

                        lblMessage.Text = "تم تحديث توزيع الأقسام بنجاح";
                    }
                    else
                    {
                        // معالجة التعديل الفردي
                        string departmentName = ddlDepartments.SelectedValue;
                        if (departmentName == "0")
                        {
                            lblMessage.Text = "يرجى اختيار قسم صحيح!";
                            lblMessage.CssClass = "text-danger";
                            return;
                        }

                        UpdateDepartmentManager(conn, departmentName, assistantManagerId);
                        lblMessage.Text = "تم حفظ التعديلات بنجاح!";
                    }

                    lblMessage.CssClass = "text-success";
                }

                // تحديث العرض
                LoadDistributionData();
            }
            catch (Exception ex)
            {
                lblMessage.Text = "حدث خطأ أثناء حفظ التعديلات. الرجاء المحاولة مرة أخرى.";
                lblMessage.CssClass = "text-danger";

                // لطباعة تفاصيل الخطأ أثناء التطوير
                Console.WriteLine(ex.Message);
            }
        }


        // دالة مساعدة لتحديث مدير القسم
        private void UpdateDepartmentManager(SqlConnection conn, string departmentName, string assistantManagerId)
        {
            string query;
            if (assistantManagerId == "0")
            {
                query = "UPDATE Departments SET AssistantManagerID = NULL WHERE [الأقسام] = @DepartmentName";
            }
            else
            {
                query = "UPDATE Departments SET AssistantManagerID = @AssistantManagerID WHERE [الأقسام] = @DepartmentName";
            }

            using (SqlCommand cmd = new SqlCommand(query, conn))
            {
                cmd.Parameters.AddWithValue("@DepartmentName", departmentName);
                if (assistantManagerId != "0")
                {
                    cmd.Parameters.AddWithValue("@AssistantManagerID", assistantManagerId);
                }
                cmd.ExecuteNonQuery();
            }
        }


        private void LoadDepartments()
        {
            string query = "SELECT [الأقسام] FROM [dbo].[Departments] ORDER BY [الأقسام]";
            using (SqlConnection conn = new SqlConnection(connectionString))
            {
                SqlCommand cmd = new SqlCommand(query, conn);
                conn.Open();
                using (SqlDataReader reader = cmd.ExecuteReader())
                {
                    // تحميل البيانات في DropDownList
                    DataTable dt = new DataTable();
                    dt.Load(reader);

                    // تعيين مصدر البيانات لـ DropDownList
                    ddlDepartments.DataSource = dt;
                    ddlDepartments.DataTextField = "الأقسام";
                    ddlDepartments.DataValueField = "الأقسام";
                    ddlDepartments.DataBind();
                    ddlDepartments.Items.Insert(0, new ListItem("-- اختر القسم --", "0"));

                    // تحميل نفس البيانات في ListBox
                    lstDepartments.DataSource = dt;
                    lstDepartments.DataTextField = "الأقسام";
                    lstDepartments.DataValueField = "الأقسام";
                    lstDepartments.DataBind();
                }
            }
        }
        private void LoadDistributionData()
        {
            // تعريف CASE الخاص بمساعدي المدير للاستخدام في الاستعلامات
            string assistantManagerCase = @"
CASE 
    WHEN [AssistantManagerID] = 'A1' THEN 'مساعد المدير للخدمات الطبية'
    WHEN [AssistantManagerID] = 'A2' THEN 'مساعد المدير لخدمات التمريض'
    WHEN [AssistantManagerID] = 'A3' THEN 'مساعد المدير للخدمات الإدارية والتشغيل'
    WHEN [AssistantManagerID] = 'A4' THEN 'مساعد المدير للموارد البشرية'
    WHEN [AssistantManagerID] = 'B' THEN 'المدير العام'
    ELSE 'غير معين'
END";


            using (SqlConnection conn = new SqlConnection(connectionString))
            {
                conn.Open();

                // استعلام التوزيع التفصيلي
                string distributionQuery = $@"
      SELECT d.[الأقسام], 
             {assistantManagerCase} AS AssistantManager
      FROM dbo.Departments d
      ORDER BY d.[الأقسام]";

                using (SqlCommand cmd = new SqlCommand(distributionQuery, conn))
                {
                    using (SqlDataReader reader = cmd.ExecuteReader())
                    {
                        DataTable dtDistribution = new DataTable();
                        dtDistribution.Load(reader);
                        GridViewDistribution.DataSource = dtDistribution;
                        GridViewDistribution.DataBind();
                    }
                }

                // استعلام الإحصائيات
                string statsQuery = $@"
      SELECT 
          {assistantManagerCase} AS AssistantManager,
          COUNT(*) as DepartmentCount
      FROM Departments
      GROUP BY AssistantManagerID
      ORDER BY 
          CASE 
              WHEN AssistantManagerID IS NULL THEN 1 
              ELSE 0 
          END,
          AssistantManagerID";

                using (SqlCommand cmd = new SqlCommand(statsQuery, conn))
                {
                    using (SqlDataReader reader = cmd.ExecuteReader())
                    {
                        DataTable dtStats = new DataTable();
                        dtStats.Load(reader);
                        GridViewStats.DataSource = dtStats;
                        GridViewStats.DataBind();
                    }
                }
            }

            // تطبيق التلوين على الصفوف غير المعينة
            foreach (GridViewRow row in GridViewDistribution.Rows)
            {
                if (row.RowType == DataControlRowType.DataRow)
                {
                    string assistantManager = row.Cells[1].Text;
                    if (assistantManager == "غير معين")
                    {
                        row.BackColor = System.Drawing.Color.LightYellow;
                    }
                }
            }
        }

        protected void btnBulkAssign_Click(object sender, EventArgs e)
        {
            btnSave_Click(sender, e);
        }


        protected void btnImportDeptCSV_Click(object sender, EventArgs e)
        {
            if (!FileUploadControl.HasFile)
            {
                ShowError("الرجاء اختيار ملف للاستيراد");
                return;
            }

            try
            {
                // التحقق من نوع الملف
                string fileExtension = Path.GetExtension(FileUploadControl.FileName).ToLower();
                if (fileExtension != ".csv")
                {
                    ShowError("الرجاء اختيار ملف CSV فقط");
                    return;
                }

                using (StreamReader sr = new StreamReader(FileUploadControl.FileContent, Encoding.UTF8))
                {
                    int successCount = 0;
                    int errorCount = 0;
                    bool isFirstLine = true;
                    StringBuilder report = new StringBuilder();
                    HashSet<string> existingDepartments = new HashSet<string>();

                    using (SqlConnection con = new SqlConnection(connectionString))
                    {
                        con.Open();
                        using (SqlTransaction transaction = con.BeginTransaction())
                        {
                            try
                            {
                                string line;
                                int lineNumber = 0;

                                while ((line = sr.ReadLine()) != null)
                                {
                                    lineNumber++;
                                    if (isFirstLine)
                                    {
                                        isFirstLine = false;
                                        continue;
                                    }

                                    if (string.IsNullOrWhiteSpace(line))
                                        continue;

                                    string[] fields = line.Split(';');
                                    if (fields.Length < 2)
                                    {
                                        errorCount++;
                                        report.AppendLine($"<div class='text-danger'>سطر {lineNumber}: لا يحتوي على جميع الحقول المطلوبة</div>");
                                        continue;
                                    }

                                    string departmentName = fields[0].Trim();
                                    string assistantManagerID = fields[1].Trim();

                                    // التحقق من عدم ترك الحقول فارغة
                                    if (string.IsNullOrWhiteSpace(departmentName) || string.IsNullOrWhiteSpace(assistantManagerID))
                                    {
                                        errorCount++;
                                        report.AppendLine($"<div class='text-danger'>سطر {lineNumber}: يحتوي على حقول فارغة</div>");
                                        continue;
                                    }

                                    // التحقق من وجود القسم
                                    string checkQuery = "SELECT COUNT(*) FROM Departments WHERE [الأقسام] = @DepartmentName";
                                    using (SqlCommand checkCmd = new SqlCommand(checkQuery, con, transaction))
                                    {
                                        checkCmd.Parameters.AddWithValue("@DepartmentName", departmentName);
                                        if ((int)checkCmd.ExecuteScalar() > 0)
                                        {
                                            errorCount++;
                                            report.AppendLine($"<div class='text-warning'>سطر {lineNumber}: القسم موجود مسبقاً - {departmentName}</div>");
                                            continue;
                                        }
                                    }

                                    // إدخال القسم
                                    string insertQuery = @"INSERT INTO Departments ([الأقسام], [AssistantManagerID]) 
                                                VALUES (@DepartmentName, @AssistantManagerID)";
                                    using (SqlCommand cmd = new SqlCommand(insertQuery, con, transaction))
                                    {
                                        cmd.Parameters.AddWithValue("@DepartmentName", departmentName);
                                        cmd.Parameters.AddWithValue("@AssistantManagerID", assistantManagerID);

                                        cmd.ExecuteNonQuery();
                                        successCount++;
                                        report.AppendLine($"<div class='text-success'>تم استيراد القسم: {departmentName}</div>");
                                    }
                                }

                                if (successCount > 0)
                                {
                                    transaction.Commit();
                                    report.Insert(0, $"<div class='fw-bold mb-2'>تم استيراد {successCount} قسم بنجاح.</div>");
                                    if (errorCount > 0)
                                    {
                                        report.Insert(0, $"<div class='text-warning mb-2'>فشل استيراد {errorCount} قسم.</div>");
                                    }
                                }
                                else
                                {
                                    transaction.Rollback();
                                    report.Insert(0, "<div class='text-danger fw-bold mb-2'>لم يتم استيراد أي أقسام.</div>");
                                }

                                // عرض التقرير
                                litReport.Text = $"<div class='report-container'>{report.ToString()}</div>";
                                reportPanel.Visible = true;

                                // تحديث البيانات في الواجهة
                                LoadDepartments();
                            }
                            catch (Exception ex)
                            {
                                transaction.Rollback();
                                throw new Exception($"خطأ في معالجة الملف: {ex.Message}");
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في استيراد الملف: {ex.Message}");
            }
        }



        protected void btnExportDeptCSV_Click(object sender, EventArgs e)
        {
            try
            {
                using (SqlConnection con = new SqlConnection(connectionString))
                {
                    string query = "SELECT [الأقسام], [AssistantManagerID] FROM Departments";

                    using (SqlCommand cmd = new SqlCommand(query, con))
                    {
                        con.Open();

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            StringBuilder sb = new StringBuilder();

                            // إضافة عناوين الأعمدة
                            sb.AppendLine("اسم القسم;معرف المساعد");

                            // إضافة البيانات
                            while (reader.Read())
                            {
                                sb.AppendLine($"{reader["الأقسام"]};{reader["AssistantManagerID"]}");
                            }

                            Response.Clear();
                            Response.Buffer = true;
                            Response.AddHeader("content-disposition", "attachment;filename=Departments.csv");
                            Response.Charset = "";
                            Response.ContentType = "application/text";
                            Response.ContentEncoding = System.Text.Encoding.UTF8;
                            Response.Output.Write("\uFEFF"); // BOM للدعم العربي
                            Response.Output.Write(sb.ToString());
                            Response.Flush();
                            Response.End();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                ShowError("خطأ في تصدير البيانات: " + ex.Message);
            }
        }

        protected void btnDeleteAllDepts_Click(object sender, EventArgs e)
        {
            try
            {
                using (SqlConnection con = new SqlConnection(connectionString))
                {
                    con.Open();

                    string deleteQuery = "DELETE FROM Departments";

                    using (SqlCommand cmd = new SqlCommand(deleteQuery, con))
                    {
                        int rowsAffected = cmd.ExecuteNonQuery();

                        if (rowsAffected > 0)
                        {
                            ShowSuccess($"تم حذف جميع الأقسام بنجاح. عدد الأقسام المحذوفة: {rowsAffected}");
                            LoadDepartments(); // تحديث العرض
                        }
                        else
                        {
                            ShowError("لا توجد أقسام لحذفها.");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                ShowError("حدث خطأ أثناء عملية الحذف: " + ex.Message);
            }
        }
        // دالة استيراد الحسابات من ملف CSV
        protected void btnImportAccountsCSV_Click(object sender, EventArgs e)
        {
            if (!AccountFileUpload.HasFile)
            {
                ShowError("الرجاء اختيار ملف للاستيراد");
                return;
            }

            try
            {
                string fileExtension = Path.GetExtension(AccountFileUpload.FileName).ToLower();
                if (fileExtension != ".csv")
                {
                    ShowError("الرجاء اختيار ملف CSV فقط");
                    return;
                }

                using (StreamReader sr = new StreamReader(AccountFileUpload.FileContent, Encoding.UTF8))
                {
                    int successCount = 0;
                    int errorCount = 0;
                    bool isFirstLine = true;
                    StringBuilder report = new StringBuilder();

                    using (SqlConnection con = new SqlConnection(connectionString))
                    {
                        con.Open();
                        using (SqlTransaction transaction = con.BeginTransaction())
                        {
                            try
                            {
                                string line;
                                int lineNumber = 0;

                                while ((line = sr.ReadLine()) != null)
                                {
                                    lineNumber++;
                                    if (isFirstLine)
                                    {
                                        isFirstLine = false;
                                        continue;
                                    }

                                    if (string.IsNullOrWhiteSpace(line))
                                        continue;

                                    string[] fields = line.Split(';');
                                    if (fields.Length < 3) // تغيير الحد الأدنى للحقول إلى 3 (اسم المستخدم، كلمة المرور، القسم)
                                    {
                                        errorCount++;
                                        report.AppendLine($"<div class='text-danger'>سطر {lineNumber}: لا يحتوي على الحقول الأساسية المطلوبة</div>");
                                        continue;
                                    }

                                    string username = fields[0].Trim();
                                    string password = fields[1].Trim();
                                    string permission = fields[2].Trim();

                                    // التعامل مع البريد الإلكتروني والهاتف كحقول اختيارية
                                    string email = (fields.Length > 3) ? fields[3].Trim() : "";
                                    string phone = (fields.Length > 4) ? fields[4].Trim() : "";

                                    // التحقق من البيانات الإلزامية فقط
                                    if (string.IsNullOrWhiteSpace(username) || string.IsNullOrWhiteSpace(password) ||
                                        string.IsNullOrWhiteSpace(permission))
                                    {
                                        errorCount++;
                                        report.AppendLine($"<div class='text-danger'>سطر {lineNumber}: يحتوي على حقول أساسية فارغة</div>");
                                        continue;
                                    }

                                    // التحقق من البريد الإلكتروني فقط إذا كان موجوداً
                                    if (!string.IsNullOrWhiteSpace(email) && !IsValidEmail(email))
                                    {
                                        errorCount++;
                                        report.AppendLine($"<div class='text-danger'>سطر {lineNumber}: البريد الإلكتروني غير صالح</div>");
                                        continue;
                                    }

                                    // التحقق من طول كلمة المرور
                                    if (password.Length < 6)
                                    {
                                        errorCount++;
                                        report.AppendLine($"<div class='text-danger'>سطر {lineNumber}: كلمة المرور يجب أن تكون 6 أحرف على الأقل</div>");
                                        continue;
                                    }

                                    // التحقق من وجود المستخدم
                                    string checkQuery = "SELECT COUNT(*) FROM dbo.login WHERE username = @Username";
                                    using (SqlCommand checkCmd = new SqlCommand(checkQuery, con, transaction))
                                    {
                                        checkCmd.Parameters.AddWithValue("@Username", username);
                                        int userCount = (int)checkCmd.ExecuteScalar();

                                        if (userCount > 0)
                                        {
                                            errorCount++;
                                            report.AppendLine($"<div class='text-warning'>سطر {lineNumber}: المستخدم موجود مسبقاً - {username}</div>");
                                            continue;
                                        }
                                    }

                                    // تشفير كلمة المرور بنفس طريقة التشفير المستخدمة في إضافة المستخدم
                                    string hashedPassword = HashPassword(password);

                                    // إدخال المستخدم
                                    string insertQuery = @"INSERT INTO dbo.login 
    (username, password, permission, email, phone) 
    VALUES 
    (@Username, @Password, @Permission, @Email, @Phone)";
                                    using (SqlCommand cmd = new SqlCommand(insertQuery, con, transaction))
                                    {
                                        cmd.Parameters.AddWithValue("@Username", username);
                                        cmd.Parameters.AddWithValue("@Password", hashedPassword);
                                        cmd.Parameters.AddWithValue("@Permission", permission);
                                        cmd.Parameters.AddWithValue("@Email", !string.IsNullOrEmpty(email) ? (object)email : DBNull.Value);
                                        cmd.Parameters.AddWithValue("@Phone", !string.IsNullOrEmpty(phone) ? (object)phone : DBNull.Value);

                                        cmd.ExecuteNonQuery();
                                        successCount++;

                                        // إضافة ملاحظة إذا كانت بيانات المستخدم غير مكتملة
                                        if (string.IsNullOrEmpty(email) || string.IsNullOrEmpty(phone))
                                        {
                                            report.AppendLine($"<div class='text-success'>تم استيراد المستخدم: {username} <span class='text-warning'>(بيانات غير مكتملة)</span></div>");
                                        }
                                        else
                                        {
                                            report.AppendLine($"<div class='text-success'>تم استيراد المستخدم: {username}</div>");
                                        }
                                    }
                                }

                                if (successCount > 0)
                                {
                                    transaction.Commit();
                                    report.Insert(0, $"<div class='fw-bold mb-2'>تم استيراد {successCount} مستخدم بنجاح.</div>");
                                    if (errorCount > 0)
                                    {
                                        report.Insert(0, $"<div class='text-warning mb-2'>فشل استيراد {errorCount} مستخدم.</div>");
                                    }
                                }
                                else
                                {
                                    transaction.Rollback();
                                    report.Insert(0, "<div class='text-danger fw-bold mb-2'>لم يتم استيراد أي مستخدمين.</div>");
                                }

                                // عرض التقرير
                                litAccountReport.Text = $"<div class='report-container'>{report.ToString()}</div>";
                                accountReportPanel.Visible = true;

                                // تحديث عرض الحسابات والإحصائيات
                                BindAccountsGrid();
                                UpdateStatistics();
                            }
                            catch (Exception ex)
                            {
                                transaction.Rollback();
                                throw new Exception($"خطأ في معالجة الملف: {ex.Message}");
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                ShowError($"خطأ في استيراد الملف: {ex.Message}");
            }
        }

        // دالة تصدير الحسابات إلى ملف CSV
        // دالة تصدير الحسابات إلى ملف CSV
        protected void btnExportAccountsCSV_Click(object sender, EventArgs e)
        {
            try
            {
                using (SqlConnection con = new SqlConnection(connectionString))
                {
                    string query = @"SELECT 
                                username, 
                                password, 
                                permission, 
                                email, 
                                phone 
                             FROM dbo.login 
                             WHERE username != 'Super Admin'";
                    using (SqlCommand cmd = new SqlCommand(query, con))
                    {
                        con.Open();
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            StringBuilder sb = new StringBuilder();
                            // إضافة عناوين الأعمدة
                            sb.AppendLine("username;password;permission;email;phone");

                            // إضافة البيانات
                            while (reader.Read())
                            {
                                sb.AppendLine(
                                    $"{reader["username"]};" +
                                    $"*****;" + 
                                    $"{reader["permission"]};" +
                                    $"{reader["email"]};" +
                                    $"{reader["phone"]}"
                                );
                            }

                            Response.Clear();
                            Response.Buffer = true;
                            Response.AddHeader("content-disposition", "attachment;filename=Accounts.csv");
                            Response.Charset = "";
                            Response.ContentType = "text/csv";
                            Response.ContentEncoding = Encoding.UTF8;
                            Response.Output.Write("\uFEFF"); // BOM للدعم العربي
                            Response.Output.Write(sb.ToString());
                            Response.Flush();
                            Response.End();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                ShowError("خطأ في تصدير البيانات: " + ex.Message);
            }
        }

        // دالة حذف جميع الحسابات
        protected void btnDeleteAllAccounts_Click(object sender, EventArgs e)
        {
            try
            {
                using (SqlConnection con = new SqlConnection(connectionString))
                {
                    con.Open();

                    string deleteQuery = "DELETE FROM dbo.login WHERE username != 'Super Admin'";

                    using (SqlCommand cmd = new SqlCommand(deleteQuery, con))
                    {
                        int rowsAffected = cmd.ExecuteNonQuery();

                        if (rowsAffected > 0)
                        {
                            ShowSuccess($"تم حذف جميع الحسابات بنجاح. عدد الحسابات المحذوفة: {rowsAffected}");
                            BindAccountsGrid();
                            UpdateStatistics();
                        }
                        else
                        {
                            ShowError("لا توجد حسابات لحذفها.");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                ShowError("حدث خطأ أثناء عملية الحذف: " + ex.Message);
            }
        }
    }
}
