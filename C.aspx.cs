﻿using System;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.IO.Compression;
using System.IO;
using System.Linq;
using System.Web.UI.WebControls;
using abozyad.Helpers;
using System.Text;
using System.Web.UI;
using System.Collections.Generic;

namespace abozyad
{
    public partial class WebForm6 : System.Web.UI.Page
    {


        // عند تحميل الصفحة، تتحقق هذه الدالة من صلاحيات المستخدم.
        // إذا لم يكن لدى المستخدم الصلاحيات المناسبة (المحددة في القائمة allowedPermissions)، يتم إعادة توجيهه إلى صفحة "رفض الوصول".
        // كما يتم التحقق مما إذا كانت هذه أول مرة يتم فيها تحميل الصفحة (IsPostBack).
        // إذا كانت هذه هي المرة الأولى لتحميل الصفحة، يتم استدعاء دالة PopulateOrderNumbers لتحميل أرقام الطلبات المتاحة.
        protected void Page_Load(object sender, EventArgs e)
        {


            // التحقق مما إذا كانت صلاحية المستخدم غير موجودة
            if (Session["UserPermission"] == null)
            {
                // إعادة التوجيه إلى صفحة "رفض الوصول" أو عرض رسالة خطأ
                Response.Redirect("AccessDenied.aspx");
            }

            // قائمة بالصلاحيات المسموح بها
            string[] allowedPermissions = {
                "مشرف خدمات الموظفين",
                "مشرف إدارة تخطيط الموارد البشرية",
                "مشرف إدارة تقنية المعلومات",
                "مشرف مراقبة الدوام",
                "مشرف السجلات الطبية",
                "مشرف إدارة الرواتب والاستحقاقات",
                "مشرف إدارة القانونية والالتزام",
                "مشرف خدمات الموارد البشرية",
                "مشرف إدارة الإسكان",
                "مشرف قسم الملفات",
                "مشرف العيادات الخارجية",
                "مشرف التأمينات الاجتماعية",
                "مشرف وحدة مراقبة المخزون",
                "مشرف إدارة تنمية الإيرادات",
                "مشرف إدارة الأمن و السلامة",
                "مشرف الطب الاتصالي"
            };
            Session["UserPermission"] = "مشرف إدارة تقنية المعلومات";
            // التحقق مما إذا كانت صلاحية المستخدم غير موجودة ضمن الصلاحيات المسموح بها أو إذا كانت الصلاحية "مدير حسابات"
            if (!allowedPermissions.Contains(Session["UserPermission"]?.ToString()) || Session["UserPermission"].ToString() == "مدير حسابات")
            {
                // إعادة التوجيه إلى صفحة "رفض الوصول" أو عرض رسالة خطأ
                Response.Redirect("AccessDenied.aspx");
            }
            // تحميل البيانات في حالة أول تحميل للصفحة
            if (!IsPostBack)
            {
                PopulateOrderNumbers();
                LoadFollowUpRecords(); // إضافة تحميل السجلات
            }


        }
        private string CleanFileName(string fileName)
        {
            // إزالة الأحرف غير المسموح بها في أسماء الملفات
            char[] invalidChars = System.IO.Path.GetInvalidFileNameChars();
            foreach (char c in invalidChars)
            {
                fileName = fileName.Replace(c, '_');
            }

            // التأكد من أن الاسم لا يتجاوز طولاً معيناً
            const int maxLength = 100;
            if (fileName.Length > maxLength)
            {
                string extension = System.IO.Path.GetExtension(fileName);
                fileName = fileName.Substring(0, maxLength - extension.Length) + extension;
            }

            return fileName;
        }

        protected void btnDownload_Click(object sender, EventArgs e)
        {
            try
            {
                string selectedOrderNumber = ddlOrderNumbers.SelectedValue;
                string connectionString = ConfigurationManager.ConnectionStrings["MyConnectionString"].ConnectionString;

                using (SqlConnection con = new SqlConnection(connectionString))
                {
                    string query = @"SELECT ملف1, ملف2, ملف3, ملف4, [اسم الموظف] 
                           FROM ordersTable 
                           WHERE [رقم الطلب] = @OrderNumber";

                    using (SqlCommand cmd = new SqlCommand(query, con))
                    {
                        cmd.Parameters.AddWithValue("@OrderNumber", selectedOrderNumber);
                        con.Open();

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                string employeeName = reader["اسم الموظف"].ToString().Trim();
                                bool filesFound = false;

                                using (MemoryStream zipStream = new MemoryStream())
                                {
                                    using (ZipArchive zipArchive = new ZipArchive(zipStream, ZipArchiveMode.Create, true))
                                    {
                                        for (int i = 0; i < 4; i++)
                                        {
                                            byte[] compressedData = reader[i] as byte[];
                                            if (compressedData != null && compressedData.Length > 0)
                                            {
                                                try
                                                {
                                                    // فك ضغط البيانات
                                                    byte[] pdfData = FileCompressor.ExtractFile(compressedData);

                                                    if (pdfData != null && IsPdfFile(pdfData))
                                                    {
                                                        filesFound = true;
                                                        string fileName = CleanFileName($"مرفق_{i + 1}_طلب_{selectedOrderNumber}_{employeeName}.pdf");

                                                        ZipArchiveEntry zipEntry = zipArchive.CreateEntry(fileName);
                                                        using (Stream entryStream = zipEntry.Open())
                                                        {
                                                            entryStream.Write(pdfData, 0, pdfData.Length);
                                                        }

                                                        System.Diagnostics.Debug.WriteLine($"✅ تم إضافة الملف: {fileName}");
                                                    }
                                                    else
                                                    {
                                                        System.Diagnostics.Debug.WriteLine($"❌ الملف {i + 1} ليس PDF صالح");
                                                    }
                                                }
                                                catch (Exception ex)
                                                {
                                                    System.Diagnostics.Debug.WriteLine($"❌ خطأ في معالجة الملف {i + 1}: {ex.Message}");
                                                    continue;  // استمر مع الملف التالي
                                                }
                                            }
                                        }
                                    }

                                    if (filesFound)
                                    {
                                        string zipFileName = CleanFileName($"مرفقات_طلب_{selectedOrderNumber}_{employeeName}.zip");

                                        Response.Clear();
                                        Response.ContentType = "application/zip";
                                        Response.AddHeader("Content-Disposition", $"attachment; filename={zipFileName}");
                                        Response.BinaryWrite(zipStream.ToArray());
                                        Response.Flush();
                                        Response.End();
                                    }
                                    else
                                    {
                                        LabelError.Text = "لا توجد ملفات صالحة لتحميلها.";
                                        LabelError.Visible = true;
                                    }
                                }
                            }
                            else
                            {
                                LabelError.Text = "لم يتم العثور على الطلب.";
                                LabelError.Visible = true;
                            }
                        }
                    }
                }
            }
            catch (System.Threading.ThreadAbortException)
            {
                // تجاهل
            }
            catch (Exception ex)
            {
                LabelError.Text = "حدث خطأ أثناء تحميل الملفات: " + ex.Message;
                LabelError.Visible = true;
                System.Diagnostics.Debug.WriteLine($"❌ خطأ عام: {ex.Message}");
            }
        }

        private bool IsPdfFile(byte[] data)
        {
            if (data == null || data.Length < 4)
                return false;

            // التحقق من PDF header
            return data[0] == 0x25 && // %
                   data[1] == 0x50 && // P
                   data[2] == 0x44 && // D
                   data[3] == 0x46;   // F
        }



        /// <summary>
        /// تحديد امتداد الملف بناءً على محتواه - تم تحسينها للتعامل مع PDF فقط
        /// </summary>
        private string DetermineFileExtension(byte[] fileData)
        {
            return ValidatePdfFormat(fileData) ? ".pdf" : null;
        }

        /// <summary>
        /// التحقق من أن الملف هو PDF صالح
        /// </summary>
        private bool ValidatePdfFormat(byte[] data)
        {
            try
            {
                if (data == null || data.Length < 4)
                {
                    System.Diagnostics.Debug.WriteLine("❌ البيانات فارغة أو قصيرة جداً");
                    return false;
                }

                // فحص توقيع PDF (PDF Signature)
                bool isPdf = data[0] == 0x25 && // %
                            data[1] == 0x50 && // P
                            data[2] == 0x44 && // D
                            data[3] == 0x46;   // F

                if (!isPdf)
                {
                    System.Diagnostics.Debug.WriteLine("❌ الملف ليس بصيغة PDF صالحة");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("✅ تم التحقق من صحة ملف PDF");
                }

                return isPdf;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في فحص نوع الملف: {ex.Message}");
                return false;
            }
        }


        // عند تغيير العنصر المحدد في القائمة المنسدلة للطلبات (ddlOrderNumbers)، 
        // تتحقق هذه الدالة مما إذا كان قد تم اختيار رقم طلب صحيح (ليس "0").
        // إذا كان رقم الطلب المختار صحيحًا، يتم استدعاء دالة LoadOrderDetails لتحميل تفاصيل الطلب المحدد.

        protected void ddlOrderNumbers_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (ddlOrderNumbers.SelectedValue != "0")
            {
                LoadOrderDetails(ddlOrderNumbers.SelectedValue);
            }
        }


        // تقوم هذه الدالة بتحميل تفاصيل الطلب بناءً على رقم الطلب المرسل.
        // تستعلم عن تفاصيل الطلب من قاعدة البيانات باستخدام رقم الطلب المحدد.
        // تعرض البيانات المسترجعة في مجموعة من الحقول (مثل رقم الطلب، تاريخ الطلب، حالة الطلب، المشرفين...إلخ).
        // إذا تم العثور على بيانات الطلب، يتم إظهارها في واجهة المستخدم، وإلا يتم إخفاء لوحة تفاصيل الطلب.
        private void LoadOrderDetails(string orderNumber)
        {
            if (AutoPathInfoPanel != null)
            {
                AutoPathInfoPanel.InnerHtml = "";
                AutoPathInfoPanel.Visible = false;
            }

            using (SqlConnection con = new SqlConnection(connectionString))
            {

                string query = @"SELECT [رقم الطلب], [تاريخ الطلب], [حالة الطلب], [نوع الطلب], [اسم الموظف],
                [القسم], [تفاصيل مقدم الطلب], [الوظيفة], [رقم الموظف], [السجل المدني], 
                [الجنسية], [رقم الجوال], [نوع التوظيف], [المؤهل], 
                [تم التأكيد/الإلغاء من مدير القسم], [تم التأكيد/الإلغاء من قبل المشرف], 
                [تم التحويل/الإلغاء من قبل المنسق], [سبب الإلغاء/الإعادة], [تفاصيل المنسق], 
                [مشرف خدمات الموظفين], [مشرف إدارة تخطيط الموارد البشرية], 
                [مشرف إدارة تقنية المعلومات], [مشرف مراقبة الدوام], [مشرف السجلات الطبية], 
                [مشرف إدارة الرواتب والاستحقاقات], [مشرف إدارة القانونية والالتزام], 
                [مشرف خدمات الموارد البشرية], [مشرف إدارة الإسكان], [مشرف قسم الملفات], 
                [مشرف العيادات الخارجية], [مشرف التأمينات الاجتماعية], [مشرف وحدة مراقبة المخزون], 
                [مشرف إدارة تنمية الإيرادات], [مشرف إدارة الأمن و السلامة], [مشرف الطب الاتصالي], 
                [مدير الموارد البشرية], [نوع التحويل], [ملاحظات المشرفين], 
                [ملف1], [ملف2], [ملف3], [ملف4] 
         FROM OrdersTable WHERE [رقم الطلب] = @OrderNumber";

                using (SqlCommand cmd = new SqlCommand(query, con))
                {
                    cmd.Parameters.AddWithValue("@OrderNumber", orderNumber);
                    try
                    {
                        con.Open();
                        SqlDataReader reader = cmd.ExecuteReader();

                        if (reader.Read())
                        {
                        SetLabelVisibilityAndText(LabelOrderNumber, reader["رقم الطلب"].ToString());
                        SetLabelVisibilityAndText(LabelOrderDate, reader["تاريخ الطلب"].ToString());
                        SetLabelVisibilityAndText(LabelOrderStatus, reader["حالة الطلب"].ToString());
                        SetLabelVisibilityAndText(LabelOrderType, reader["نوع الطلب"].ToString());
                        SetLabelVisibilityAndText(LabelEmployeeName, reader["اسم الموظف"].ToString());
                        SetLabelVisibilityAndText(LabelDepartment, reader["القسم"].ToString());
                        SetLabelVisibilityAndText(LabelNotes, reader["تفاصيل مقدم الطلب"].ToString());
                        SetLabelVisibilityAndText(LabelJobTitle, reader["الوظيفة"].ToString());
                        SetLabelVisibilityAndText(LabelEmployeeNumber, reader["رقم الموظف"].ToString());
                        SetLabelVisibilityAndText(LabelCivilRegistry, reader["السجل المدني"].ToString());
                        SetLabelVisibilityAndText(LabelNationality, reader["الجنسية"].ToString());
                        SetLabelVisibilityAndText(LabelMobileNumber, reader["رقم الجوال"].ToString());
                        SetLabelVisibilityAndText(LabelEmploymentType, reader["نوع التوظيف"].ToString());
                        SetLabelVisibilityAndText(LabelQualification, reader["المؤهل"].ToString());
                        SetLabelVisibilityAndText(LabelManagerApproval, reader["تم التأكيد/الإلغاء من مدير القسم"].ToString());
                        SetLabelVisibilityAndText(LabelSupervisorApproval, reader["تم التأكيد/الإلغاء من قبل المشرف"].ToString());
                        SetLabelVisibilityAndText(LabelCoordinatorApproval, reader["تم التحويل/الإلغاء من قبل المنسق"].ToString());
                        SetLabelVisibilityAndText(LabelCancellationReason, reader["سبب الإلغاء/الإعادة"].ToString());
                        SetLabelVisibilityAndText(LabelCoordinatorDetails, reader["تفاصيل المنسق"].ToString());
                        SetLabelVisibilityAndText1(LabelMedicalServicesPermission, reader["مشرف خدمات الموظفين"].ToString());
                        SetLabelVisibilityAndText1(LabelHRPlanningPermission, reader["مشرف إدارة تخطيط الموارد البشرية"].ToString());
                        SetLabelVisibilityAndText1(LabelITPermission, reader["مشرف إدارة تقنية المعلومات"].ToString());
                        SetLabelVisibilityAndText1(LabelAttendanceControlPermission, reader["مشرف مراقبة الدوام"].ToString());
                        SetLabelVisibilityAndText1(LabelMedicalRecordsPermission, reader["مشرف السجلات الطبية"].ToString());
                        SetLabelVisibilityAndText1(LabelPayrollPermission, reader["مشرف إدارة الرواتب والاستحقاقات"].ToString());
                        SetLabelVisibilityAndText1(LabelLegalCompliancePermission, reader["مشرف إدارة القانونية والالتزام"].ToString());
                        SetLabelVisibilityAndText1(LabelHRServicesPermission, reader["مشرف خدمات الموارد البشرية"].ToString());
                        SetLabelVisibilityAndText1(LabelHousingPermission, reader["مشرف إدارة الإسكان"].ToString());
                        SetLabelVisibilityAndText1(LabelFilesSectionPermission, reader["مشرف قسم الملفات"].ToString());
                        SetLabelVisibilityAndText1(LabelOutpatientPermission, reader["مشرف العيادات الخارجية"].ToString());
                        SetLabelVisibilityAndText1(LabelSocialInsurancePermission, reader["مشرف التأمينات الاجتماعية"].ToString());
                        SetLabelVisibilityAndText1(LabelInventoryControlPermission, reader["مشرف وحدة مراقبة المخزون"].ToString());
                        SetLabelVisibilityAndText1(LabelSelfResourcesPermission, reader["مشرف إدارة تنمية الإيرادات"].ToString());
                        SetLabelVisibilityAndText1(LabelNursingPermission, reader["مشرف إدارة الأمن و السلامة"].ToString());
                        SetLabelVisibilityAndText1(LabelEmployeeServicesPermission, reader["مشرف الطب الاتصالي"].ToString());
                        SetLabelVisibilityAndText(LabelHRManagerApproval, reader["مدير الموارد البشرية"].ToString());
                            // التحقق من حالة الطلب
                            if (reader["حالة الطلب"].ToString() == "يتطلب إجراءات")
                            {
                                string actionDetails = "";
                                if (!reader.IsDBNull(reader.GetOrdinal("ملاحظات المشرفين")))
                                {
                                    actionDetails = reader["ملاحظات المشرفين"].ToString();
                                }
                                if (!string.IsNullOrEmpty(actionDetails))
                                {
                                    LabelMessage.Text = $"نود إبلاغكم بأن طلبكم قيد الانتظار حالياً نظراً لعدم اكتمال بعض المستندات. {actionDetails}";
                                }
                                else
                                {
                                    LabelMessage.Text = "نود إبلاغكم بأن طلبكم قيد الانتظار حالياً نظراً لعدم اكتمال بعض المستندات.";
                                }
                                LabelMessage.Visible = true;
                            }
                            string civilRegistry = reader["السجل المدني"].ToString();
                            if (!string.IsNullOrEmpty(civilRegistry))
                            {
                                CheckFollowUpAlert(civilRegistry);
                            }
                            OrderDetailsPanel.Visible = true;
                        }
                        else
                        {
                            OrderDetailsPanel.Visible = false;
                        }
                    }
                    catch (Exception ex)
                    {
                        LabelError.Text = $"حدث خطأ أثناء تحميل تفاصيل الطلب: {ex.Message}";
                        LabelError.Visible = true;
                    }
                }
            }
        }

        // تقوم هذه الدالة بتعيين النص لعنصر الـ Label والتحقق مما إذا كان النص يحتوي على قيمة.
        // إذا كان النص يمثل تاريخًا صالحًا، تقوم بعرضه بتنسيق (yyyy-MM-dd).
        // إذا لم يكن النص تاريخًا، يتم عرضه كما هو.
        // إذا كان النص فارغًا أو غير صالح، يتم إخفاء الـ Label.
        private void SetLabelVisibilityAndText(Label label, string text)
        {
            if (!string.IsNullOrWhiteSpace(text))
            {


                DateTime dateValue;
                if (DateTime.TryParse(text, out dateValue))
                {
                    label.Text = dateValue.ToString("yyyy-MM-dd");
                    label.Visible = true;
                }
                else
                {

                    label.Text = text;
                    label.Visible = true;
                }
            }
            else
            {
                label.Visible = false;
            }


        }

        // تقوم هذه الدالة بتعيين النص لعنصر الـ Label مع إجراء بعض التعديلات عليه.
        // إذا كان النص يحتوي على العبارة "اعتماد بواسطة"، يتم إزالتها.
        // إذا كان النص فارغًا، يتم استبداله بـ "/".
        // بعد تعديل النص (إذا لزم الأمر)، يتم تعيينه لعنصر الـ Label ويتم التأكد من إظهار الـ Label.
        private void SetLabelVisibilityAndText1(Label label, string text)
        {
            if (text.Contains("اعتماد بواسطة"))
            {
                text = text.Replace("اعتماد بواسطة", "").Trim();
            }

            if (string.IsNullOrEmpty(text))
            {
                text = "/";
            }

            label.Visible = true; // Make sure the label is visible
            label.Text = text; // Set the text of the label

        }


        // تقوم هذه الدالة بملء القائمة المنسدلة بأرقام الطلبات بناءً على صلاحية المستخدم.
        // إذا كانت صلاحية المستخدم غير محددة، تعرض رسالة خطأ.
        // في حالة كان المستخدم "مدير الموارد البشرية"، يتم جلب الطلبات التي حالتها (1).
        // أما إذا كان المستخدم مشرف قسم معين، يتم جلب الطلبات التي في حالة "الطلب تحت التنفيذ" أو "أُعيد بواسطة أحد المشرفين".
        // بعد استرجاع الطلبات من قاعدة البيانات، يتم إضافتها إلى القائمة المنسدلة مع خيار افتراضي "اختر رقم الطلب".

        private void PopulateOrderNumbers()
        {
            string department = Session["UserPermission"]?.ToString().Trim();
            if (string.IsNullOrEmpty(department))
            {
                LabelError.Text = "صلاحية المستخدم غير محددة.";
                LabelError.Visible = true;
                return;
            }

            if (department.Length > 5)
            {
                department = department.Substring(5);
            }

            try
            {
                using (SqlConnection con = new SqlConnection(connectionString))
                {
                    string query = @"
               SELECT DISTINCT
                   [رقم الطلب],
                   [اسم الموظف],
                   CASE 
                       WHEN [اسم الموظف] IS NULL OR [اسم الموظف] = '' 
                       THEN CAST([رقم الطلب] AS NVARCHAR(50))
                       ELSE CAST([رقم الطلب] AS NVARCHAR(50)) + ' | ' + [اسم الموظف]
                   END AS DisplayText
               FROM ordersTable 
        WHERE (
            -- يظهر الطلب في كل الحالات (C, يتطلب إجراءات, أُعيد)
            [حالة الطلب] IN ('(C)', N'يتطلب إجراءات من المشرف', N'أُعيد بواسطة أحد المشرفين')
            AND [مشرف " + department + @"] IS NOT NULL 
            AND [مشرف " + department + @"] <> '' 
            AND
            (
                -- المشرف لم يتخذ أي إجراء بعد
                [مشرف " + department + @"] LIKE '%الطلب تحت التنفيذ%'
                OR
                -- أو المشرف هو من طلب الإجراءات (يستمر في رؤية الطلب)
                [مشرف " + department + @"] LIKE '%يتطلب إجراءات%'
                OR
                -- حالة التحويل السريع
                [نوع التحويل] = 'سريع'
            )
            AND
            -- لم يقم باعتماد أو إعادة الطلب
            NOT [مشرف " + department + @"] LIKE '%اعتماد بواسطة%'
            AND NOT [مشرف " + department + @"] LIKE '%تم الإعادة%'
        )
        ORDER BY [رقم الطلب] DESC";

                    using (SqlCommand cmd = new SqlCommand(query, con))
                    {
                        con.Open();
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            ddlOrderNumbers.Items.Clear();
                            ddlOrderNumbers.DataSource = reader;
                            ddlOrderNumbers.DataTextField = "DisplayText";
                            ddlOrderNumbers.DataValueField = "رقم الطلب";
                            ddlOrderNumbers.DataBind();
                            ddlOrderNumbers.Items.Insert(0, new ListItem("-- اختر رقم الطلب --", ""));
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LabelError.Text = "حدث خطأ أثناء تحميل الطلبات: " + ex.Message;
                LabelError.Visible = true;
            }
        }






        // عند النقر على زر "تأكيد الطلب"، تقوم هذه الدالة بتنفيذ عدة خطوات:
        // 1. الحصول على صلاحية المستخدم الحالية من الجلسة وتحديد القسم.
        // 2. التحقق من اختيار رقم طلب صحيح. إذا لم يتم اختيار رقم طلب، يتم عرض رسالة خطأ.
        // 3. يتم تحديث حالة الطلب في قاعدة البيانات، حيث يتم استبدال "الطلب تحت التنفيذ" بـ "اعتماد بواسطة [اسم المستخدم] وتاريخ اليوم".
        // 4. إذا تم تحديث الطلب بنجاح، يتم عرض رسالة تأكيد وإخفاء تفاصيل الطلب، وتحديث القائمة المنسدلة للطلبات.
        // 5. في حال وجود خطأ أثناء عملية التأكيد، يتم عرض رسالة خطأ.

        protected void btnConfirmOrder_Click(object sender, EventArgs e)
        {
            try
            {
                string department = Session["UserPermission"]?.ToString().Trim();
                if (department != null && department.Length > 5)
                {
                    department = department.Substring(5);
                }

                if (ddlOrderNumbers.SelectedIndex == 0)
                {
                    ShowError("يرجى اختيار رقم الطلب.");
                    return;
                }

                string selectedOrderNumber = ddlOrderNumbers.SelectedValue;
                string confirmedBy = Session["Username"]?.ToString();
                string currentDate = DateTime.Now.ToString("yyyy-MM-dd");

                using (SqlConnection con = new SqlConnection(connectionString))
                {
                    con.Open();
                    using (var transaction = con.BeginTransaction())
                    {
                        try
                        {
                            // التحقق من حالة جميع المشرفين
                            string supervisorCheckQuery = @"
                        SELECT 
                            [مشرف خدمات الموظفين], 
                            [مشرف إدارة تخطيط الموارد البشرية],
                            [مشرف إدارة تقنية المعلومات],
                            [مشرف مراقبة الدوام],
                            [مشرف السجلات الطبية],
                            [مشرف إدارة الرواتب والاستحقاقات],
                            [مشرف إدارة القانونية والالتزام],
                            [مشرف خدمات الموارد البشرية],
                            [مشرف إدارة الإسكان],
                            [مشرف قسم الملفات],
                            [مشرف العيادات الخارجية],
                            [مشرف التأمينات الاجتماعية],
                            [مشرف وحدة مراقبة المخزون],
                            [مشرف إدارة تنمية الإيرادات],
                            [مشرف إدارة الأمن و السلامة],
                            [مشرف الطب الاتصالي]
                        FROM ordersTable 
                        WHERE [رقم الطلب] = @OrderNumber";

                            bool canProceedToHRManager = true;
                            Dictionary<string, string> supervisorStatuses = new Dictionary<string, string>();

                            using (var cmdCheck = new SqlCommand(supervisorCheckQuery, con, transaction))
                            {
                                cmdCheck.Parameters.AddWithValue("@OrderNumber", selectedOrderNumber);
                                using (var reader = cmdCheck.ExecuteReader())
                                {
                                    if (reader.Read())
                                    {
                                        // التحقق من كل عمود مشرف
                                        for (int i = 0; i < reader.FieldCount; i++)
                                        {
                                            string columnName = reader.GetName(i);
                                            string value = reader.IsDBNull(i) ? "" : reader.GetString(i);
                                            supervisorStatuses[columnName] = value;

                                            // إذا كان العمود يحتوي على "الطلب تحت التنفيذ"، فهذا يعني أن المشرف لم يعتمد بعد
                                            if (!string.IsNullOrEmpty(value) &&
                                                value.Contains("الطلب تحت التنفيذ"))
                                            {
                                                canProceedToHRManager = false;
                                            }
                                        }
                                    }
                                }
                            }

                            // تحديث حالة المشرف الحالي
                            string updateQuery = @"UPDATE ordersTable 
                        SET [مشرف " + department + @"] = @NewStatus";

                            // إضافة تحديث حالة الطلب إذا كان جميع المشرفين قد اعتمدوا
                            if (canProceedToHRManager)
                            {
                                updateQuery += ", [حالة الطلب] = '(D)'";
                            }

                            updateQuery += @", [ملاحظات المشرفين] = CASE 
                            WHEN [حالة الطلب] = N'يتطلب إجراءات' THEN NULL 
                            ELSE [ملاحظات المشرفين] 
                        END
                        WHERE [رقم الطلب] = @OrderNumber";

                            using (var cmdUpdate = new SqlCommand(updateQuery, con, transaction))
                            {
                                string newStatus = $"{currentDate} | اعتماد بواسطة {confirmedBy} {currentDate}";
                                cmdUpdate.Parameters.AddWithValue("@OrderNumber", selectedOrderNumber);
                                cmdUpdate.Parameters.AddWithValue("@NewStatus", newStatus);

                                int result = cmdUpdate.ExecuteNonQuery();
                                if (result > 0)
                                {
                                    transaction.Commit();

                                    string successMessage = canProceedToHRManager
                                        ? "تم اعتماد الطلب بنجاح وتحويله إلى مدير الموارد البشرية."
                                        : "تم اعتماد الطلب بنجاح. في انتظار اعتماد باقي المشرفين.";

                                    ShowSuccess(successMessage);
                                    OrderDetailsPanel.Visible = false;
                                    UpdateOrderStatuses();
                                    PopulateOrderNumbers();
                                    UpdateMasterCount();
                                }
                                else
                                {
                                    throw new Exception("لم يتم العثور على الطلب");
                                }
                            }
                        }
                        catch (Exception)
                        {
                            transaction.Rollback();
                            throw;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                ShowError($"حدث خطأ أثناء تأكيد الطلب: {ex.Message}");
            }
        }

        private void UpdateMasterCount()
        {
            if (Master is SiteMaster master)
            {
                master.UpdateNewOrdersCount();
            }
        }






        // هذه الدالة تقوم بتحديث حالة الطلبات بناءً على الأعمدة المتعلقة بالمشرفين:
        // 1. جلب جميع الطلبات التي لم تُقبل بعد من قاعدة البيانات.
        // 2. التكرار عبر كل صف من الطلبات:
        //    - يتم التحقق من جميع أعمدة المشرفين للتأكد مما إذا كانت جميع الأعمدة فارغة أو إذا كانت تحتوي على حالات.
        //    - إذا كانت هناك أي عمود يحتوي على "تم الإعادة"، يتم تحديث حالة الطلب إلى "أُعيد بواسطة أحد المشرفين".
        //    - إذا كانت جميع الأعمدة غير فارغة وتحتوي على "اعتماد بواسطة"، يتم تحديث حالة الطلب إلى "(D)" للإشارة إلى اكتمال الطلب.
        // 3. يتم تنفيذ تحديث حالة الطلب لكل طلب بناءً على التحقق السابق.
        protected void UpdateOrderStatuses()
        {
            string connectionString = ConfigurationManager.ConnectionStrings["MyConnectionString"].ConnectionString;
            string department = Session["UserPermission"]?.ToString().Trim();
            if (department?.Length > 5) department = department.Substring(5);
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                con.Open();

                // استرجاع جميع الصفوف من جدول الطلبات التي حالتها ليست "مقبول"
                string selectQuery = "SELECT * FROM ordersTable WHERE [حالة الطلب] <> 'مقبول'";
                DataTable ordersTable = new DataTable();

                using (SqlDataAdapter adapter = new SqlDataAdapter(selectQuery, con))
                {
                    adapter.Fill(ordersTable); // ملء الجدول بالبيانات المسترجعة
                }

                // تحضير أمر التحديث
                string updateQuery = "UPDATE ordersTable SET [حالة الطلب] = @RequestStatus WHERE [رقم الطلب] = @OrderNumber";
                using (SqlCommand updateCmd = new SqlCommand(updateQuery, con))
                {
                    updateCmd.Parameters.Add("@RequestStatus", SqlDbType.NVarChar);
                    updateCmd.Parameters.Add("@OrderNumber", SqlDbType.Int);

                    // التكرار عبر كل صف
                    foreach (DataRow row in ordersTable.Rows)
                    {
                        bool allEthenColumnsEmpty = true; // للتحقق إذا كانت جميع أعمدة المشرفين فارغة
                        bool anyColumnRejected = false;  // للتحقق إذا تم إعادة الطلب من أحد المشرفين
                        bool allNonEmptyColumnsSigned = true; // للتحقق إذا تم التوقيع على جميع الأعمدة غير الفارغة

                        // التكرار عبر كل عمود من الأعمدة
                        foreach (DataColumn column in ordersTable.Columns)
                        {
                            if (column.ColumnName.StartsWith("مشرف")) // التحقق إذا كان العمود يخص مشرف
                            {
                                string columnValue = row[column]?.ToString();

                                if (!string.IsNullOrEmpty(columnValue))
                                {
                                    allEthenColumnsEmpty = false; // إذا كان العمود غير فارغ

                                    if (columnValue.Contains("تم الإعادة"))
                                    {
                                        anyColumnRejected = true; // إذا تم إعادة الطلب من المشرف
                                        break; // الخروج من الحلقة
                                    }
                                    else if (!columnValue.Contains("اعتماد بواسطة"))
                                    {
                                        allNonEmptyColumnsSigned = false; // إذا لم يتم التوقيع على العمود
                                    }
                                }
                            }
                        }

                        if (!allEthenColumnsEmpty)
                        {
                            if (anyColumnRejected)
                            {
                                updateCmd.Parameters["@RequestStatus"].Value = "أُعيد بواسطة أحد المشرفين";
                                updateCmd.Parameters["@OrderNumber"].Value = row["رقم الطلب"];
                                updateCmd.ExecuteNonQuery();
                            }
                            else if (row["حالة الطلب"].ToString() == "يتطلب إجراءات")
                            {
                                // تجاهل التحديث للطلبات التي تتطلب إجراءات
                                continue;
                            }
                            else if (allNonEmptyColumnsSigned)
                            {
                                updateCmd.Parameters["@RequestStatus"].Value = "(D)";
                                updateCmd.Parameters["@OrderNumber"].Value = row["رقم الطلب"];
                                updateCmd.ExecuteNonQuery();
                            }
                        }
                    }
                }
            }
        }



        // هذه الدالة تُنفذ عند النقر على زر "رفض الطلب".
        // 1. التحقق من صلاحية المستخدم واستخراج اسم القسم.
        // 2. التحقق من تحديد رقم طلب صحيح.
        // 3. التحقق من أن حقل سبب الإعادة ليس فارغًا.
        // 4. تحديث حالة الطلب في قاعدة البيانات، حيث يتم استبدال الحالة السابقة بحالة "تم الإعادة" مع ذكر اسم الشخص الذي قام بالإعادة وسبب الإعادة.
        // 5. بعد نجاح عملية التحديث، يتم إخفاء تفاصيل الطلب وتحديث قائمة الطلبات المتاحة للمستخدم.
        // 6. إذا حدث خطأ أثناء العملية، يتم عرض رسالة خطأ مناسبة.

        protected void btnRejectOrder_Click(object sender, EventArgs e)
        {
            string department = Session["UserPermission"]?.ToString().Trim();
            if (department != null && department.Length > 5)
            {
                department = department.Substring(5);// تقليص اسم القسم ليصبح فقط الاسم دون "مشرف"
            }

            // التحقق من اختيار رقم طلب صحيح
            if (ddlOrderNumbers.SelectedIndex == 0)
            {
                LabelError.Text = "يرجى اختيار رقم الطلب.";
                LabelError.Visible = true;
                LabelMessage.Visible = false;
                return;
            }
            string rejectReason = txtRejectReason.Text.Trim();

            // التحقق من إدخال سبب الإعادة
            if (string.IsNullOrEmpty(rejectReason))
            {
                LabelError.Text = "يرجى إدخال سبب الإعادة.";
                LabelError.Visible = true;
                return;
            }

            string selectedOrderNumber = ddlOrderNumbers.SelectedValue; // الحصول على رقم الطلب المحدد
            string confirmedBy = Session["Username"]?.ToString(); // استرجاع اسم المستخدم من الجلسة
            string connectionString = ConfigurationManager.ConnectionStrings["MyConnectionString"].ConnectionString;

            using (SqlConnection con = new SqlConnection(connectionString))
            {
                // تحديث حالة الطلب في العمود المناسب بناءً على القسم
                string query = $"UPDATE ordersTable SET [مشرف {department}] = @ConfirmedBy WHERE [رقم الطلب] = @OrderNumber";
                using (SqlCommand cmd = new SqlCommand(query, con))
                {
                    cmd.Parameters.AddWithValue("@OrderNumber", selectedOrderNumber);
                    cmd.Parameters.AddWithValue("@ConfirmedBy", $"تم الإعادة من قبل {confirmedBy} ({rejectReason})");
                    //cmd.Parameters.AddWithValue("@RejectReason", rejectReason);


                    con.Open();
                    int rowsAffected = cmd.ExecuteNonQuery();

                    if (rowsAffected > 0)
                    {
                        LabelMessage.Text = "تم إعادة الطلب بنجاح.";
                        OrderDetailsPanel.Visible = false;
                        LabelMessage.Visible = true;
                        LabelError.Visible = false;
                        UpdateOrderStatuses();// تحديث حالات الطلبات بعد التحديث
                        PopulateOrderNumbers(); // تحديث قائمة الطلبات بعد إعادة الطلب

                        SiteMaster master = (SiteMaster)Master;
                        if (master != null)
                        {
                            master.UpdateNewOrdersCount();// تحديث عدد الطلبات الجديدة في واجهة المستخدم
                        }
                        // تنظيف الحقول بعد النجاح
                        ClearFields(); // استدعاء دالة لتنظيف الحقول
                    }
                    else
                    {
                        LabelError.Text = "حدث خطأ أثناء تأكيد الطلب.";
                        LabelError.Visible = true;
                        LabelMessage.Visible = false;
                    }
                }
            }
        }
        // إضافة متغير connectionString
        string connectionString = ConfigurationManager.ConnectionStrings["MyConnectionString"].ConnectionString;

        // معالجة يتطلب إجراءات
        protected void btnNeedsAction_Click(object sender, EventArgs e)
        {
            string orderNumber = ddlOrderNumbers.SelectedValue;
            string actionDetails = txtActionRequired.Text.Trim();
            string confirmedBy = Session["Username"]?.ToString();
            string department = Session["UserPermission"]?.ToString().Trim();
            string currentDate = DateTime.Now.ToString("yyyy-MM-dd");

            if (string.IsNullOrEmpty(actionDetails) || ddlOrderNumbers.SelectedIndex == 0)
            {
                LabelError.Text = string.IsNullOrEmpty(actionDetails) ?
                    "الرجاء إدخال الإجراءات المطلوبة" : "يرجى اختيار رقم الطلب";
                LabelError.Visible = true;
                return;
            }

            if (department?.Length > 5)
                department = department.Substring(5);

            using (SqlConnection con = new SqlConnection(connectionString))
            {
                // First get existing notes
                string selectQuery = "SELECT [ملاحظات المشرفين] FROM ordersTable WHERE [رقم الطلب] = @OrderNumber";
                string existingNotes = "";

                using (SqlCommand cmdSelect = new SqlCommand(selectQuery, con))
                {
                    cmdSelect.Parameters.AddWithValue("@OrderNumber", orderNumber);
                    con.Open();
                    var result = cmdSelect.ExecuteScalar();
                    existingNotes = result != DBNull.Value ? result.ToString() : "";
                }

                // Append new action details to existing notes
                string newNotes = string.IsNullOrEmpty(existingNotes)
                    ? $"{department}: {actionDetails}"
                    : $"{existingNotes}\n{department}: {actionDetails}";

                string updateQuery = $@"UPDATE ordersTable 
            SET [مشرف {department}] = @ActionStatus,
                [حالة الطلب] = N'يتطلب إجراءات من المشرف',
                [ملاحظات المشرفين] = @ActionDetails
            WHERE [رقم الطلب] = @OrderNumber";

                using (SqlCommand cmdUpdate = new SqlCommand(updateQuery, con))
                {
                    cmdUpdate.Parameters.AddWithValue("@ActionStatus",
                        $"{currentDate} | يتطلب إجراءات - {confirmedBy}");
                    cmdUpdate.Parameters.AddWithValue("@ActionDetails", newNotes);
                    cmdUpdate.Parameters.AddWithValue("@OrderNumber", orderNumber);

                    try
                    {
                        int result = cmdUpdate.ExecuteNonQuery();

                        if (result > 0)
                        {
                            LabelMessage.Text = "تم حفظ الإجراءات المطلوبة بنجاح";
                            LabelMessage.Visible = true;
                            LabelError.Visible = false;
                            OrderDetailsPanel.Visible = false;
                            ClearFields();
                            PopulateOrderNumbers();
                            UpdateOrderStatuses();
                        }
                        else
                        {
                            LabelError.Text = "لم يتم العثور على الطلب";
                            LabelError.Visible = true;
                        }
                    }
                    catch (Exception ex)
                    {
                        LabelError.Text = "حدث خطأ في النظام: " + ex.Message;
                        LabelError.Visible = true;
                    }
                }
            }
        }
        // دالة لتنظيف الحقول
        private void ClearFields()
        {
            ddlOrderNumbers.SelectedIndex = 0; // إعادة تعيين قائمة الطلبات
            txtRejectReason.Text = string.Empty; // تفريغ حقل سبب الإعادة
        }

        // أضف هذه الدالة مع باقي الدوال في الصفحة
        private void LoadFollowUpRecords()
        {
            string department = Session["UserPermission"]?.ToString().Trim();
            if (department != null && department.Length > 5)
            {
                department = department.Substring(5);
            }

            using (SqlConnection con = new SqlConnection(connectionString))
            {
                string query = @"SELECT SupervisorID, CivilRegistry, OwnerName, SpecialProcedure 
                        FROM SupervisorsFollowUp 
                        WHERE SupervisorID = @SupervisorID 
                        ORDER BY OwnerName ASC"; // تغيير الترتيب ليكون حسب اسم صاحب الطلب

                using (SqlCommand cmd = new SqlCommand(query, con))
                {
                    cmd.Parameters.Add("@SupervisorID", SqlDbType.NVarChar, 100).Value = department;

                    try
                    {
                        con.Open();
                        SqlDataAdapter da = new SqlDataAdapter(cmd);
                        DataTable dt = new DataTable();
                        da.Fill(dt);
                        gvFollowUpRecords.DataSource = dt;
                        gvFollowUpRecords.DataBind();
                    }
                    catch (Exception ex)
                    {
                        LabelError.Text = "خطأ في تحميل البيانات: " + ex.Message;
                        LabelError.Visible = true;
                    }
                }
            }
        }

        protected void btnAddFollowUp_Click(object sender, EventArgs e)
        {
            string department = Session["UserPermission"]?.ToString().Trim();
            if (department != null && department.Length > 5)
            {
                department = department.Substring(5);
            }

            string civilRegistry = txtCivilRegistry.Text.Trim();
            string ownerName = txtOwnerName.Text.Trim();
            string specialProcedure = txtSpecialProcedure.Text.Trim();

            if (string.IsNullOrEmpty(civilRegistry))
            {
                LabelError.Text = "الرجاء إدخال السجل المدني";
                LabelError.Visible = true;
                return;
            }

            using (SqlConnection con = new SqlConnection(connectionString))
            {
                // التحقق من وجود السجل المدني مسبقًا
                string checkQuery = @"SELECT COUNT(*) FROM SupervisorsFollowUp 
                              WHERE CivilRegistry = @CivilRegistry AND SupervisorID = @SupervisorID";

                using (SqlCommand checkCmd = new SqlCommand(checkQuery, con))
                {
                    checkCmd.Parameters.Add("@SupervisorID", SqlDbType.NVarChar, 100).Value = department;
                    checkCmd.Parameters.Add("@CivilRegistry", SqlDbType.NVarChar, 50).Value = civilRegistry;

                    try
                    {
                        con.Open();
                        int count = (int)checkCmd.ExecuteScalar();
                        if (count > 0)
                        {
                            LabelError.Text = "السجل المدني موجود بالفعل.";
                            LabelError.Visible = true;
                            return;
                        }
                    }
                    catch (Exception ex)
                    {
                        LabelError.Text = "خطأ في التحقق من السجل: " + ex.Message;
                        LabelError.Visible = true;
                        return;
                    }
                }

                // إذا لم يكن السجل موجودًا، يتم إضافة السجل الجديد
                string query = @"INSERT INTO SupervisorsFollowUp 
                         (SupervisorID, CivilRegistry, OwnerName, SpecialProcedure)
                         VALUES (@SupervisorID, @CivilRegistry, @OwnerName, @SpecialProcedure)";

                using (SqlCommand cmd = new SqlCommand(query, con))
                {
                    cmd.Parameters.Add("@SupervisorID", SqlDbType.NVarChar, 100).Value = department;
                    cmd.Parameters.Add("@CivilRegistry", SqlDbType.NVarChar, 50).Value = civilRegistry;
                    cmd.Parameters.Add("@OwnerName", SqlDbType.NVarChar, 100).Value = ownerName;
                    cmd.Parameters.Add("@SpecialProcedure", SqlDbType.NVarChar, 200).Value = specialProcedure;

                    try
                    {
                        cmd.ExecuteNonQuery();
                        ClearFollowUpForm();
                        LoadFollowUpRecords();
                        LabelMessage.Text = "تم إضافة السجل بنجاح";
                        LabelMessage.Visible = true;
                        LabelError.Visible = false;
                    }
                    catch (Exception ex)
                    {
                        LabelError.Text = "خطأ في إضافة السجل: " + ex.Message;
                        LabelError.Visible = true;
                    }
                }
            }
        }


        protected void gvFollowUpRecords_RowCommand(object sender, GridViewCommandEventArgs e)
        {
            string civilRegistry = e.CommandArgument.ToString();

            if (e.CommandName == "EditRecord")
            {
                LoadRecordForEdit(civilRegistry);
            }
            else if (e.CommandName == "DeleteRecord")
            {
                DeleteFollowUpRecord(civilRegistry);
            }
        }


        private void LoadRecordForEdit(string civilRegistry)
        {
            string department = Session["UserPermission"]?.ToString().Trim();
            if (department?.Length > 5)
            {
                department = department.Substring(5);
            }

            using (SqlConnection con = new SqlConnection(connectionString))
            {
                string query = @"SELECT CivilRegistry, OwnerName, SpecialProcedure 
                        FROM SupervisorsFollowUp 
                        WHERE CivilRegistry = @CivilRegistry 
                        AND SupervisorID = @SupervisorID";

                using (SqlCommand cmd = new SqlCommand(query, con))
                {
                    cmd.Parameters.AddWithValue("@CivilRegistry", civilRegistry);
                    cmd.Parameters.AddWithValue("@SupervisorID", department);

                    try
                    {
                        con.Open();
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                txtEditCivilRegistry.Text = reader["CivilRegistry"].ToString();
                                txtEditOwnerName.Text = reader["OwnerName"].ToString();
                                txtEditSpecialProcedure.Text = reader["SpecialProcedure"].ToString();
                                EditPanel.Visible = true;
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        LabelError.Text = "خطأ في تحميل البيانات: " + ex.Message;
                        LabelError.Visible = true;
                    }
                }
            }
        }

        protected void btnUpdateRecord_Click(object sender, EventArgs e)
        {
            string department = Session["UserPermission"]?.ToString().Trim();
            if (department?.Length > 5)
            {
                department = department.Substring(5);
            }

            using (SqlConnection con = new SqlConnection(connectionString))
            {
                string query = @"UPDATE SupervisorsFollowUp 
                        SET OwnerName = @OwnerName,
                            SpecialProcedure = @SpecialProcedure
                        WHERE CivilRegistry = @CivilRegistry 
                        AND SupervisorID = @SupervisorID";

                using (SqlCommand cmd = new SqlCommand(query, con))
                {
                    cmd.Parameters.AddWithValue("@SupervisorID", department);
                    cmd.Parameters.AddWithValue("@CivilRegistry", txtEditCivilRegistry.Text.Trim());
                    cmd.Parameters.AddWithValue("@OwnerName", txtEditOwnerName.Text.Trim());
                    cmd.Parameters.AddWithValue("@SpecialProcedure", txtEditSpecialProcedure.Text.Trim());

                    try
                    {
                        con.Open();
                        int result = cmd.ExecuteNonQuery();
                        if (result > 0)
                        {
                            EditPanel.Visible = false;
                            LoadFollowUpRecords();
                            LabelMessage.Text = "تم تحديث السجل بنجاح";
                            LabelMessage.Visible = true;
                        }
                    }
                    catch (Exception ex)
                    {
                        LabelError.Text = "خطأ في تحديث البيانات: " + ex.Message;
                        LabelError.Visible = true;
                    }
                }
            }
        }

        protected void btnCancelEdit_Click(object sender, EventArgs e)
        {
            EditPanel.Visible = false;
        }

        private void DeleteFollowUpRecord(string civilRegistry)
        {
            string department = Session["UserPermission"]?.ToString().Trim();
            if (department?.Length > 5)
            {
                department = department.Substring(5);
            }

            using (SqlConnection con = new SqlConnection(connectionString))
            {
                string query = @"DELETE FROM SupervisorsFollowUp 
                        WHERE CivilRegistry = @CivilRegistry 
                        AND SupervisorID = @SupervisorID";

                using (SqlCommand cmd = new SqlCommand(query, con))
                {
                    cmd.Parameters.AddWithValue("@CivilRegistry", civilRegistry);
                    cmd.Parameters.AddWithValue("@SupervisorID", department);

                    try
                    {
                        con.Open();
                        int result = cmd.ExecuteNonQuery();
                        if (result > 0)
                        {
                            LoadFollowUpRecords();
                            LabelMessage.Text = "تم حذف السجل بنجاح";
                            LabelMessage.Visible = true;
                        }
                    }
                    catch (Exception ex)
                    {
                        LabelError.Text = "خطأ في حذف السجل: " + ex.Message;
                        LabelError.Visible = true;
                    }
                }
            }
        }




        private void ClearFollowUpForm()
        {
            txtCivilRegistry.Text = "";
            txtOwnerName.Text = "";
            txtSpecialProcedure.Text = "";
        }
        protected void btnImportCSV_Click(object sender, EventArgs e)
        {
            if (!fileUploadCSV.HasFile)
            {
                ShowError("الرجاء اختيار ملف للاستيراد");
                return;
            }

            // الحصول على معرف المشرف من الجلسة
            string department = Session["UserPermission"]?.ToString().Trim();
            if (string.IsNullOrEmpty(department))
            {
                ShowError("لم يتم العثور على معرف المشرف");
                return;
            }

            if (department.Length > 5)
            {
                department = department.Substring(5);
            }

            try
            {
                using (StreamReader sr = new StreamReader(fileUploadCSV.FileContent))
                {
                    int successCount = 0;
                    int errorCount = 0;
                    bool isFirstLine = true;
                    StringBuilder errorMessages = new StringBuilder();
                    HashSet<string> existingCivilRegistries = new HashSet<string>();

                    using (SqlConnection con = new SqlConnection(connectionString))
                    {
                        con.Open();
                        using (SqlTransaction transaction = con.BeginTransaction())
                        {
                            try
                            {
                                string line;
                                while ((line = sr.ReadLine()) != null)
                                {
                                    // تجاهل السطر الأول (العناوين)
                                    if (isFirstLine)
                                    {
                                        isFirstLine = false;
                                        continue;
                                    }

                                    if (string.IsNullOrWhiteSpace(line))
                                        continue;

                                    string[] fields = line.Split(';');
                                    if (fields.Length < 4)
                                    {
                                        errorCount++;
                                        errorMessages.AppendLine("خطأ: السطر لا يحتوي على جميع الحقول المطلوبة");
                                        continue;
                                    }

                                    string civilRegistry = fields[1].Trim();

                                    // التحقق من صحة السجل المدني
                                    if (!ValidateCivilRegistry(civilRegistry))
                                    {
                                        errorCount++;
                                        errorMessages.AppendLine($"خطأ: السجل المدني غير صالح: {civilRegistry}");
                                        continue;
                                    }

                                    // التحقق من عدم التكرار في نفس الملف
                                    if (existingCivilRegistries.Contains(civilRegistry))
                                    {
                                        errorCount++;
                                        errorMessages.AppendLine($"خطأ: السجل المدني مكرر في الملف: {civilRegistry}");
                                        continue;
                                    }

                                    existingCivilRegistries.Add(civilRegistry);

                                    // التحقق من وجود السجل في قاعدة البيانات
                                    string checkQuery = @"SELECT COUNT(*) FROM SupervisorsFollowUp 
                                               WHERE CivilRegistry = @CivilRegistry 
                                               AND SupervisorID = @SupervisorID";

                                    using (SqlCommand checkCmd = new SqlCommand(checkQuery, con, transaction))
                                    {
                                        checkCmd.Parameters.AddWithValue("@SupervisorID", department);
                                        checkCmd.Parameters.AddWithValue("@CivilRegistry", civilRegistry);

                                        if ((int)checkCmd.ExecuteScalar() > 0)
                                        {
                                            errorCount++;
                                            errorMessages.AppendLine($"السجل المدني موجود مسبقاً: {civilRegistry}");
                                            continue;
                                        }
                                    }

                                    // إدخال السجل
                                    string insertQuery = @"INSERT INTO SupervisorsFollowUp 
                                                (SupervisorID, CivilRegistry, OwnerName, SpecialProcedure) 
                                                VALUES 
                                                (@SupervisorID, @CivilRegistry, @OwnerName, @SpecialProcedure)";

                                    using (SqlCommand cmd = new SqlCommand(insertQuery, con, transaction))
                                    {
                                        cmd.Parameters.AddWithValue("@SupervisorID", department);
                                        cmd.Parameters.AddWithValue("@CivilRegistry", civilRegistry);
                                        cmd.Parameters.AddWithValue("@OwnerName", fields[2].Trim());
                                        cmd.Parameters.AddWithValue("@SpecialProcedure", fields[3].Trim());

                                        try
                                        {
                                            cmd.ExecuteNonQuery();
                                            successCount++;
                                        }
                                        catch (Exception ex)
                                        {
                                            errorCount++;
                                            errorMessages.AppendLine($"خطأ في إدخال السجل: {ex.Message}");
                                        }
                                    }
                                }

                                if (successCount > 0)
                                {
                                    transaction.Commit();
                                    ShowSuccess($"تم استيراد {successCount} سجل بنجاح" +
                                              (errorCount > 0 ? $". فشل استيراد {errorCount} سجل" : ""));
                                    if (errorCount > 0)
                                    {
                                        ShowError(errorMessages.ToString());
                                    }
                                    LoadFollowUpRecords();
                                }
                                else
                                {
                                    transaction.Rollback();
                                    ShowError("لم يتم استيراد أي سجلات. " + errorMessages.ToString());
                                }
                            }
                            catch (Exception)
                            {
                                transaction.Rollback();
                                throw;
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                ShowError("خطأ في استيراد الملف: " + ex.Message);
            }
        }

        private bool ValidateCivilRegistry(string civilRegistry)
        {
            if (string.IsNullOrWhiteSpace(civilRegistry) || civilRegistry.Length != 10)
                return false;

            return civilRegistry.All(char.IsDigit);
        }



        private void ShowError(string message)
        {
            LabelError.Text = message;
            LabelError.Visible = true;
        }
        private void ShowSuccess(string message)
        {
            LabelMessage.Text = message;
            LabelMessage.ForeColor = System.Drawing.Color.Green;
            LabelMessage.Visible = true;
        }





        protected void btnExportCSV_Click(object sender, EventArgs e)
        {
            string department = Session["UserPermission"]?.ToString().Trim();
            if (department?.Length > 5)
            {
                department = department.Substring(5);
            }

            try
            {
                using (SqlConnection con = new SqlConnection(connectionString))
                {
                    string query = @"SELECT SupervisorID, CivilRegistry, OwnerName, SpecialProcedure 
                           FROM SupervisorsFollowUp 
                           WHERE SupervisorID = @SupervisorID";

                    using (SqlCommand cmd = new SqlCommand(query, con))
                    {
                        cmd.Parameters.AddWithValue("@SupervisorID", department);
                        con.Open();

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            StringBuilder sb = new StringBuilder();

                            // إضافة عناوين الأعمدة
                            sb.AppendLine("SupervisorID;CivilRegistry;OwnerName;SpecialProcedure");

                            // إضافة البيانات
                            while (reader.Read())
                            {
                                sb.AppendLine($"{reader["SupervisorID"]};{reader["CivilRegistry"]};{reader["OwnerName"]};{reader["SpecialProcedure"]}");
                            }

                            Response.Clear();
                            Response.Buffer = true;
                            Response.AddHeader("content-disposition", "attachment;filename=FollowUpRecords.csv");
                            Response.Charset = "";
                            Response.ContentType = "application/text";
                            Response.ContentEncoding = System.Text.Encoding.UTF8;
                            Response.Output.Write("\uFEFF");
                            Response.Output.Write(sb.ToString());
                            Response.Flush();
                            Response.End();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LabelError.Text = "خطأ في تصدير البيانات: " + ex.Message;
                LabelError.Visible = true;
            }
        }

        protected void btnDeleteAll_Click(object sender, EventArgs e)
        {
            // تأكيد الحذف باستخدام JavaScript
            string confirmScript = "if(!confirm('هل أنت متأكد من حذف جميع البيانات الخاصة بك؟')) return false;";
            ClientScript.RegisterStartupScript(this.GetType(), "ConfirmDelete", confirmScript, true);

            try
            {
                string supervisorID = Session["UserPermission"]?.ToString().Trim();
                if (supervisorID?.Length > 5)
                {
                    supervisorID = supervisorID.Substring(5);
                }

                using (SqlConnection con = new SqlConnection(connectionString))
                {
                    con.Open();

                    string deleteQuery = "DELETE FROM SupervisorsFollowUp WHERE SupervisorID = @SupervisorID";

                    using (SqlCommand cmd = new SqlCommand(deleteQuery, con))
                    {
                        cmd.Parameters.AddWithValue("@SupervisorID", supervisorID);
                        int rowsAffected = cmd.ExecuteNonQuery();

                        if (rowsAffected > 0)
                        {
                            ShowSuccess($"تم حذف جميع البيانات الخاصة بك بنجاح. عدد السجلات المحذوفة: {rowsAffected}");
                        }
                        else
                        {
                            ShowError("لا توجد بيانات لحذفها.");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                ShowError("حدث خطأ أثناء عملية الحذف: " + ex.Message);
            }
        }


        private void CheckFollowUpAlert(string civilRegistry)
        {
            // إخفاء أي تنبيهات سابقة
            if (AutoPathInfoPanel != null)
            {
                AutoPathInfoPanel.InnerHtml = "";
                AutoPathInfoPanel.Visible = false;
            }

            string department = Session["UserPermission"]?.ToString().Trim();
            if (department?.Length > 5)
            {
                department = department.Substring(5);
            }

            using (SqlConnection con = new SqlConnection(connectionString))
            {
                string query = @"SELECT SpecialProcedure 
                        FROM SupervisorsFollowUp 
                        WHERE SupervisorID = @SupervisorID 
                        AND CivilRegistry = @CivilRegistry";

                using (SqlCommand cmd = new SqlCommand(query, con))
                {
                    cmd.Parameters.AddWithValue("@SupervisorID", department);
                    cmd.Parameters.AddWithValue("@CivilRegistry", civilRegistry);

                    try
                    {
                        con.Open();
                        var result = cmd.ExecuteScalar();

                        if (result != null)
                        {
                            string specialProcedure = result.ToString();
                            ShowFollowUpAlert(specialProcedure);
                        }
                    }
                    catch (Exception ex)
                    {
                        LabelError.Text = "خطأ في التحقق من التنبيهات: " + ex.Message;
                        LabelError.Visible = true;
                    }
                }
            }
        }

        private void ShowFollowUpAlert(string specialProcedure)
        {
            string script = $@"
    Swal.fire({{
        title: '⚠️ تنبيه: هذا الطلب يحتاج متابعة خاصة',
        html: 'التفاصيل: {specialProcedure}',
        icon: 'warning',
        confirmButtonText: 'حسناً'
    }});";
            ScriptManager.RegisterStartupScript(this, GetType(), "FollowUpAlert", script, true);

            // إضافة تنبيه مرئي في الصفحة
            string alertHtml = $@"<div class='alert alert-warning text-right' role='alert'>
        <h5 class='alert-heading'>⚠️ تنبيه: هذا الطلب يحتاج متابعة خاصة</h5>
        <hr>
        <p>التفاصيل: {specialProcedure}</p>
        <hr>
        <p class='mb-0'>يرجى مراجعة الإجراءات المطلوبة قبل المتابعة</p>
    </div>";

            // تأكد من وجود عنصر لعرض التنبيه في الصفحة
            if (AutoPathInfoPanel != null)
            {
                AutoPathInfoPanel.InnerHtml = alertHtml;
                AutoPathInfoPanel.Visible = true;
            }
        }
        protected void btnToggleRecords_Click(object sender, EventArgs e)
        {
            RecordsPanel.Visible = !RecordsPanel.Visible;
            btnToggleRecords.Text = RecordsPanel.Visible ? "إخفاء السجلات" : "إظهار السجلات";
        }

    }
}