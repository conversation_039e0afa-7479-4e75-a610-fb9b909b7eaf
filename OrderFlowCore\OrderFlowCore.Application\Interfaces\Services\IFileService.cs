using OrderFlowCore.Application.Common;
using OrderFlowCore.Core.Models;

namespace OrderFlowCore.Application.Interfaces.Services
{
    public interface IFileService
    {
        Task<ServiceResult<List<string>>> UploadFilesAsync(List<byte[]> files, string prefix = "file");
        Task<ServiceResult<string>> UploadFileAsync(byte[] fileData, string fileName, string prefix = "file");
        Task<ServiceResult<byte[]>> DownloadFilesZipAsync(List<string> fileUrls);
        Task<ServiceResult<byte[]>> CreateZipFromFilesAsync(List<string> fileUrls, string zipName);
        Task<ServiceResult<byte[]>> GenerateOrderPdfAsync(OrdersTable order);
        Task<ServiceResult> DeleteFileAsync(string fileUrl);
        Task<ServiceResult> DeleteFilesAsync(List<string> fileUrls);
        Task<ServiceResult<bool>> FileExistsAsync(string fileUrl);
        Task<ServiceResult<long>> GetFileSizeAsync(string fileUrl);
        Task<ServiceResult<string>> GetFileContentTypeAsync(string fileUrl);
    }
}