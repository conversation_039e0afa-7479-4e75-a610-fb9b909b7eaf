﻿
@{
    var currentController = ViewContext.RouteData.Values["Controller"]?.ToString() ?? "";
}

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>@ViewData["Title"] - Dashboard</title>

    @* Core Styles *@
    <partial name="_StylesPartial" />

    @* Page Specific Styles *@
    @await RenderSectionAsync("Styles", required: false)
</head>
<body class="hold-transition sidebar-mini layout-fixed">
    <div class="wrapper">
        @* Top Navigation Bar *@
        <partial name="_NavbarPartial" />

        @* Sidebar Navigation *@
        <partial name="_SidebarPartial" model="currentController" />

        @* Main Content Area *@
        <main class="content-wrapper">
            @RenderBody()
        </main>
    </div>

    @* Core Scripts *@
    <partial name="_ScriptsPartial" />

    @* Page Specific Scripts *@
    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
