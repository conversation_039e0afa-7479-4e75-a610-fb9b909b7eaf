using OrderFlowCore.Application.Interfaces.Services;

namespace OrderFlowCore.Web.ViewModels
{
    public class DashboardViewModel
    {
        public string Username { get; set; } = "";
        public string Role { get; set; } = "";
        public string Email { get; set; } = "";
        public DashboardStatisticsDto Statistics { get; set; } = new();
        public bool HasError { get; set; }
        public string ErrorMessage { get; set; } = "";
    }

    public class DashboardCardViewModel
    {
        public string Title { get; set; } = "";
        public string Value { get; set; } = "";
        public string Icon { get; set; } = "";
        public string Color { get; set; } = "";
        public string Change { get; set; } = "";
        public string ChangeType { get; set; } = ""; // "increase", "decrease", "neutral"
        public string Link { get; set; } = "";
    }

    public class ChartDataViewModel
    {
        public List<string> Labels { get; set; } = new();
        public List<int> Data { get; set; } = new();
        public List<string> Colors { get; set; } = new();
        public string ChartType { get; set; } = ""; // "pie", "bar", "line", "doughnut"
    }
}
