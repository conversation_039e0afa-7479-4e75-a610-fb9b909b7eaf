﻿using abozyad.Helpers;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Spreadsheet;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data.SqlClient;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Web.UI.WebControls;
using Text = DocumentFormat.OpenXml.Wordprocessing.Text;




namespace abozyad
{
    public partial class WebForm7 : System.Web.UI.Page
    {


        // دالة Page_Load تتحقق من صلاحية المستخدم عند تحميل الصفحة.
        // إذا كانت الصلاحية غير موجودة أو كانت "مدير حسابات"، يتم إعادة توجيه المستخدم إلى صفحة رفض الوصول.
        // إذا كانت الصفحة تُحمّل لأول مرة (وليس بعد أي إجراء مستخدم)، يتم استدعاء دالة PopulateOrderNumbers لملء قائمة أرقام الطلبات.
        protected void Page_Load(object sender, EventArgs e)
        {
            // قائمة الصلاحيات المسموح لها بالدخول
            string[] allowedPermissions = {
        "منسق الموارد البشرية",
        "مدير الموارد البشرية",
        "مدير حسابات",
        "مشرف قسم الملفات"
    };
            Session["UserPermission"] = "مدير حسابات";
            // التحقق من صلاحية المستخدم
            if (Session["UserPermission"] == null || !allowedPermissions.Contains(Session["UserPermission"].ToString()))
            {
                // إذا لم يكن للمستخدم صلاحية، يتم توجيهه إلى صفحة رفض الوصول
                Session["AccessDeniedMessage"] = "ليس لديك صلاحية للوصول إلى هذه الصفحة.";
                Response.Redirect("AccessDenied.aspx");
            }

            // تحميل البيانات عند تحميل الصفحة لأول مرة
            if (!IsPostBack)
            {
                PopulatePrintOrders();
            }
        }

        private string CleanFileName(string fileName)
        {
            // إزالة الأحرف غير المسموح بها في أسماء الملفات
            char[] invalidChars = System.IO.Path.GetInvalidFileNameChars();
            foreach (char c in invalidChars)
            {
                fileName = fileName.Replace(c, '_');
            }

            // التأكد من أن الاسم لا يتجاوز طولاً معيناً
            const int maxLength = 100;
            if (fileName.Length > maxLength)
            {
                string extension = System.IO.Path.GetExtension(fileName);
                fileName = fileName.Substring(0, maxLength - extension.Length) + extension;
            }

            return fileName;
        }

        protected void btnDownload_Click(object sender, EventArgs e)
        {
            try
            {
                if (ddlPrintOrders.SelectedValue == "")
                {
                    LabelError.Text = "يرجى اختيار رقم طلب من القائمة";
                    LabelError.Visible = true;
                    return;
                }
                string selectedOrderNumber = ddlPrintOrders.SelectedValue;

                string connectionString = ConfigurationManager.ConnectionStrings["MyConnectionString"].ConnectionString;

                using (SqlConnection con = new SqlConnection(connectionString))
                {
                    string query = @"SELECT ملف1, ملف2, ملف3, ملف4, [اسم الموظف] 
                       FROM ordersTable 
                       WHERE [رقم الطلب] = @OrderNumber";

                    using (SqlCommand cmd = new SqlCommand(query, con))
                    {
                        cmd.Parameters.AddWithValue("@OrderNumber", selectedOrderNumber);
                        con.Open();

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                string employeeName = reader["اسم الموظف"].ToString().Trim();
                                bool filesFound = false;

                                using (MemoryStream zipStream = new MemoryStream())
                                {
                                    using (ZipArchive zipArchive = new ZipArchive(zipStream, ZipArchiveMode.Create, true))
                                    {
                                        for (int i = 0; i < 4; i++)
                                        {
                                            byte[] compressedData = reader[i] as byte[];
                                            if (compressedData != null && compressedData.Length > 0)
                                            {
                                                try
                                                {
                                                    byte[] pdfData = FileCompressor.ExtractFile(compressedData);

                                                    if (pdfData != null && IsPdfFile(pdfData))
                                                    {
                                                        filesFound = true;
                                                        string fileName = CleanFileName($"مرفق_{i + 1}_طلب_{selectedOrderNumber}_{employeeName}.pdf");

                                                        ZipArchiveEntry zipEntry = zipArchive.CreateEntry(fileName);
                                                        using (Stream entryStream = zipEntry.Open())
                                                        {
                                                            entryStream.Write(pdfData, 0, pdfData.Length);
                                                        }
                                                    }
                                                }
                                                catch (Exception ex)
                                                {
                                                    System.Diagnostics.Debug.WriteLine($"❌ خطأ في معالجة الملف {i + 1}: {ex.Message}");
                                                    continue;
                                                }
                                            }
                                        }
                                    }

                                    if (filesFound)
                                    {
                                        string zipFileName = CleanFileName($"مرفقات_طلب_{selectedOrderNumber}_{employeeName}.zip");

                                        Response.Clear();
                                        Response.ContentType = "application/zip";
                                        Response.AddHeader("Content-Disposition", $"attachment; filename={zipFileName}");
                                        Response.BinaryWrite(zipStream.ToArray());
                                        Response.Flush();
                                        Response.End();
                                    }
                                    else
                                    {
                                        LabelError.Text = "لا توجد ملفات صالحة لتحميلها.";
                                        LabelError.Visible = true;
                                    }
                                }
                            }
                            else
                            {
                                LabelError.Text = "لم يتم العثور على الطلب.";
                                LabelError.Visible = true;
                            }
                        }
                    }
                }
            }
            catch (System.Threading.ThreadAbortException)
            {
                // تجاهل
            }
            catch (Exception ex)
            {
                LabelError.Text = "حدث خطأ أثناء تحميل الملفات. الرجاء المحاولة مرة أخرى.";
                LabelError.Visible = true;
                System.Diagnostics.Debug.WriteLine($"❌ خطأ عام: {ex.Message}");
            }
        }


        private bool IsPdfFile(byte[] data)
        {
            if (data == null || data.Length < 4)
                return false;

            // التحقق من PDF header
            return data[0] == 0x25 && // %
                   data[1] == 0x50 && // P
                   data[2] == 0x44 && // D
                   data[3] == 0x46;   // F
        }



        /// <summary>
        /// تحديد امتداد الملف بناءً على محتواه - تم تحسينها للتعامل مع PDF فقط
        /// </summary>
        private string DetermineFileExtension(byte[] fileData)
        {
            return ValidatePdfFormat(fileData) ? ".pdf" : null;
        }

        /// <summary>
        /// التحقق من أن الملف هو PDF صالح
        /// </summary>
        private bool ValidatePdfFormat(byte[] data)
        {
            try
            {
                if (data == null || data.Length < 4)
                {
                    System.Diagnostics.Debug.WriteLine("❌ البيانات فارغة أو قصيرة جداً");
                    return false;
                }

                // فحص توقيع PDF (PDF Signature)
                bool isPdf = data[0] == 0x25 && // %
                            data[1] == 0x50 && // P
                            data[2] == 0x44 && // D
                            data[3] == 0x46;   // F

                if (!isPdf)
                {
                    System.Diagnostics.Debug.WriteLine("❌ الملف ليس بصيغة PDF صالحة");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("✅ تم التحقق من صحة ملف PDF");
                }

                return isPdf;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في فحص نوع الملف: {ex.Message}");
                return false;
            }
        }






        // دالة LoadOrderDetails تستقبل رقم الطلب وتقوم بالاتصال بقاعدة البيانات لجلب التفاصيل المتعلقة بالطلب.
        // يتم استرجاع معلومات الطلب مثل رقم الطلب، حالة الطلب، نوع الطلب، تفاصيل مقدم الطلب، 
        // والموافقات من قبل المشرفين. بعد ذلك، يتم عرض التفاصيل في الصفحة.
        // إذا تم العثور على الطلب، يتم عرض التفاصيل في الحقول المناسبة، وإذا لم يتم العثور على الطلب، 
        // يتم إخفاء لوحة التفاصيل.

        private void LoadOrderDetails(string orderNumber)
        {
            string connectionString = ConfigurationManager.ConnectionStrings["MyConnectionString"].ConnectionString;

            using (SqlConnection con = new SqlConnection(connectionString))
            {
                string query = @"SELECT [رقم الطلب], [تاريخ الطلب], [حالة الطلب], [نوع الطلب], [اسم الموظف],
                [القسم], [تفاصيل مقدم الطلب], [الوظيفة], [رقم الموظف], [السجل المدني], 
                [الجنسية], [رقم الجوال], [نوع التوظيف], [المؤهل], 
                [تم التأكيد/الإلغاء من مدير القسم], [تم التأكيد/الإلغاء من قبل المشرف], 
                [تم التحويل/الإلغاء من قبل المنسق], [سبب الإلغاء/الإعادة], [تفاصيل المنسق], 
                [مشرف خدمات الموظفين], [مشرف إدارة تخطيط الموارد البشرية], 
                [مشرف إدارة تقنية المعلومات], [مشرف مراقبة الدوام], [مشرف السجلات الطبية], 
                [مشرف إدارة الرواتب والاستحقاقات], [مشرف إدارة القانونية والالتزام], 
                [مشرف خدمات الموارد البشرية], [مشرف إدارة الإسكان], [مشرف قسم الملفات], 
                [مشرف العيادات الخارجية], [مشرف التأمينات الاجتماعية], [مشرف وحدة مراقبة المخزون], 
                [مشرف إدارة تنمية الإيرادات], [مشرف إدارة الأمن و السلامة], [مشرف الطب الاتصالي], 
                [مدير الموارد البشرية], [نوع التحويل], [ملاحظات المشرفين], 
                [ملف1], [ملف2], [ملف3], [ملف4]  
            FROM OrdersTable WHERE [رقم الطلب] = @OrderNumber";

                using (SqlCommand cmd = new SqlCommand(query, con))
                {
                    cmd.Parameters.AddWithValue("@OrderNumber", orderNumber);
                    con.Open();
                    SqlDataReader reader = cmd.ExecuteReader();

                    if (reader.Read())
                    {
                        SetLabelVisibilityAndText(LabelOrderNumber, reader["رقم الطلب"].ToString());
                        SetLabelVisibilityAndText(LabelOrderDate, reader["تاريخ الطلب"].ToString());
                        SetLabelVisibilityAndText(LabelOrderStatus, reader["حالة الطلب"].ToString());
                        SetLabelVisibilityAndText(LabelOrderType, reader["نوع الطلب"].ToString());
                        SetLabelVisibilityAndText(LabelEmployeeName, reader["اسم الموظف"].ToString());
                        SetLabelVisibilityAndText(LabelDepartment, reader["القسم"].ToString());
                        SetLabelVisibilityAndText(LabelNotes, reader["تفاصيل مقدم الطلب"].ToString());
                        SetLabelVisibilityAndText(LabelJobTitle, reader["الوظيفة"].ToString());
                        SetLabelVisibilityAndText(LabelEmployeeNumber, reader["رقم الموظف"].ToString());
                        SetLabelVisibilityAndText(LabelCivilRegistry, reader["السجل المدني"].ToString());
                        SetLabelVisibilityAndText(LabelNationality, reader["الجنسية"].ToString());
                        SetLabelVisibilityAndText(LabelMobileNumber, reader["رقم الجوال"].ToString());
                        SetLabelVisibilityAndText(LabelEmploymentType, reader["نوع التوظيف"].ToString());
                        SetLabelVisibilityAndText(LabelQualification, reader["المؤهل"].ToString());
                        SetLabelVisibilityAndText(LabelManagerApproval, reader["تم التأكيد/الإلغاء من مدير القسم"].ToString());
                        SetLabelVisibilityAndText(LabelSupervisorApproval, reader["تم التأكيد/الإلغاء من قبل المشرف"].ToString());
                        SetLabelVisibilityAndText(LabelCoordinatorApproval, reader["تم التحويل/الإلغاء من قبل المنسق"].ToString());
                        SetLabelVisibilityAndText(LabelCancellationReason, reader["سبب الإلغاء/الإعادة"].ToString());
                        SetLabelVisibilityAndText(LabelCoordinatorDetails, reader["تفاصيل المنسق"].ToString());
                        SetLabelVisibilityAndText1(LabelMedicalServicesPermission, reader["مشرف خدمات الموظفين"].ToString());
                        SetLabelVisibilityAndText1(LabelHRPlanningPermission, reader["مشرف إدارة تخطيط الموارد البشرية"].ToString());
                        SetLabelVisibilityAndText1(LabelITPermission, reader["مشرف إدارة تقنية المعلومات"].ToString());
                        SetLabelVisibilityAndText1(LabelAttendanceControlPermission, reader["مشرف مراقبة الدوام"].ToString());
                        SetLabelVisibilityAndText1(LabelMedicalRecordsPermission, reader["مشرف السجلات الطبية"].ToString());
                        SetLabelVisibilityAndText1(LabelPayrollPermission, reader["مشرف إدارة الرواتب والاستحقاقات"].ToString());
                        SetLabelVisibilityAndText1(LabelLegalCompliancePermission, reader["مشرف إدارة القانونية والالتزام"].ToString());
                        SetLabelVisibilityAndText1(LabelHRServicesPermission, reader["مشرف خدمات الموارد البشرية"].ToString());
                        SetLabelVisibilityAndText1(LabelHousingPermission, reader["مشرف إدارة الإسكان"].ToString());
                        SetLabelVisibilityAndText1(LabelFilesSectionPermission, reader["مشرف قسم الملفات"].ToString());
                        SetLabelVisibilityAndText1(LabelOutpatientPermission, reader["مشرف العيادات الخارجية"].ToString());
                        SetLabelVisibilityAndText1(LabelSocialInsurancePermission, reader["مشرف التأمينات الاجتماعية"].ToString());
                        SetLabelVisibilityAndText1(LabelInventoryControlPermission, reader["مشرف وحدة مراقبة المخزون"].ToString());
                        SetLabelVisibilityAndText1(LabelSelfResourcesPermission, reader["مشرف إدارة تنمية الإيرادات"].ToString());
                        SetLabelVisibilityAndText1(LabelNursingPermission, reader["مشرف إدارة الأمن و السلامة"].ToString());
                        SetLabelVisibilityAndText1(LabelEmployeeServicesPermission, reader["مشرف الطب الاتصالي"].ToString());
                        SetLabelVisibilityAndText(LabelHRManagerApproval, reader["مدير الموارد البشرية"].ToString());
                        OrderDetailsPanel.Visible = true;
                    }
                    else
                    {
                        OrderDetailsPanel.Visible = false;
                    }
                }
            }
        }

        // دالة SetLabelVisibilityAndText تقوم بتعيين النص للـ Label والتحقق من صحته.
        // إذا كان النص غير فارغ، يتم التحقق إذا كان النص عبارة عن تاريخ صحيح، 
        // وفي هذه الحالة يتم تنسيقه وعرضه. 
        // إذا لم يكن النص عبارة عن تاريخ، يتم عرض النص كما هو.
        // إذا كان النص فارغاً، يتم إخفاء الـ Label.

        private void SetLabelVisibilityAndText(Label label, string text)
        {
            if (!string.IsNullOrWhiteSpace(text))
            {


                DateTime dateValue;
                if (DateTime.TryParse(text, out dateValue))
                {
                    label.Text = dateValue.ToString("yyyy-MM-dd");
                    label.Visible = true;
                }
                else
                {

                    label.Text = text;
                    label.Visible = true;
                }
            }
            else
            {
                label.Visible = false;
            }


        }

        // دالة SetLabelVisibilityAndText1 تقوم بمعالجة نص المشرف باستخدام دالة FormatSupervisorText
        // يتم تمرير النص بدون حذف البيانات لتتم معالجته بشكل كامل (false).
        // يتم تعيين النص المعالج للتسمية (Label) وجعلها مرئية.

        private void SetLabelVisibilityAndText1(Label label, string text)
        {
            // استخدم FormatSupervisorText لمعالجة النص
            string processedText = FormatSupervisorText(text, false); // لأننا نريد النص كاملًا نمرر 'false'

            // عرض النص في التسمية
            label.Visible = true;
            label.Text = processedText;
        }



        // دالة btnConfirmOrder_Click تتأكد من أن المستخدم قد اختار رقم طلب صحيح من القائمة.
        // ثم تسترجع تفاصيل الطلب المحدد من قاعدة البيانات.
        // يتم فتح قالب مستند Word واستبدال الإشارات المرجعية بمعلومات الطلب (مثل اسم الموظف، تفاصيل الطلب، وغيرها).
        // بعد ذلك، يتم تحويل المستند إلى صيغة PDF وتقديمه للمستخدم للتنزيل.
        // في حالة وجود أي خطأ (مثل عدم اختيار رقم الطلب أو عدم وجود الطلب في قاعدة البيانات) يتم عرض رسالة خطأ.


        protected void btnConfirmOrder_Click(object sender, EventArgs e)
        {
            if (ddlPrintOrders.SelectedValue == "")
            {
                LabelError.Text = "يرجى اختيار رقم طلب من القائمة";
                LabelError.Visible = true;
                return;
            }

            string selectedOrderNumber = ddlPrintOrders.SelectedValue;
            string connectionString = ConfigurationManager.ConnectionStrings["MyConnectionString"].ConnectionString;

            using (SqlConnection con = new SqlConnection(connectionString))
            {
                string query = @"SELECT * FROM ordersTable WHERE [رقم الطلب] = @OrderNumber";

                using (SqlCommand cmd = new SqlCommand(query, con))
                {
                    cmd.Parameters.AddWithValue("@OrderNumber", selectedOrderNumber);
                    con.Open();

                    using (SqlDataReader reader = cmd.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            // إنشاء مسارات الملفات
                            string templatePath = Server.MapPath("~/App_Data/Form.docx");
                            string outputDocxPath = Server.MapPath($"~/App_Data/{selectedOrderNumber}.docx");
                            string outputPdfPath = Server.MapPath($"~/App_Data/{selectedOrderNumber}.pdf");

                            // نسخ القالب
                            File.Copy(templatePath, outputDocxPath, true);

                            // معالجة مستند الوورد
                            using (WordprocessingDocument wordDoc = WordprocessingDocument.Open(outputDocxPath, true))
                            {
                                var bookmarks = wordDoc.MainDocumentPart.RootElement
                                    .Descendants<DocumentFormat.OpenXml.Wordprocessing.BookmarkStart>()
                                    .ToList();

                                foreach (var bookmark in bookmarks)
                                {
                                    string bookmarkName = bookmark.Name.Value;
                                    string text = GetBookmarkValue(bookmarkName, reader);

                                    if (!string.IsNullOrEmpty(text))
                                    {
                                        text = SanitizeText(text);
                                        InsertTextAtBookmark(bookmark, text, true);
                                    }
                                }
                            }

                            // التحويل إلى PDF
                            ConvertDocxToPdf(outputDocxPath, outputPdfPath);

                            // إعداد التنزيل
                            string employeeName = reader["اسم الموظف"].ToString().Trim();
                            DateTime orderDate = DateTime.Parse(reader["تاريخ الطلب"].ToString());
                            string fileName = $"{selectedOrderNumber}_{employeeName}_{orderDate:ddMMyyyy}.pdf";

                            DownloadFile(outputPdfPath, SanitizeFilename(fileName));

                            // إظهار رسالة النجاح
                            LabelMessage.Text = "تم إنشاء الملف بنجاح وجاهز للتنزيل";
                            LabelMessage.Visible = true;
                            LabelError.Visible = false;
                        }
                        else
                        {
                            ShowError("رقم الطلب غير موجود في النظام");
                        }
                    }
                }
            }
        }

        // دالة مساعدة لاستخراج القيم
        private string GetBookmarkValue(string bookmarkName, SqlDataReader reader)
        {
            switch (bookmarkName)
            {
                case "bm1": return reader["اسم الموظف"].ToString();
                case "bm31": return reader["الوظيفة"].ToString();
                case "bm3": return reader["رقم الموظف"].ToString();
                case "bm4": return reader["السجل المدني"].ToString();
                case "bm6": return reader["الجنسية"].ToString();
                case "bm2": return reader["رقم الجوال"].ToString();
                case "bm5": return reader["نوع التوظيف"].ToString();
                case "bm34": return reader["المؤهل"].ToString();
                case "bm7": return reader["القسم"].ToString();
                case "bm8": return reader["تفاصيل مقدم الطلب"].ToString();
                case "bm9": return FormatSupervisorText(reader["تم التأكيد/الإلغاء من قبل المشرف"].ToString(), false);
                case "bm10": return reader["تفاصيل المنسق"].ToString();
                case "bm11": return FormatSupervisorText(reader["تم التأكيد/الإلغاء من مدير القسم"].ToString(), false);
                case "bm12": return FormatSupervisorWithDate(reader["مشرف خدمات الموظفين"].ToString());
                case "bm13": return FormatSupervisorWithDate(reader["مشرف إدارة القانونية والالتزام"].ToString());
                case "bm14": return FormatSupervisorWithDate(reader["مشرف العيادات الخارجية"].ToString());
                case "bm15": return FormatSupervisorWithDate(reader["مشرف إدارة الأمن و السلامة"].ToString());
                case "bm16": return FormatSupervisorWithDate(reader["مشرف إدارة تخطيط الموارد البشرية"].ToString());
                case "bm17": return FormatSupervisorWithDate(reader["مشرف إدارة الرواتب والاستحقاقات"].ToString());
                case "bm18": return FormatSupervisorWithDate(reader["مشرف قسم الملفات"].ToString());
                case "bm19": return FormatSupervisorWithDate(reader["مشرف إدارة تنمية الإيرادات"].ToString());
                case "bm20": return FormatSupervisorWithDate(reader["مشرف إدارة تقنية المعلومات"].ToString());
                case "bm21": return FormatSupervisorWithDate(reader["مشرف السجلات الطبية"].ToString());
                case "bm22": return FormatSupervisorWithDate(reader["مشرف إدارة الإسكان"].ToString());
                case "bm23": return FormatSupervisorWithDate(reader["مشرف وحدة مراقبة المخزون"].ToString());
                case "bm24": return FormatSupervisorWithDate(reader["مشرف مراقبة الدوام"].ToString());
                case "bm25": return FormatSupervisorWithDate(reader["مشرف خدمات الموارد البشرية"].ToString());
                case "bm26": return FormatSupervisorWithDate(reader["مشرف التأمينات الاجتماعية"].ToString());
                case "bm27": return FormatSupervisorWithDate(reader["مشرف الطب الاتصالي"].ToString());
                case "bm28": return FormatSupervisorWithDate(reader["مدير الموارد البشرية"].ToString());
                case "bm29": return reader["تم التحويل/الإلغاء من قبل المنسق"].ToString();
                case "bm30": return DateTime.Now.ToString("yyyy/MM/dd");
                case "bm32": return reader["نوع الطلب"].ToString();
                case "bm33": return reader["رقم الطلب"].ToString();
                default: return string.Empty;
            }
        }

        // دالة مساعدة لتنظيم نص المشرف مع التاريخ
        private string FormatSupervisorWithDate(string supervisorText)
        {
            string formattedText = FormatSupervisorText(supervisorText, true);
            string date = ExtractDateFromText(supervisorText);

            return !formattedText.Contains(date)
                ? $"{formattedText}\n{date}"
                : formattedText;
        }

        // دالة لتنظيف النص
        private string SanitizeText(string text)
        {
            return text.Replace("|", "")
                      .Replace("\r", "")
                      .Trim();
        }

        // دالة SanitizeFilename تقوم بتنظيف اسم الملف من خلال استبدال جميع الأحرف غير الصالحة بأحرف "_".
        // يتم استخدام قائمة الأحرف غير الصالحة التي لا يمكن استخدامها في أسماء الملفات.
        // الهدف من هذه الدالة هو التأكد من أن اسم الملف آمن للاستخدام على نظام الملفات.

        private string SanitizeFilename(string filename)
        {
            foreach (char c in System.IO.Path.GetInvalidFileNameChars())
            {
                filename = filename.Replace(c, '_');
            }
            return filename;
        }


        // دالة ConvertDocxToPdf تقوم بتحويل ملف DOCX إلى ملف PDF باستخدام مكتبة Spire.Doc.
        // يتم تحميل ملف الـ DOCX من المسار المحدد docxPath.
        // بعد ذلك، يتم حفظ الملف بتنسيق PDF في المسار المحدد pdfPath.


        private void ConvertDocxToPdf(string docxPath, string pdfPath)
        {
            // Load the DOCX file using Spire.Doc
            Spire.Doc.Document doc = new Spire.Doc.Document();
            doc.LoadFromFile(docxPath);

            // Save the file as PDF
            doc.SaveToFile(pdfPath, Spire.Doc.FileFormat.PDF);


        }

        // دالة DownloadFile تقوم بتنزيل ملف PDF من المسار المحدد filePath باسم الملف المحدد fileName.
        // يتم التحقق من وجود الملف أولاً.
        // إذا كان الملف موجودًا، يتم إعداد الاستجابة (Response) لعرض حوار التحميل مع تهيئة رأس المحتوى (Content-Disposition) 
        // وحجم الملف (Content-Length) ونوع المحتوى (ContentType) كملف PDF.
        // أخيرًا، يتم إرسال الملف إلى العميل باستخدام Response.WriteFile ثم إنهاء الاستجابة باستخدام Response.End.

        private void DownloadFile(string filePath, string fileName)
        {
            FileInfo file = new FileInfo(filePath);

            if (file.Exists)
            {
                Response.Clear();
                Response.AddHeader("Content-Disposition", "attachment; filename=" + fileName);
                Response.AddHeader("Content-Length", file.Length.ToString());
                Response.ContentType = "application/pdf";
                Response.WriteFile(file.FullName);
                Response.End();
            }
        }

        // دالة GetText تقوم باستخراج النص من عنصر Paragraph الخاص بـ OpenXML.
        // تقوم بتجميع جميع العناصر الفرعية من نوع Text داخل الفقرة (Paragraph) وتجمع النصوص معًا باستخدام المسافات كفاصل.
        // يتم استخدام LINQ لتحديد النصوص من جميع عناصر Text داخل الفقرة ثم ربطها معًا في سلسلة نصية واحدة.

        private string GetText(DocumentFormat.OpenXml.Wordprocessing.Paragraph paragraph)
        {
            return string.Join(" ", paragraph.Elements<Text>().Select(t => t.Text));
        }

        // تقوم هذه الدالة بتنسيق نص المشرف من خلال إزالة جملة "اعتماد بواسطة" واستخراج الاسم والتاريخ.
        // إذا كان النص يحتوي على جملة "اعتماد بواسطة"، يتم إزالتها.
        // تقوم الدالة باستخراج التاريخ من النص باستخدام دالة ExtractDateFromText.
        // يتم تقسيم النص إلى جزئين: جزء للاسم وجزء للتاريخ.
        // إذا تم تحديد المعلمة 'returnSupervisorOnly' بقيمة true، يتم إرجاع اسم المشرف فقط دون التاريخ.
        // إذا لم يتم تحديد 'returnSupervisorOnly'، يتم إرجاع الاسم متبوعًا بالتاريخ، مع فصل بينهما بسطر جديد.


        private string FormatSupervisorText(string input, bool returnSupervisorOnly = false)
        {
            if (string.IsNullOrEmpty(input) || input == "/")
                return "/";

            var regex = new System.Text.RegularExpressions.Regex(
                @"(\d{4}-\d{2}-\d{2})\s*\|\s*اعتماد بواسطة\s*(.*?)\s*(\d{4}-\d{2}-\d{2})"
            );

            var match = regex.Match(input);

            if (match.Success)
            {
                string submissionDate = match.Groups[1].Value.Trim();
                string name = match.Groups[2].Value.Trim();
                string approvalDate = match.Groups[3].Value.Trim();

                if (returnSupervisorOnly)
                {
                    return name;
                }
                else
                {
                    return $"{name}\n{approvalDate}";
                }
            }

            return input;
        }


        private string ExtractDateFromText(string text)
        {
            try
            {
                // تعبير نمطي لاستخراج أول تاريخ من النص
                var regex = new System.Text.RegularExpressions.Regex(@"\d{4}-\d{2}-\d{2}");
                var match = regex.Match(text);

                // إذا تم العثور على تاريخ، يتم إرجاعه
                return match.Success ? match.Value : string.Empty;
            }
            catch
            {
                // في حالة وجود خطأ، يتم إرجاع سلسلة فارغة
                return string.Empty;
            }
        }

        private void InsertTextAtBookmark(DocumentFormat.OpenXml.Wordprocessing.BookmarkStart bookmark, string text, bool useSmallFont)
        {
            // تقسيم النص إلى الاسم والتاريخ
            string[] parts = text.Split('\n');

            if (parts.Length == 2)
            {
                string name = parts[0].Trim();
                string date = parts[1].Trim();

                // إنشاء فقرة للاسم
                var nameRun = new DocumentFormat.OpenXml.Wordprocessing.Run(
                    new DocumentFormat.OpenXml.Wordprocessing.Text(name));
                var nameParagraph = new DocumentFormat.OpenXml.Wordprocessing.Paragraph(nameRun);
                bookmark.Parent.InsertAfter(nameParagraph, bookmark);

                // إنشاء فقرة للتاريخ بحجم خط أصغر
                if (!string.IsNullOrEmpty(date))
                {
                    var dateRun = new DocumentFormat.OpenXml.Wordprocessing.Run(
                        new DocumentFormat.OpenXml.Wordprocessing.Text(date));

                    var dateProperties = new DocumentFormat.OpenXml.Wordprocessing.RunProperties
                    {
                        FontSize = new DocumentFormat.OpenXml.Wordprocessing.FontSize { Val = "18" }
                    };

                    dateRun.RunProperties = dateProperties;
                    var dateParagraph = new DocumentFormat.OpenXml.Wordprocessing.Paragraph(dateRun);
                    bookmark.Parent.InsertAfter(dateParagraph, nameParagraph);
                }
            }
            else
            {
                // إذا لم يكن هناك تقسيم، يتم إدخال النص كاملاً
                var run = new DocumentFormat.OpenXml.Wordprocessing.Run(
                    new DocumentFormat.OpenXml.Wordprocessing.Text(text));
                bookmark.Parent.InsertAfter(run, bookmark);
            }
        }
        private readonly string connectionString = ConfigurationManager.ConnectionStrings["MyConnectionString"].ConnectionString;
        private void PopulatePrintOrders(string searchTerm = "", string filter = "today")
        {
            try
            {
                using (SqlConnection con = new SqlConnection(connectionString))
                {
                    string query = @"
            SELECT 
                o.[رقم الطلب], 
                o.[اسم الموظف],
                o.[تاريخ الطلب],
                o.[مدير الموارد البشرية],
                CASE 
                    WHEN o.[اسم الموظف] IS NULL OR o.[اسم الموظف] = '' 
                    THEN CAST(o.[رقم الطلب] AS NVARCHAR(50))
                    ELSE CAST(o.[رقم الطلب] AS NVARCHAR(50)) + ' | ' + o.[اسم الموظف]
                END AS DisplayText
            FROM ordersTable o
            WHERE [حالة الطلب] = 'مقبول'
            AND [مدير الموارد البشرية] IS NOT NULL
            AND (
                @SearchTerm = '' 
                OR CAST([رقم الطلب] AS NVARCHAR) LIKE @SearchTerm + '%'
                OR [اسم الموظف] LIKE N'%' + @SearchTerm + '%'
            )
            AND (
                @Filter = 'all'
                OR (@Filter = 'today' AND CONVERT(date, [تاريخ الطلب]) = CONVERT(date, GETDATE()))
                OR (@Filter = 'week' AND DATEDIFF(day, [تاريخ الطلب], GETDATE()) <= 7)
                OR (@Filter = 'month' AND DATEDIFF(month, [تاريخ الطلب], GETDATE()) = 0)
            )
            ORDER BY [تاريخ الطلب] DESC, [رقم الطلب] DESC";

                    using (SqlCommand cmd = new SqlCommand(query, con))
                    {
                        cmd.Parameters.AddWithValue("@SearchTerm", searchTerm ?? "");
                        cmd.Parameters.AddWithValue("@Filter", filter);

                        con.Open();
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            ddlPrintOrders.Items.Clear();
                            ddlPrintOrders.DataSource = reader;
                            ddlPrintOrders.DataTextField = "DisplayText";
                            ddlPrintOrders.DataValueField = "رقم الطلب";
                            ddlPrintOrders.DataBind();
                        }
                    }

                    // إضافة العنصر الافتراضي مع الإحصائيات
                    int resultCount = ddlPrintOrders.Items.Count;
                    string filterText = GetFilterText(filter);

                    string defaultText = resultCount > 0
                        ? $"-- اختر من {resultCount} طلب ({filterText}) --"
                        : "-- لا توجد طلبات --";

                    ddlPrintOrders.Items.Insert(0, new ListItem(defaultText, ""));
                }
            }
            catch (Exception ex)
            {
                ShowError("حدث خطأ أثناء تحميل الطلبات: " + ex.Message);
            }
        }

        private string GetFilterText(string filter)
        {
            switch (filter)
            {
                case "today": return "اليوم";
                case "week": return "أسبوع";
                case "month": return "شهر";
                default: return "الكل";
            }
        }
        private void ShowError(string message)
        {
            LabelError.Text = message;
            LabelError.Visible = true;
            LabelMessage.Visible = false;
        }
        protected void txtPrintSearch_TextChanged(object sender, EventArgs e)
        {
            PopulatePrintOrders(txtPrintSearch.Text.Trim(), ViewState["PrintFilter"]?.ToString() ?? "today");
        }

        protected void btnPrintFilter_Click(object sender, EventArgs e)
        {
            LinkButton btn = (LinkButton)sender;
            string filter = btn.CommandArgument;

            // تحديث مظهر الأزرار
            btnPrintToday.CssClass = UpdateButtonStyle(btnPrintToday, filter == "today");
            btnPrintWeek.CssClass = UpdateButtonStyle(btnPrintWeek, filter == "week");
            btnPrintMonth.CssClass = UpdateButtonStyle(btnPrintMonth, filter == "month");
            btnPrintAll.CssClass = UpdateButtonStyle(btnPrintAll, filter == "all");

            ViewState["PrintFilter"] = filter;
            PopulatePrintOrders(txtPrintSearch.Text.Trim(), filter);
        }

        private string UpdateButtonStyle(LinkButton btn, bool isActive)
        {
            return $"btn {(isActive ? "btn-success" : "btn-outline-success")} d-flex align-items-center justify-content-center flex-grow-1 rounded-3";
        }

        protected void ddlPrintOrders_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (ddlPrintOrders.SelectedValue != "")
            {
                LoadOrderDetails(ddlPrintOrders.SelectedValue);
            }
        }













    }
}