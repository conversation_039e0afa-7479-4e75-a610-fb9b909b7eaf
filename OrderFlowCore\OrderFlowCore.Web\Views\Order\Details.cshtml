@model OrderFlowCore.Web.ViewModels.OrderDetailsViewModel
@using OrderFlowCore.Core.Entities
@using System.Text.Json

@{
    ViewData["Title"] = "تفاصيل الطلب";
    Layout = "_Layout";

    var statusMap = Enum.GetValues(typeof(OrderStatus))
        .Cast<OrderStatus>()
        .ToDictionary(s => s.ToString(), s => s.ToString());
}

<!-- Hero Section -->
<section class="hero-details">
    <div class="container text-center text-white position-relative">
        <h1 class="display-3 fw-bold mb-4 animate-fade-in">تفاصيل الطلب #@Model.Id</h1>
        <p class="lead fs-4 mb-0 animate-fade-in" style="animation-delay: 0.2s;">متابعة حالة طلبك ومراحل المعالجة</p>
        
        <!-- Floating shapes -->
        <div class="floating-shape" style="top: 20%; right: 10%; width: 100px; height: 100px; background: radial-gradient(circle, rgba(255,255,255,0.2) 0%, transparent 70%); border-radius: 50%;"></div>
        <div class="floating-shape" style="bottom: 30%; left: 5%; width: 80px; height: 80px; background: radial-gradient(circle, rgba(255,255,255,0.15) 0%, transparent 70%); border-radius: 50%; animation-delay: -3s;"></div>
    </div>
</section>

<!-- Progress Section -->
<section class="py-5" style="margin-top: -100px;">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="progress-card">
                    <h3 class="text-center mb-4">مراحل معالجة الطلب</h3>
                    
                    <!-- Progress Steps -->
                    <div class="progress-steps">
                        <div class="progress-step completed">
                            <div class="step-circle">
                                <i class="fas fa-check"></i>
                            </div>
                            <div class="step-label">تم تقديم الطلب</div>
                        </div>
                        <div class="progress-step">
                            <div class="step-circle">DM</div>
                            <div class="step-label">مدير القسم</div>
                        </div>
                        <div class="progress-step">
                            <div class="step-circle">A</div>
                            <div class="step-label">مساعد المدير</div>
                        </div>
                        <div class="progress-step">
                            <div class="step-circle">B</div>
                            <div class="step-label">منسق الموارد البشرية</div>
                        </div>
                        <div class="progress-step">
                            <div class="step-circle">C</div>
                            <div class="step-label">المشرفون</div>
                        </div>
                        <div class="progress-step">
                            <div class="step-circle">D</div>
                            <div class="step-label">مدير الموارد البشرية</div>
                        </div>
                        <div class="progress-step">
                            <div class="step-circle">
                                <i class="fas fa-flag-checkered"></i>
                            </div>
                            <div class="step-label">مكتمل</div>
                        </div>
                    </div>

                    <!-- Progress Bar -->
                    <div class="progress-bar-modern">
                        <div class="progress-fill" id="progressBar" style="width: 0%"></div>
                    </div>
                    
                    <div class="text-center">
                        <p class="mb-0 text-muted" id="progressText">0% مكتمل</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Order Details Section -->
<section class="pt-5">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <!-- Basic Order Info -->
                <div class="details-section">
                    <div class="section-header">
                        <i class="fas fa-info-circle"></i>
                        <h5 class="mb-0">معلومات الطلب الأساسية</h5>
                    </div>
                    <table class="details-table">
                        <tr>
                            <th width="25%">رقم الطلب</th>
                            <td width="25%">@Model.Id</td>
                            <th width="25%">تاريخ الطلب</th>
                            <td width="25%">@Model.CreatedAt.ToString("yyyy-MM-dd")</td>
                        </tr>
                        <tr>
                            <th>حالة الطلب</th>
                            <td colspan="3">
                                <span class="status-badge bg-primary">@Model.OrderStatus.ToDisplayString()</span>
                            </td>
                        </tr>
                        <tr>
                            <th>نوع الطلب</th>
                            <td colspan="3">@Model.OrderType</td>
                        </tr>
                    </table>
                </div>

                <!-- Employee Details -->
                <div class="details-section">
                    <div class="section-header">
                        <i class="fas fa-user"></i>
                        <h5 class="mb-0">بيانات الموظف</h5>
                    </div>
                    <table class="details-table">
                        <tr>
                            <th>اسم الموظف</th>
                            <td>@Model.EmployeeName</td>
                            <th>رقم الموظف</th>
                            <td>@Model.EmployeeNumber</td>
                        </tr>
                        <tr>
                            <th>القسم</th>
                            <td>@Model.Department</td>
                            <th>الوظيفة</th>
                            <td>@Model.JobTitle</td>
                        </tr>
                        <tr>
                            <th>نوع التوظيف</th>
                            <td>@Model.EmploymentType</td>
                            <th>المؤهل</th>
                            <td>@Model.Qualification</td>
                        </tr>
                        <tr>
                            <th>الجنسية</th>
                            <td>@Model.Nationality</td>
                            <th>السجل المدني</th>
                            <td>@Model.CivilRecord</td>
                        </tr>
                        <tr>
                            <th>رقم الجوال</th>
                            <td colspan="3">@Model.MobileNumber</td>
                        </tr>
                        @if (!string.IsNullOrEmpty(Model.Details))
                        {
                            <tr>
                                <td colspan="4" style="background: #f8fafc; padding: 1.5rem;">
                                    <strong>تفاصيل مقدم الطلب:</strong><br />
                                    <div style="margin-top: 0.5rem; color: var(--text-light);">
                                        @Model.Details
                                    </div>
                                </td>
                            </tr>
                        }
                    </table>
                </div>

                <!-- Approval Status -->
                <div class="details-section">
                    <div class="section-header">
                        <i class="fas fa-check-circle"></i>
                        <h5 class="mb-0">حالة الموافقات</h5>
                    </div>
                    <table class="details-table">
                        <tr>
                            <th>مدير القسم</th>
                            <td>@(Model.ConfirmedByDepartmentManager ?? "قيد الانتظار")</td>
                        </tr>
                        <tr>
                            <th>مساعد المدير</th>
                            <td>@(Model.ConfirmedByAssistantManager ?? "قيد الانتظار")</td>
                        </tr>
                        <tr>
                            <th>المنسق</th>
                            <td>@(Model.ConfirmedByCoordinator ?? "قيد الانتظار")</td>
                        </tr>
                        @if (!string.IsNullOrEmpty(Model.ReasonForCancellation))
                        {
                            <tr>
                                <th>سبب الإلغاء/الإعادة</th>
                                <td colspan="3" class="text-danger">@Model.ReasonForCancellation</td>
                            </tr>
                        }
                        @if (!string.IsNullOrEmpty(Model.CoordinatorDetails))
                        {
                            <tr>
                                <th>تفاصيل المنسق</th>
                                <td colspan="3">@Model.CoordinatorDetails</td>
                            </tr>
                        }
                    </table>
                </div>

                <!-- Supervisors Status -->
                <div class="details-section">
                    <div class="section-header">
                        <i class="fas fa-users"></i>
                        <h5 class="mb-0">حالة المشرفين</h5>
                    </div>
                    <table class="details-table">
                        <tr>
                            <th>خدمات الموظفين</th>
                            <td>@(Model.SupervisorOfEmployeeServices ?? "-")</td>
                            <th>تخطيط الموارد البشرية</th>
                            <td>@(Model.SupervisorOfHumanResourcesPlanning ?? "-")</td>
                        </tr>
                        <tr>
                            <th>تقنية المعلومات</th>
                            <td>@(Model.SupervisorOfInformationTechnology ?? "-")</td>
                            <th>مراقبة الدوام</th>
                            <td>@(Model.SupervisorOfAttendance ?? "-")</td>
                        </tr>
                        <tr>
                            <th>السجلات الطبية</th>
                            <td>@(Model.SupervisorOfMedicalRecords ?? "-")</td>
                            <th>الرواتب والاستحقاقات</th>
                            <td>@(Model.SupervisorOfPayrollAndBenefits ?? "-")</td>
                        </tr>
                        <tr>
                            <th>القانونية والالتزام</th>
                            <td>@(Model.SupervisorOfLegalAndCompliance ?? "-")</td>
                            <th>خدمات الموارد البشرية</th>
                            <td>@(Model.SupervisorOfHumanResourcesServices ?? "-")</td>
                        </tr>
                        <tr>
                            <th>الإسكان</th>
                            <td>@(Model.SupervisorOfHousing ?? "-")</td>
                            <th>قسم الملفات</th>
                            <td>@(Model.SupervisorOfFiles ?? "-")</td>
                        </tr>
                        <tr>
                            <th>العيادات الخارجية</th>
                            <td>@(Model.SupervisorOfOutpatientClinics ?? "-")</td>
                            <th>التأمينات الاجتماعية</th>
                            <td>@(Model.SupervisorOfSocialSecurity ?? "-")</td>
                        </tr>
                        <tr>
                            <th>مراقبة المخزون</th>
                            <td>@(Model.SupervisorOfInventoryControl ?? "-")</td>
                            <th>تنمية الإيرادات</th>
                            <td>@(Model.SupervisorOfRevenueDevelopment ?? "-")</td>
                        </tr>
                        <tr>
                            <th>الأمن والسلامة</th>
                            <td>@(Model.SupervisorOfSecurity ?? "-")</td>
                            <th>الطب الاتصالي</th>
                            <td>@(Model.SupervisorOfMedicalConsultation ?? "-")</td>
                        </tr>
                    </table>
                </div>

                <!-- HR Manager -->
                <div class="details-section">
                    <div class="section-header">
                        <i class="fas fa-user-tie"></i>
                        <h5 class="mb-0">مدير الموارد البشرية</h5>
                    </div>
                    <table class="details-table">
                        <tr>
                            <th width="30%">الحالة</th>
                            <td>@(Model.HumanResourcesManager ?? "قيد الانتظار")</td>
                        </tr>
                    </table>
                </div>

                <!-- File Upload Section -->
                <div class="file-upload-section slide-in">
                    <div class="card-modern">
                        <div class="card-header">
                            <h3><i class="fas fa-paperclip me-2"></i>المرفقات | Attachments</h3>
                        </div>
                        <div class="card-body">
                            <!-- Upload Instructions -->
                            <div class="alert alert-modern alert-info mb-4">
                                <h6 class="fw-bold mb-2">
                                    <i class="fas fa-info-circle me-2"></i>
                                    تعليمات رفع الملفات:
                                </h6>
                                <ul class="mb-0 ps-3">
                                    <li>يجب أن تكون جميع الملفات بصيغة PDF فقط</li>
                                    <li>الحد الأقصى لحجم كل ملف: 5 ميجابايت</li>
                                    <li>يمكنك رفع حتى 4 ملفات</li>
                                </ul>
                            </div>

                            <!-- File Upload Structure -->
                            <div class="col-12">
                                <label class="form-label-modern-primary">المرفقات</label>
                                <div class="row g-3">
                                    <!-- File 1 -->
                                    <div class="col-12">
                                        <label class="form-label-modern-primary small">المرفق الأول</label>
                                        @if (!string.IsNullOrEmpty(Model.File1Url))
                                        {
                                            <div class="mb-2">
                                                <a href="@Model.File1Url" class="file-link" target="_blank">
                                                    <i class="fas fa-file-pdf file-icon"></i>
                                                    مرفق 1
                                                </a>
                                                <span class="bg-success status-badge text-white ms-2">تم الرفع</span>
                                            </div>
                                        }
                                        <input type="file" class="form-control form-control-modern" id="FileUpload1" name="FileUpload1" accept=".pdf" />
                                        <div class="form-text small">ملف PDF</div>
                                        <div class="mt-2">
                                            <button type="button" class="btn-modern btn-primary-modern btn-sm" onclick="uploadFile(1)">
                                                <i class="fas fa-upload"></i> 
                                                @(string.IsNullOrEmpty(Model.File1Url) ? "رفع" : "تحديث")
                                            </button>
                                        </div>
                                        <div id="lblFile1Status" class="mt-1"></div>
                                    </div>

                                    <!-- File 2 -->
                                    <div class="col-12">
                                        <label class="form-label-modern-primary small">المرفق الثاني</label>
                                        @if (!string.IsNullOrEmpty(Model.File2Url))
                                        {
                                            <div class="mb-2">
                                                <a href="@Model.File2Url" class="file-link" target="_blank">
                                                    <i class="fas fa-file-pdf file-icon"></i>
                                                    مرفق 2
                                                </a>
                                                <span class="bg-success status-badge text-white ms-2">تم الرفع</span>
                                            </div>
                                        }
                                        <input type="file" class="form-control form-control-modern" id="FileUpload2" name="FileUpload2" accept=".pdf" />
                                        <div class="form-text small">ملف PDF</div>
                                        <div class="mt-2">
                                            <button type="button" class="btn-modern btn-primary-modern btn-sm" onclick="uploadFile(2)">
                                                <i class="fas fa-upload"></i> 
                                                @(string.IsNullOrEmpty(Model.File2Url) ? "رفع" : "تحديث")
                                            </button>
                                        </div>
                                        <div id="lblFile2Status" class="mt-1"></div>
                                    </div>

                                    <!-- File 3 -->
                                    <div class="col-12">
                                        <label class="form-label-modern-primary small">المرفق الثالث</label>
                                        @if (!string.IsNullOrEmpty(Model.File3Url))
                                        {
                                            <div class="mb-2">
                                                <a href="@Model.File3Url" class="file-link" target="_blank">
                                                    <i class="fas fa-file-pdf file-icon"></i>
                                                    مرفق 3
                                                </a>
                                                <span class="bg-success status-badge text-white ms-2">تم الرفع</span>
                                            </div>
                                        }
                                        <input type="file" class="form-control form-control-modern" id="FileUpload3" name="FileUpload3" accept=".pdf" />
                                        <div class="form-text small">ملف PDF</div>
                                        <div class="mt-2">
                                            <button type="button" class="btn-modern btn-primary-modern btn-sm" onclick="uploadFile(3)">
                                                <i class="fas fa-upload"></i> 
                                                @(string.IsNullOrEmpty(Model.File3Url) ? "رفع" : "تحديث")
                                            </button>
                                        </div>
                                        <div id="lblFile3Status" class="mt-1"></div>
                                    </div>

                                    <!-- File 4 -->
                                    <div class="col-12">
                                        <label class="form-label-modern-primary small">المرفق الرابع</label>
                                        @if (!string.IsNullOrEmpty(Model.File4Url))
                                        {
                                            <div class="mb-2">
                                                <a href="@Model.File4Url" class="file-link" target="_blank">
                                                    <i class="fas fa-file-pdf file-icon"></i>
                                                    مرفق 4
                                                </a>
                                                <span class="bg-success status-badge text-white ms-2">تم الرفع</span>
                                            </div>
                                        }
                                        <input type="file" class="form-control form-control-modern" id="FileUpload4" name="FileUpload4" accept=".pdf" />
                                        <div class="form-text small">ملف PDF</div>
                                        <div class="mt-2">
                                            <button type="button" class="btn-modern btn-primary-modern btn-sm" onclick="uploadFile(4)">
                                                <i class="fas fa-upload"></i> 
                                                @(string.IsNullOrEmpty(Model.File4Url) ? "رفع" : "تحديث")
                                            </button>
                                        </div>
                                        <div id="lblFile4Status" class="mt-1"></div>
                                    </div>
                                </div>
                                <div class="form-text mt-2">يمكن رفع ملفات PDF فقط، الحد الأقصى 4 ملفات، حجم كل ملف 5 ميجابايت</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Action Buttons Section -->
<section class="py-5 bg-light">
    <div class="container text-center">
        <a asp-action="New" class="btn btn-modern btn-primary-modern">
            <i class="fas fa-plus-circle me-2"></i>طلب جديد
        </a>
        <a href="@Url.Action("Index", "Home")" class="btn btn-modern btn-white-modern">
            <i class="fas fa-home me-2"></i>العودة للصفحة الرئيسية
        </a>
    </div>
</section>


@section Scripts {
    <script src="~/js/orderdetals.js"></script>

    <script>
    const OrderStatus = @Html.Raw(JsonSerializer.Serialize(statusMap))

    // Initialize progress tracker when DOM is ready
    document.addEventListener('DOMContentLoaded', function() {
        try {
            // Create global instance
            window.orderProgressTracker = new OrderProgressTracker();
        
            // Get initial status from server-side model
            const initialStatus = '@Model.OrderStatus';
        
            // Initialize with the current status
            window.orderProgressTracker.init(initialStatus);
        
        } catch (error) {
            console.error('Failed to initialize OrderProgressTracker:', error);
        }
    });

</script>
}


