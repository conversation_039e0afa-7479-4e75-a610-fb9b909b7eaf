using OrderFlowCore.Core.Entities;
using System.ComponentModel.DataAnnotations;

namespace OrderFlowCore.Application.DTOs;

public class DepartmentDto
{
    public int Id { get; set; }

    [Required(ErrorMessage = "اسم القسم مطلوب")]
    [StringLength(100, ErrorMessage = "اسم القسم يجب أن يكون أقل من 100 حرف")]
    public string Name { get; set; } = string.Empty;

    [StringLength(500, ErrorMessage = "الوصف يجب أن يكون أقل من 500 حرف")]
    public string? Description { get; set; }

    public bool IsActive { get; set; } = true;

    public DateTime CreatedAt { get; set; } = DateTime.Now;

    [StringLength(10, ErrorMessage = "معرف مساعد المدير يجب أن يكون أقل من 10 أحرف")]
    public AssistantManagerType AssistantManagerId { get; set; } = AssistantManagerType.Unknown;
}