@model OrderFlowCore.Application.DTOs.ChangePasswordDto
@{
    ViewData["Title"] = "تغيير كلمة المرور";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <div>
                    <h1 class="page-title">تغيير كلمة المرور</h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="@Url.Action("Index", "Dashboard")">لوحة التحكم</a></li>
                            <li class="breadcrumb-item"><a href="@Url.Action("Profile", "User")">الملف الشخصي</a></li>
                            <li class="breadcrumb-item active" aria-current="page">تغيير كلمة المرور</li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </div>

    <div class="row justify-content-center">
        <div class="col-lg-6 col-md-8">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-gradient-warning text-dark">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-shield-alt fa-2x me-3"></i>
                        <div>
                            <h4 class="mb-0">تغيير كلمة المرور</h4>
                            <small>قم بتحديث كلمة المرور الخاصة بك</small>
                        </div>
                    </div>
                </div>
                <div class="card-body p-4">
                    @if (TempData["ToastrSuccess"] != null)
                    {
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i>
                            @TempData["ToastrSuccess"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    }

                    @if (TempData["ToastrError"] != null)
                    {
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            @TempData["ToastrError"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    }

                    <form asp-action="ChangePassword" method="post" id="changePasswordForm">
                        <div asp-validation-summary="ModelOnly" class="alert alert-danger"></div>
                        
                        <div class="mb-4">
                            <label asp-for="CurrentPassword" class="form-label fw-bold">
                                <i class="fas fa-lock me-2"></i>كلمة المرور الحالية
                            </label>
                            <div class="input-group">
                                <input asp-for="CurrentPassword" type="password" class="form-control form-control-lg" 
                                       placeholder="أدخل كلمة المرور الحالية" />
                                <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('CurrentPassword')">
                                    <i class="fas fa-eye" id="CurrentPasswordIcon"></i>
                                </button>
                            </div>
                            <span asp-validation-for="CurrentPassword" class="text-danger"></span>
                        </div>

                        <div class="mb-4">
                            <label asp-for="NewPassword" class="form-label fw-bold">
                                <i class="fas fa-key me-2"></i>كلمة المرور الجديدة
                            </label>
                            <div class="input-group">
                                <input asp-for="NewPassword" type="password" class="form-control form-control-lg" 
                                       placeholder="أدخل كلمة المرور الجديدة" 
                                       onkeyup="checkPasswordStrength(this.value)" />
                                <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('NewPassword')">
                                    <i class="fas fa-eye" id="NewPasswordIcon"></i>
                                </button>
                            </div>
                            <div class="password-strength mt-2">
                                <div class="progress" style="height: 5px;">
                                    <div class="progress-bar" id="passwordStrengthBar" role="progressbar"></div>
                                </div>
                                <small class="text-muted" id="passwordStrengthText">قوة كلمة المرور</small>
                            </div>
                            <span asp-validation-for="NewPassword" class="text-danger"></span>
                        </div>

                        <div class="mb-4">
                            <label asp-for="ConfirmPassword" class="form-label fw-bold">
                                <i class="fas fa-check-circle me-2"></i>تأكيد كلمة المرور الجديدة
                            </label>
                            <div class="input-group">
                                <input asp-for="ConfirmPassword" type="password" class="form-control form-control-lg" 
                                       placeholder="أعد إدخال كلمة المرور الجديدة" 
                                       onkeyup="checkPasswordMatch()" />
                                <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('ConfirmPassword')">
                                    <i class="fas fa-eye" id="ConfirmPasswordIcon"></i>
                                </button>
                            </div>
                            <div class="mt-2">
                                <small class="text-muted" id="passwordMatchText">تأكيد كلمة المرور</small>
                            </div>
                            <span asp-validation-for="ConfirmPassword" class="text-danger"></span>
                        </div>

                        <div class="alert alert-info">
                            <h6 class="alert-heading"><i class="fas fa-info-circle me-2"></i>متطلبات كلمة المرور:</h6>
                            <ul class="mb-0">
                                <li>يجب أن تكون 8 أحرف على الأقل</li>
                                <li>يُنصح بتضمين أحرف كبيرة وصغيرة</li>
                                <li>يُنصح بتضمين أرقام ورموز خاصة</li>
                                <li>يجب أن تكون مختلفة عن كلمة المرور الحالية</li>
                            </ul>
                        </div>

                        <hr class="my-4">

                        <div class="d-flex justify-content-between align-items-center">
                            <a asp-action="Profile" class="btn btn-outline-secondary btn-lg">
                                <i class="fas fa-arrow-right me-2"></i>العودة للملف الشخصي
                            </a>
                            <div>
                                <button type="button" class="btn btn-outline-warning btn-lg me-2" onclick="resetForm()">
                                    <i class="fas fa-undo me-2"></i>إعادة تعيين
                                </button>
                                <button type="submit" class="btn btn-warning btn-lg" id="submitBtn">
                                    <i class="fas fa-save me-2"></i>تغيير كلمة المرور
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>



@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    
    <script>
        // إظهار/إخفاء كلمة المرور
        function togglePassword(fieldId) {
            const field = document.getElementById(fieldId);
            const icon = document.getElementById(fieldId + 'Icon');
            
            if (field.type === 'password') {
                field.type = 'text';
                icon.className = 'fas fa-eye-slash';
            } else {
                field.type = 'password';
                icon.className = 'fas fa-eye';
            }
        }

        // فحص قوة كلمة المرور
        function checkPasswordStrength(password) {
            const strengthBar = document.getElementById('passwordStrengthBar');
            const strengthText = document.getElementById('passwordStrengthText');
            
            let strength = 0;
            let feedback = '';
            
            if (password.length >= 8) strength += 25;
            if (/[a-z]/.test(password)) strength += 25;
            if (/[A-Z]/.test(password)) strength += 25;
            if (/[0-9!@@#$%^&*]/.test(password)) strength += 25;
            
            strengthBar.style.width = strength + '%';
            
            if (strength <= 25) {
                strengthBar.className = 'progress-bar bg-danger';
                feedback = 'ضعيفة جداً';
            } else if (strength <= 50) {
                strengthBar.className = 'progress-bar bg-warning';
                feedback = 'ضعيفة';
            } else if (strength <= 75) {
                strengthBar.className = 'progress-bar bg-info';
                feedback = 'جيدة';
            } else {
                strengthBar.className = 'progress-bar bg-success';
                feedback = 'قوية';
            }
            
            strengthText.textContent = `قوة كلمة المرور: ${feedback}`;
        }

        // فحص تطابق كلمة المرور
        function checkPasswordMatch() {
            const newPassword = document.getElementById('NewPassword').value;
            const confirmPassword = document.getElementById('ConfirmPassword').value;
            const matchText = document.getElementById('passwordMatchText');
            
            if (confirmPassword === '') {
                matchText.textContent = 'تأكيد كلمة المرور';
                matchText.className = 'text-muted';
            } else if (newPassword === confirmPassword) {
                matchText.textContent = '✓ كلمة المرور متطابقة';
                matchText.className = 'text-success';
            } else {
                matchText.textContent = '✗ كلمة المرور غير متطابقة';
                matchText.className = 'text-danger';
            }
        }

        // إعادة تعيين النموذج
        function resetForm() {
            if (confirm('هل أنت متأكد من إعادة تعيين النموذج؟')) {
                document.getElementById('changePasswordForm').reset();
                document.getElementById('passwordStrengthBar').style.width = '0%';
                document.getElementById('passwordStrengthText').textContent = 'قوة كلمة المرور';
                document.getElementById('passwordMatchText').textContent = 'تأكيد كلمة المرور';
                document.getElementById('passwordMatchText').className = 'text-muted';
            }
        }

        // تأكيد قبل الإرسال
        document.getElementById('changePasswordForm').addEventListener('submit', function(e) {
            const submitBtn = document.getElementById('submitBtn');
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري التحديث...';
            submitBtn.disabled = true;
        });

        // إخفاء رسائل التنبيه تلقائياً
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);
    </script>
} 