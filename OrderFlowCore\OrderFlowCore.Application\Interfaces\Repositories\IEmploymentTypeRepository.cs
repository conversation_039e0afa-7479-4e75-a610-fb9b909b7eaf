using OrderFlowCore.Application.DTOs;

namespace OrderFlowCore.Application.Interfaces.Repositories;

public interface IEmploymentTypeRepository
{
    Task<IEnumerable<EmploymentTypeDto>> GetAllAsync();
    Task<EmploymentTypeDto?> GetByIdAsync(int id);
    Task<bool> CreateAsync(EmploymentTypeDto dto);
    Task<bool> UpdateAsync(EmploymentTypeDto dto);
    Task<bool> DeleteAsync(int id);
    Task<bool> ExistsAsync(int id);
}
