using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Core.Models;
using System.Threading.Tasks;

namespace OrderFlowCore.Application.Interfaces.Repositories
{
    public interface IAutoRouteRepository
    {
        Task<AutoRouting> GetAutoRouteAsync(string orderType, string nationality, string job);
        Task<AutoRouteDto> GetByIdAsync(int id);
        Task<bool> CreateAsync(AutoRouteDto dto);
        Task<bool> UpdateAsync(AutoRouteDto dto);
        Task<bool> DeleteAsync(int id);
        Task<bool> ExistsAsync(string orderType, string nationality, string job, int? excludeId = null);
    }
}
