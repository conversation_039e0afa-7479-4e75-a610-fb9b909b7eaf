using OrderFlowCore.Application.Common;
using OrderFlowCore.Core.Entities;

namespace OrderFlowCore.Application.Interfaces.Services
{
    public interface IDashboardService
    {
        Task<ServiceResult<DashboardStatisticsDto>> GetDashboardStatisticsAsync();
    }

    public class DashboardStatisticsDto
    {
        public OrderStatisticsDto OrderStatistics { get; set; } = new();
        public DepartmentStatisticsDto DepartmentStatistics { get; set; } = new();
        public EmployeeStatisticsDto EmployeeStatistics { get; set; } = new();
        public WorkflowStatisticsDto WorkflowStatistics { get; set; } = new();
    }

    public class OrderStatisticsDto
    {
        public int TotalOrders { get; set; }
        public int PendingOrders { get; set; }
        public int CompletedOrders { get; set; }
        public int CancelledOrders { get; set; }
        public int TodayOrders { get; set; }
        public int ThisWeekOrders { get; set; }
        public int ThisMonthOrders { get; set; }
        public List<OrderStatusCountDto> OrdersByStatus { get; set; } = new();
        public List<OrderTypeCountDto> OrdersByType { get; set; } = new();
        public List<DepartmentOrderCountDto> OrdersByDepartment { get; set; } = new();
    }

    public class DepartmentStatisticsDto
    {
        public int TotalDepartments { get; set; }
        public int AssignedDepartments { get; set; }
        public int UnassignedDepartments { get; set; }
        public List<ManagerDepartmentCountDto> DepartmentsByManager { get; set; } = new();
    }

    public class EmployeeStatisticsDto
    {
        public int TotalEmployees { get; set; }
        public List<NationalityCountDto> EmployeesByNationality { get; set; } = new();
        public List<EmploymentTypeCountDto> EmployeesByType { get; set; } = new();
        public List<QualificationCountDto> EmployeesByQualification { get; set; } = new();
    }



    public class WorkflowStatisticsDto
    {
        public int OrdersAtDirectManager { get; set; }
        public int OrdersAtAssistantManagers { get; set; }
        public int OrdersAtCoordinators { get; set; }
        public int OrdersAtSupervisors { get; set; }
        public int OrdersAtManagers { get; set; }
        public int OrdersRequiringAction { get; set; }
        public List<WorkflowStageCountDto> OrdersByWorkflowStage { get; set; } = new();
    }

    // Supporting DTOs
    public class OrderStatusCountDto
    {
        public OrderStatus Status { get; set; }
        public string StatusDisplayName { get; set; } = "";
        public int Count { get; set; }
        public double Percentage { get; set; }
    }

    public class OrderTypeCountDto
    {
        public string OrderType { get; set; } = "";
        public int Count { get; set; }
        public double Percentage { get; set; }
    }

    public class DepartmentOrderCountDto
    {
        public string DepartmentName { get; set; } = "";
        public int Count { get; set; }
        public double Percentage { get; set; }
    }

    public class ManagerDepartmentCountDto
    {
        public AssistantManagerType ManagerType { get; set; }
        public string ManagerName { get; set; } = "";
        public int DepartmentCount { get; set; }
        public double Percentage { get; set; }
    }

    public class NationalityCountDto
    {
        public string Nationality { get; set; } = "";
        public int Count { get; set; }
        public double Percentage { get; set; }
    }

    public class EmploymentTypeCountDto
    {
        public string EmploymentType { get; set; } = "";
        public int Count { get; set; }
        public double Percentage { get; set; }
    }

    public class QualificationCountDto
    {
        public string Qualification { get; set; } = "";
        public int Count { get; set; }
        public double Percentage { get; set; }
    }



    public class WorkflowStageCountDto
    {
        public string StageName { get; set; } = "";
        public int Count { get; set; }
        public double Percentage { get; set; }
        public string StageColor { get; set; } = "";
    }
}
