﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data.SqlClient;
using System.Linq;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Data;  // لاستخدام DataTable
using System.Text;
using System.IO;



namespace abozyad
{
    public partial class WebForm8 : System.Web.UI.Page
    {


        // تقوم هذه الدالة بتنفيذ عدة مهام عند تحميل الصفحة.
        // أولاً، يتم التحقق من صلاحية المستخدم. إذا كانت الصلاحية غير مطابقة لشروط الوصول المحددة (مثل "منسق الموارد البشرية" أو "مدير حسابات")، 
        // يتم إعادة توجيه المستخدم إلى صفحة "AccessDenied.aspx" لمنعه من الوصول.
        // بعد ذلك، في حالة تحميل الصفحة لأول مرة (وليس نتيجة إعادة إرسال بيانات)، يتم التكرار عبر جميع عناصر التحكم في حاوية الـ CheckBox
        // للتحقق مما إذا كانت موجودة في قاعدة البيانات ضمن عمود محدد في جدول المسارات.
        // بناءً على وجود النص في قاعدة البيانات، يتم تحديد حالة مربع الاختيار (Checked أو Unchecked) لكل مربع اختيار على الصفحة.


        protected void Page_Load(object sender, EventArgs e)
        {
            try
            {
                Session["UserPermission"] = "مدير حسابات";
                // التحقق من الصلاحيات
                if (Session["UserPermission"] == null ||
                    (Session["UserPermission"].ToString() != "منسق الموارد البشرية" &&
                     Session["UserPermission"].ToString() != "مدير حسابات"))
                {
                    Response.Redirect("AccessDenied.aspx");
                    return;
                }

                if (!IsPostBack)
                {
                    // تحميل البيانات الأولية
                    LoadInitialData();
                    UpdateActiveCountLabel();
                    UpdateAutoActiveCountLabel();
                    getCheckBoxesStatesFromTablePaths();
                    PopulateDropDownListTypesFromExcel();
                    PopulateDropDownListJobFromDatabase();
                }
                else if (Request["__EVENTTARGET"] == "UpdateEmployeeCount")
                {
                    // تحديث عدد الموظفين إذا كان الحدث هو "UpdateEmployeeCount"
                    UpdateEmployeeCount();
                }
            }
            catch (Exception ex)
            {
                ShowAlert("حدث خطأ أثناء تحميل الصفحة: " + ex.Message, "error");
            }
        }

        private void PopulateDropDownListJobFromDatabase()
        {
            string connectionString = ConfigurationManager.ConnectionStrings["MyConnectionString"].ConnectionString;
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                string query = "SELECT [أنواع الوظائف] FROM jobTypes WHERE [أنواع الوظائف] != 'أخرى - Other'";
                using (SqlCommand command = new SqlCommand(query, connection))
                {
                    connection.Open();
                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        // إضافة الخيارات الافتراضية
                        ddlAutoRequestJob.Items.Add("");
                        ddlDirectRequestJob.Items.Add("");

                        // إضافة الخيار "الكل"
                        ddlAutoRequestJob.Items.Add(new ListItem("الكل", "ALL"));
                        ddlDirectRequestJob.Items.Add(new ListItem("الكل", "ALL"));

                        // إضافة الخيار "الكل باستثناء أخرى"
                        ddlAutoRequestJob.Items.Add(new ListItem("الكل باستثناء أخرى", "ALL_EXCEPT_OTHER"));
                        ddlDirectRequestJob.Items.Add(new ListItem("الكل باستثناء أخرى", "ALL_EXCEPT_OTHER"));

                        // إضافة باقي الوظائف
                        while (reader.Read())
                        {
                            string jobTitle = reader["أنواع الوظائف"].ToString();
                            ddlAutoRequestJob.Items.Add(new ListItem(jobTitle));
                            ddlDirectRequestJob.Items.Add(new ListItem(jobTitle));
                        }
                    }
                }
            }
        }

        private void getCheckBoxesStatesFromTablePaths()
        {
            foreach (Control control in pnlManualRouting.Controls)
            {
                // Check if the control is a container that can have child controls
                if (control.HasControls())
                {
                    foreach (Control childControl in control.Controls)
                    {
                        if (childControl is CheckBox checkBox)
                        {
                            string pathColumn = GetPathColumn(checkBox);

                            // Ensure the CheckBox is associated with a valid column
                            if (!string.IsNullOrEmpty(pathColumn))
                            {
                                // Check if the text exists in the PathsTable
                                bool exists = CheckIfExistsInPathTable(checkBox.Text, pathColumn);

                                // Set the CheckBox's checked state based on the result
                                checkBox.Checked = exists;
                            }
                        }
                    }
                }
            }
        }

        private void PopulateDropDownListTypesFromExcel()
        {

            string connectionString = ConfigurationManager.ConnectionStrings["MyConnectionString"].ConnectionString;
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                string query = "SELECT  [أنواع الطلبات] FROM ordersTypes";
                using (SqlCommand command = new SqlCommand(query, connection))
                {
                    connection.Open();
                    using (SqlDataReader reader = command.ExecuteReader())
                    {

                        //{
                        ddlDirectRequestType.Items.Add("");
                        ddlAutoRequestType.Items.Add("");

                        while (reader.Read())
                        {

                            //if (reader["الأقسام"].ToString() != "")
                            //{
                            ddlDirectRequestType.Items.Add(new ListItem(reader["أنواع الطلبات"].ToString()));
                            ddlAutoRequestType.Items.Add(new ListItem(reader["أنواع الطلبات"].ToString()));
                            //}
                        }
                    }
                }
            }
        }
        // دالة تحميل البيانات الأولية
        private void LoadInitialData()
        {
            try
            {


                // تحميل المشرفين
                LoadSupervisors();

                LoadAutoRoutes();

                // تحميل المسارات
                LoadDirectRoutes();

            }
            catch (Exception ex)
            {

                LabelError.Visible = true;
                LabelAutoError.Visible = true;
                LabelAutoError.Text = "خطأ في تحميل البيانات الأولية: " + ex.Message;

                LabelError.Text = "خطأ في تحميل البيانات الأولية: " + ex.Message;

                throw new Exception("خطأ في تحميل البيانات الأولية: " + ex.Message);
            }
        }




        // تقوم هذه الدالة بالتحقق مما إذا كان النص المحدد موجودًا في عمود معين بجدول PathsTable.
        // تأخذ الدالة نصًا واسم العمود كمدخلات، ثم تنفذ استعلام SQL للتحقق من وجود النص في العمود.
        // إذا وجدت الدالة أن النص موجود (بناءً على عدد النتائج المسترجعة من قاعدة البيانات)، تعيد القيمة "true"، وإلا تعيد "false".

        private bool CheckIfExistsInPathTable(string text, string pathColumn)
        {
            string connectionString = ConfigurationManager.ConnectionStrings["MyConnectionString"].ConnectionString;
            string query = $"SELECT COUNT(*) FROM PathsTable WHERE {pathColumn} = @Text";

            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                using (SqlCommand command = new SqlCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@Text", text);
                    connection.Open();
                    int count = (int)command.ExecuteScalar();
                    return count > 0;
                }
            }
        }

        // هذه الدالة تُنفّذ عند تغيير حالة أي CheckBox في الصفحة.
        // تقوم الدالة أولاً بالحصول على النص المرتبط بـ CheckBox (النص الظاهر بجانب الـ CheckBox) واسم العمود المناسب في جدول PathsTable.
        // إذا تم تحديد الـ CheckBox (Checked)، تقوم بإضافة النص إلى العمود المناسب في جدول PathsTable.
        // وإذا تم إلغاء تحديد الـ CheckBox (Unchecked)، تقوم بإزالة النص من العمود المناسب في جدول PathsTable.

        protected void CheckBox_CheckedChanged(object sender, EventArgs e)
        {
            CheckBox checkBox = sender as CheckBox;
            string text = checkBox.Text;
            string pathColumn = GetPathColumn(checkBox);

            if (checkBox.Checked)
            {
                AddToPathTable(text, pathColumn);
            }
            else
            {
                RemoveFromPathTable(text, pathColumn);
            }
        }

        // هذه الدالة تستخدم لتحديد العمود المناسب من جدول PathsTable بناءً على الـ CheckBox المحدد.
        // كل مجموعة من الـ CheckBoxs ترتبط بمسار معين (مسار1، مسار2، مسار3).
        // يتم التحقق من معرّف الـ CheckBox (ID) وتحديد العمود المناسب وفقًا لذلك.
        // إذا كان الـ CheckBox لا ينتمي إلى أي من المجموعات المعروفة، تُرجع الدالة قيمة فارغة.

        private string GetPathColumn(CheckBox checkBox)
        {
            // Determine which path the checkbox belongs to
            if (checkBox.ID == "CheckBox1" || checkBox.ID == "CheckBox2" || checkBox.ID == "CheckBox3" ||
                checkBox.ID == "CheckBox4" || checkBox.ID == "CheckBox5" || checkBox.ID == "CheckBox6" ||
                checkBox.ID == "CheckBox7" || checkBox.ID == "CheckBox8" || checkBox.ID == "CheckBox9" ||
                checkBox.ID == "CheckBox10" || checkBox.ID == "CheckBox11" || checkBox.ID == "CheckBox12" ||
                checkBox.ID == "CheckBox13" || checkBox.ID == "CheckBox14" || checkBox.ID == "CheckBox15" ||
                checkBox.ID == "CheckBox16")
            {
                return "مسار1";
            }
            if (checkBox.ID == "CheckBox17" || checkBox.ID == "CheckBox18" || checkBox.ID == "CheckBox19" ||
                checkBox.ID == "CheckBox20" || checkBox.ID == "CheckBox21" || checkBox.ID == "CheckBox22" ||
                checkBox.ID == "CheckBox23" || checkBox.ID == "CheckBox24" || checkBox.ID == "CheckBox25" ||
                checkBox.ID == "CheckBox26" || checkBox.ID == "CheckBox27" || checkBox.ID == "CheckBox28" ||
                checkBox.ID == "CheckBox29" || checkBox.ID == "CheckBox30" || checkBox.ID == "CheckBox31" ||
                checkBox.ID == "CheckBox32")
            {
                return "مسار2";
            }
            if (checkBox.ID == "CheckBox33" || checkBox.ID == "CheckBox34" || checkBox.ID == "CheckBox35" ||
                checkBox.ID == "CheckBox36" || checkBox.ID == "CheckBox37" || checkBox.ID == "CheckBox38" ||
                checkBox.ID == "CheckBox39" || checkBox.ID == "CheckBox40" || checkBox.ID == "CheckBox41" ||
                checkBox.ID == "CheckBox42" || checkBox.ID == "CheckBox43" || checkBox.ID == "CheckBox44" ||
                checkBox.ID == "CheckBox45" || checkBox.ID == "CheckBox46" || checkBox.ID == "CheckBox47" ||
                checkBox.ID == "CheckBox48")
            {
                return "مسار3";
            }
            if (checkBox.ID == "CheckBox49" || checkBox.ID == "CheckBox50" || checkBox.ID == "CheckBox51" ||
       checkBox.ID == "CheckBox52" || checkBox.ID == "CheckBox53" || checkBox.ID == "CheckBox54" ||
       checkBox.ID == "CheckBox55" || checkBox.ID == "CheckBox56" || checkBox.ID == "CheckBox57" ||
       checkBox.ID == "CheckBox58" || checkBox.ID == "CheckBox59" || checkBox.ID == "CheckBox60" ||
       checkBox.ID == "CheckBox61" || checkBox.ID == "CheckBox62" || checkBox.ID == "CheckBox63" ||
       checkBox.ID == "CheckBox64")
            {
                return "مسار4";
            }
            if (checkBox.ID == "CheckBox65" || checkBox.ID == "CheckBox66" || checkBox.ID == "CheckBox67" ||
    checkBox.ID == "CheckBox68" || checkBox.ID == "CheckBox69" || checkBox.ID == "CheckBox70" ||
    checkBox.ID == "CheckBox71" || checkBox.ID == "CheckBox72" || checkBox.ID == "CheckBox73" ||
    checkBox.ID == "CheckBox74" || checkBox.ID == "CheckBox75" || checkBox.ID == "CheckBox76" ||
    checkBox.ID == "CheckBox77" || checkBox.ID == "CheckBox78" || checkBox.ID == "CheckBox79" ||
    checkBox.ID == "CheckBox80")
            {
                return "مسار5";
            }
            if (checkBox.ID == "CheckBox81" || checkBox.ID == "CheckBox82" || checkBox.ID == "CheckBox83" ||
                checkBox.ID == "CheckBox84" || checkBox.ID == "CheckBox85" || checkBox.ID == "CheckBox86" ||
                checkBox.ID == "CheckBox87" || checkBox.ID == "CheckBox88" || checkBox.ID == "CheckBox89" ||
                checkBox.ID == "CheckBox90" || checkBox.ID == "CheckBox91" || checkBox.ID == "CheckBox92" ||
                checkBox.ID == "CheckBox93" || checkBox.ID == "CheckBox94" || checkBox.ID == "CheckBox95" ||
                checkBox.ID == "CheckBox96")
            {
                return "مسار6";
            }
            return string.Empty;
        }


        // تقوم هذه الدالة بإضافة قيمة النص (text) إلى عمود محدد (pathColumn) في جدول PathsTable.
        // يتم استخدام عمود pathColumn المحدد كمعيار لإضافة القيمة النصية (text) إلى الجدول.
        // يتم تنفيذ عملية الإدخال عبر استعلام SQL INSERT.
        // يتم استخدام SqlConnection لفتح الاتصال بقاعدة البيانات، ثم يتم تمرير القيمة النصية إلى الجدول باستخدام SqlCommand.

        private void AddToPathTable(string text, string pathColumn)
        {
            string connectionString = ConfigurationManager.ConnectionStrings["MyConnectionString"].ConnectionString;
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                // تحديث أول صف NULL موجود
                string query = $@"
            UPDATE TOP(1) PathsTable 
            SET {pathColumn} = @Text
            WHERE {pathColumn} IS NULL 
            AND NOT EXISTS (
                SELECT 1 
                FROM PathsTable 
                WHERE {pathColumn} = @Text
            )";

                using (SqlCommand command = new SqlCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@Text", text);
                    connection.Open();
                    command.ExecuteNonQuery();
                }
            }
        }

        // تقوم هذه الدالة بإزالة قيمة النص (text) من عمود محدد (pathColumn) في جدول PathsTable.
        // يتم استخدام عمود pathColumn كمعيار لتحديد السجل الذي سيتم حذفه من الجدول.
        // يتم تنفيذ عملية الحذف عبر استعلام SQL DELETE.
        // يتم استخدام SqlConnection لفتح الاتصال بقاعدة البيانات، ثم يتم تمرير القيمة النصية لتحديد السجل المراد حذفه باستخدام SqlCommand.

        private void RemoveFromPathTable(string text, string pathColumn)
        {
            string connectionString = ConfigurationManager.ConnectionStrings["MyConnectionString"].ConnectionString;
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                // تحديث القيمة إلى NULL بدلاً من حذف الصف
                string query = $"UPDATE PathsTable SET {pathColumn} = NULL WHERE {pathColumn} = @Text";
                using (SqlCommand command = new SqlCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@Text", text);
                    connection.Open();
                    command.ExecuteNonQuery();
                }
            }
        }


        private void LoadSupervisors()
        {
            cblAutoSupervisors.Items.Clear();
            cblDirectSupervisors.Items.Clear();

            string[] supervisors = new string[] {
        "خدمات الموظفين",
        "إدارة تخطيط الموارد البشرية",
        "إدارة تقنية المعلومات",
        "مراقبة الدوام",
        "السجلات الطبية",
        "إدارة الرواتب والاستحقاقات",
        "إدارة القانونية والالتزام",
        "خدمات الموارد البشرية",
        "إدارة الإسكان",
        "قسم الملفات",
        "العيادات الخارجية",
        "التأمينات الاجتماعية",
        "وحدة مراقبة المخزون",
        "إدارة تنمية الإيرادات",
        "إدارة الأمن و السلامة",
        "الطب الاتصالي"
    };

            foreach (string supervisor in supervisors)
            {
                cblAutoSupervisors.Items.Add(new ListItem(supervisor));
                cblDirectSupervisors.Items.Add(new ListItem(supervisor));

            }
        }



        // دالة للتحقق من وجود مسار نشط



        // حدث زر إلغاء التحديد
        protected void btnClearSelection_Click(object sender, EventArgs e)
        {
            ResetSupervisorsList();
        }


        // تعديل دالة تحميل المسار المحفوظ



        private void UpdateSupervisorsList(string path1, string path2, string path3)
        {
            ResetSupervisorsList(); // إعادة تعيين القائمة أولاً

            if (!string.IsNullOrEmpty(path1))
            {
                string[] supervisors = path1.Split(';');
                foreach (string supervisor in supervisors)
                {
                    ListItem item = cblAutoSupervisors.Items.FindByText(supervisor.Trim());
                    if (item != null)
                    {
                        item.Selected = true;
                    }
                }
            }
        }

        // دالة مساعدة لإعادة تعيين قائمة المشرفين
        private void ResetSupervisorsList()
        {
            if (cblAutoSupervisors != null)
            {
                foreach (ListItem item in cblAutoSupervisors.Items)
                {
                    item.Selected = false;
                }
            }
        }

        private void ShowAlert(string message, string icon = "warning")
        {
            string script = $@"
    Swal.fire({{
        title: 'تنبيه',
        text: '{message}',
        icon: '{icon}',
        confirmButtonText: 'حسناً'
    }});";

            ScriptManager.RegisterStartupScript(this, GetType(),
                "alert_" + Guid.NewGuid().ToString(), script, true);
        }





        // دالة إعادة تعيين نموذج المسار السريع
        private void ResetDirectRouteForm()
        {
            ddlDirectRequestType.SelectedIndex = 0;
            ddlDirectNationality.SelectedIndex = 0;
            ddlDirectRequestJob.SelectedIndex = 0;
            txtDirectNotes.Text = string.Empty;
            chkDirectActive.Checked = true;

            // إعادة تعيين قائمة المشرفين
            foreach (ListItem item in cblDirectSupervisors.Items)
            {
                item.Selected = false;
            }
        }

        private void ResetAutoRouteForm()
        {
            ddlAutoRequestType.SelectedIndex = 0;
            ddlAutoNationality.SelectedIndex = 0;
            ddlAutoRequestJob.SelectedIndex = 0;
            txtAutoNotes.Text = string.Empty;
            chkAutoActive.Checked = true;

            // إعادة تعيين قائمة المشرفين
            foreach (ListItem item in cblAutoSupervisors.Items)
            {
                item.Selected = false;
            }
        }


        // معالج حدث زر الحفظ - يبقى كما هو
        // معالج حدث زر الحفظ
        protected void btnSaveDirectRoute_Click(object sender, EventArgs e)
        {
            SaveDirectRoute();
            UpdateActiveCountLabel();

        }

        // دالة الحفظ الرئيسية المحسنة
        private void SaveDirectRoute()
        {
            try
            {
                // التحقق من البيانات المطلوبة
                if (string.IsNullOrEmpty(ddlDirectRequestType.SelectedValue) ||
                    string.IsNullOrEmpty(ddlDirectNationality.SelectedValue) ||
                    string.IsNullOrEmpty(ddlDirectRequestJob.SelectedValue))
                {
                    ShowAlert("يرجى اختيار نوع الطلب والجنسية والوظيفة", "warning");
                    LabelError.Visible = true;
                    LabelMessage.Visible = false;

                    LabelError.Text = "❌يرجى اختيار نوع الطلب والجنسية";
                    return;
                }

                // التحقق من المشرفين المحددين
                var selectedSupervisors = cblDirectSupervisors.Items.Cast<ListItem>()
                    .Where(i => i.Selected)
                    .Select(i => i.Text)
                    .ToList();

                if (!selectedSupervisors.Any())
                {

                    LabelError.Visible = true;
                    LabelMessage.Visible = false;

                    LabelError.Text = "❌يرجى تحديد مشرف واحد على الأقل";
                    ShowAlert("يرجى تحديد مشرف واحد على الأقل", "warning");
                    return;
                }

                // التحقق من وجود مسار مكرر
                bool isEditing = ViewState["IsEditing"] != null && (bool)ViewState["IsEditing"];
                int? routeId = ViewState["EditRouteId"] as int?;

                if (!isEditing && CheckAndHandleDuplicateRoute(ddlDirectRequestType.SelectedValue, ddlDirectNationality.SelectedValue, ddlDirectRequestJob.SelectedValue, routeId, false))
                {
                    return; // CheckExistingSimilarRoute يعرض رسالة التحذير
                }

                // 4. حفظ البيانات
                using (SqlConnection conn = new SqlConnection(
                    ConfigurationManager.ConnectionStrings["MyConnectionString"].ConnectionString))
                {
                    string query = isEditing && routeId.HasValue
                        ? @"UPDATE DirectRouting 
                    SET نوع_الطلب = @RequestType,
                        الجنسية = @Nationality, الوظيفة = @Job,
                        المشرفين = @Supervisors,
                        حالة_المسار = @Status,
                        ملاحظات = @Notes,
                        تاريخ_التحديث = GETDATE(),
                        محدث_المسار = @Modifier
                    WHERE ID = @RouteId"
                        : @"INSERT INTO DirectRouting 
                    (نوع_الطلب, الجنسية,الوظيفة, المشرفين, حالة_المسار, 
                     ملاحظات, تاريخ_الإنشاء, منشئ_المسار)
                    VALUES 
                    (@RequestType, @Nationality, @Job, @Supervisors, @Status, 
                     @Notes, GETDATE(), @Creator)";

                    using (SqlCommand cmd = new SqlCommand(query, conn))
                    {
                        // إضافة البارامترات
                        cmd.Parameters.AddWithValue("@RequestType", ddlDirectRequestType.SelectedValue);
                        cmd.Parameters.AddWithValue("@Nationality", ddlDirectNationality.SelectedValue);
                        cmd.Parameters.AddWithValue("@Job", ddlDirectRequestJob.SelectedValue);
                        cmd.Parameters.AddWithValue("@Supervisors", string.Join(";", selectedSupervisors));
                        cmd.Parameters.AddWithValue("@Status", chkDirectActive.Checked);
                        cmd.Parameters.AddWithValue("@Notes", txtDirectNotes.Text?.Trim() ?? string.Empty);

                        string currentUser = Session["UserName"]?.ToString() ?? "System";
                        if (isEditing && routeId.HasValue)
                        {
                            cmd.Parameters.AddWithValue("@RouteId", routeId.Value);
                            cmd.Parameters.AddWithValue("@Modifier", currentUser);
                        }
                        else
                        {
                            cmd.Parameters.AddWithValue("@Creator", currentUser);
                        }

                        conn.Open();
                        cmd.ExecuteNonQuery();
                    }
                }

                // 5. تحديث العرض وإعادة تعيين النموذج
                ResetDirectRouteForm();
                ViewState["IsEditing"] = false;
                ViewState["EditRouteId"] = null;
                btnSaveDirectRoute.Text = "حفظ المسار";
                LoadDirectRoutes();

                // 6. عرض رسالة النجاح
                ShowAlert(isEditing ? "تم تحديث المسار بنجاح" : "تم إضافة المسار بنجاح", "success");
                LabelMessage.Visible = true;
                if (isEditing)
                {
                    LabelMessage.Text = "تم تحديث المسار بنجاح";
                }
                else
                {
                    LabelMessage.Text = "تم إضافة المسار بنجاح";

                }
                LabelError.Visible = false;

            }
            catch (Exception ex)
            {

                LabelError.Visible = true;
                LabelMessage.Visible = false;

                LabelError.Text = "❌حدث خطأ أثناء حفظ المسار" + ex.Message;


                ShowAlert("حدث خطأ أثناء حفظ المسار: " + ex.Message, "error");
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في حفظ المسار السريع: {ex.Message}");
            }
        }


        protected void btnSaveAutoRoute_Click(object sender, EventArgs e)
        {
            SaveAutoRoute();
            UpdateAutoActiveCountLabel();

        }

        // دالة الحفظ الرئيسية المحسنة
        private void SaveAutoRoute()
        {
            try
            {
                // التحقق من البيانات المطلوبة
                if (string.IsNullOrEmpty(ddlAutoRequestType.SelectedValue) ||
                    string.IsNullOrEmpty(ddlAutoNationality.SelectedValue) ||
                    string.IsNullOrEmpty(ddlAutoRequestJob.SelectedValue))
                {
                    ShowAlert("يرجى اختيار نوع الطلب والجنسية", "warning");
                    LabelAutoError.Visible = true;
                    LabelAutoMessage.Visible = false;
                    LabelAutoError.Text = "❌يرجى اختيار نوع الطلب والجنسية";
                    return;
                }

                // التحقق من المشرفين المحددين
                var selectedSupervisors = cblAutoSupervisors.Items.Cast<ListItem>()
                    .Where(i => i.Selected)
                    .Select(i => i.Text)
                    .ToList();

                if (!selectedSupervisors.Any())
                {
                    LabelAutoError.Visible = true;
                    LabelAutoMessage.Visible = false;
                    LabelAutoError.Text = "❌يرجى تحديد مشرف واحد على الأقل";
                    ShowAlert("يرجى تحديد مشرف واحد على الأقل", "warning");
                    return;
                }

                // التحقق من وجود مسار مكرر
                bool isEditing = ViewState["IsEditingAuto"] != null && (bool)ViewState["IsEditingAuto"];
                int? routeId = ViewState["EditAutoRouteId"] as int?;

                if (!isEditing && CheckAndHandleDuplicateAutoRoute(ddlAutoRequestType.SelectedValue, ddlAutoNationality.SelectedValue, ddlAutoRequestJob.SelectedValue, routeId, false))
                {

                    return; // CheckAndHandleDuplicateRoute يعرض رسالة التحذير
                }

                // حفظ البيانات
                using (SqlConnection conn = new SqlConnection(
                    ConfigurationManager.ConnectionStrings["MyConnectionString"].ConnectionString))
                {
                    string query = isEditing && routeId.HasValue
                        ? @"UPDATE AutoRouting 
                   SET نوع_الطلب = @RequestType,
                       الجنسية = @Nationality,
                        الوظيفة = @Job,
                       المشرفين = @Supervisors,
                       حالة_المسار = @Status,
                       ملاحظات = @Notes,
                       تاريخ_التحديث = GETDATE(),
                       محدث_المسار = @Modifier
                   WHERE ID = @RouteId"
                        : @"INSERT INTO AutoRouting 
                    (نوع_الطلب, الجنسية,الوظيفة, المشرفين, حالة_المسار, 
                    ملاحظات, تاريخ_الإنشاء, منشئ_المسار)
                   VALUES 
                   (@RequestType, @Nationality, @Job, @Supervisors, @Status, 
                    @Notes, GETDATE(), @Creator)";

                    using (SqlCommand cmd = new SqlCommand(query, conn))
                    {
                        // إضافة البارامترات
                        cmd.Parameters.AddWithValue("@RequestType", ddlAutoRequestType.SelectedValue);
                        cmd.Parameters.AddWithValue("@Nationality", ddlAutoNationality.SelectedValue);
                        cmd.Parameters.AddWithValue("@Job", ddlAutoRequestJob.SelectedValue);
                        cmd.Parameters.AddWithValue("@Supervisors", string.Join(";", selectedSupervisors));
                        cmd.Parameters.AddWithValue("@Status", chkAutoActive.Checked);
                        cmd.Parameters.AddWithValue("@Notes", txtAutoNotes.Text?.Trim() ?? string.Empty);

                        string currentUser = Session["UserName"]?.ToString() ?? "System";
                        if (isEditing && routeId.HasValue)
                        {
                            cmd.Parameters.AddWithValue("@RouteId", routeId.Value);
                            cmd.Parameters.AddWithValue("@Modifier", currentUser);
                        }
                        else
                        {
                            cmd.Parameters.AddWithValue("@Creator", currentUser);
                        }

                        conn.Open();
                        cmd.ExecuteNonQuery();
                    }
                }

                // تحديث العرض وإعادة تعيين النموذج
                ResetAutoRouteForm();
                ViewState["IsEditingAuto"] = false;
                ViewState["EditAutoRouteId"] = null;
                btnSaveAutoRoute.Text = "حفظ المسار";
                LoadAutoRoutes();

                // عرض رسالة النجاح
                ShowAlert(isEditing ? "تم تحديث المسار بنجاح" : "تم إضافة المسار بنجاح", "success");
                LabelAutoMessage.Visible = true;
                LabelAutoMessage.Text = isEditing ? "تم تحديث المسار بنجاح" : "تم إضافة المسار بنجاح";
                LabelAutoError.Visible = false;


            }
            catch (Exception ex)
            {
                LabelAutoError.Visible = true;
                LabelAutoMessage.Visible = false;
                LabelAutoError.Text = "❌حدث خطأ أثناء حفظ المسار: " + ex.Message;
                ShowAlert("حدث خطأ أثناء حفظ المسار: " + ex.Message, "error");
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في حفظ المسار التلقائي: {ex.Message}");
            }
        }



        // التحقق من التكرار
        private bool CheckAndHandleDuplicateRoute(string requestType, string nationality, string job, int? currentRouteId, bool loadDetails = false)
        {
            try
            {
                using (SqlConnection conn = new SqlConnection(ConfigurationManager.ConnectionStrings["MyConnectionString"].ConnectionString))
                {
                    string query = @"
SELECT COUNT(1) 
FROM DirectRouting 
WHERE نوع_الطلب = @RequestType 
AND الجنسية = @Nationality 
AND حالة_المسار = 1
AND (@CurrentRouteId IS NULL OR ID != @CurrentRouteId)
AND (
    CASE 
        WHEN الوظيفة = 'ALL' THEN 1
        WHEN الوظيفة = 'ALL_EXCEPT_OTHER' AND @Job NOT LIKE 'أخرى - Other:%' AND @Job != 'ALL' AND @Job != 'ALL_EXCEPT_OTHER' THEN 1
        WHEN الوظيفة = @Job THEN 1
        ELSE 0
    END = 1
)";

                    using (SqlCommand cmd = new SqlCommand(query, conn))
                    {
                        cmd.Parameters.AddWithValue("@RequestType", requestType);
                        cmd.Parameters.AddWithValue("@Nationality", nationality);
                        cmd.Parameters.AddWithValue("@Job", job);
                        cmd.Parameters.AddWithValue("@CurrentRouteId", (object)currentRouteId ?? DBNull.Value);

                        conn.Open();
                        int count = (int)cmd.ExecuteScalar();

                        if (count > 0)
                        {
                            if (loadDetails)
                            {
                                // تحميل تفاصيل المسار إذا كانت مطلوبة
                                using (SqlCommand detailCmd = new SqlCommand(@"
                        SELECT TOP 1 المشرفين, ملاحظات, منشئ_المسار, تاريخ_الإنشاء 
                        FROM DirectRouting 
                        WHERE نوع_الطلب = @RequestType 
                        AND الجنسية = @Nationality 
                        AND الوظيفة = @Job 
                        AND حالة_المسار = 1", conn))
                                {
                                    detailCmd.Parameters.AddWithValue("@RequestType", requestType);
                                    detailCmd.Parameters.AddWithValue("@Nationality", nationality);
                                    detailCmd.Parameters.AddWithValue("@Job", job);

                                    using (SqlDataReader reader = detailCmd.ExecuteReader())
                                    {
                                        if (reader.Read())
                                        {
                                            string supervisors = reader["المشرفين"].ToString();
                                            foreach (ListItem item in cblDirectSupervisors.Items)
                                            {
                                                item.Selected = supervisors.Split(';').Contains(item.Text.Trim());
                                            }

                                            txtDirectNotes.Text = reader["ملاحظات"].ToString();

                                            string creator = reader["منشئ_المسار"].ToString();
                                            DateTime createDate = Convert.ToDateTime(reader["تاريخ_الإنشاء"]);
                                            ShowAlert($"تم تحميل إعدادات مسار مشابه بواسطة {creator} بتاريخ {createDate:dd/MM/yyyy HH:mm}", "info");
                                        }
                                    }
                                }
                            }
                            else
                            {
                                LabelError.Visible = true;
                                LabelMessage.Visible = false;
                                LabelError.Text = "❌يوجد مسار مسجل مسبقاً لنفس الإعدادات";
                                ShowAlert("يوجد مسار مسجل مسبقاً لنفس نوع الطلب والجنسية والوظيفة", "warning");
                            }
                            return true;
                        }
                    }
                }
                return false;
            }
            catch (Exception ex)
            {
                LabelError.Visible = true;
                LabelMessage.Visible = false;
                LabelError.Text = "❌خطأ في التحقق من المسار" + ex.Message;
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في التحقق من المسار: {ex.Message}");
                return false;
            }
        }


        private bool CheckAndHandleDuplicateAutoRoute(string requestType, string nationality, string job, int? currentRouteId, bool loadDetails = false)
        {
            try
            {
                using (SqlConnection conn = new SqlConnection(ConfigurationManager.ConnectionStrings["MyConnectionString"].ConnectionString))
                {
                    // تحسين الاستعلام للتعامل مع ALL و ALL_EXCEPT_OTHER بشكل صحيح
                    string query = @"
SELECT COUNT(1) 
FROM AutoRouting 
WHERE نوع_الطلب = @RequestType 
AND الجنسية = @Nationality 
AND حالة_المسار = 1
AND (@CurrentRouteId IS NULL OR ID != @CurrentRouteId)
AND (
    CASE 
        WHEN الوظيفة = 'ALL' THEN 1
        WHEN الوظيفة = 'ALL_EXCEPT_OTHER' AND @Job NOT LIKE 'أخرى - Other:%' AND @Job != 'ALL' AND @Job != 'ALL_EXCEPT_OTHER' THEN 1
        WHEN الوظيفة = @Job THEN 1
        ELSE 0
    END = 1
)";

                    using (SqlCommand cmd = new SqlCommand(query, conn))
                    {
                        cmd.Parameters.AddWithValue("@RequestType", requestType);
                        cmd.Parameters.AddWithValue("@Nationality", nationality);
                        cmd.Parameters.AddWithValue("@Job", job);
                        cmd.Parameters.AddWithValue("@CurrentRouteId", (object)currentRouteId ?? DBNull.Value);

                        conn.Open();
                        int count = (int)cmd.ExecuteScalar();

                        if (count > 0)
                        {
                            if (loadDetails)
                            {
                                // تحميل تفاصيل المسار إذا كانت مطلوبة
                                using (SqlCommand detailCmd = new SqlCommand(@"
                SELECT TOP 1 المشرفين, ملاحظات, منشئ_المسار, تاريخ_الإنشاء 
                FROM AutoRouting 
                WHERE نوع_الطلب = @RequestType 
                AND الجنسية = @Nationality 
                AND الوظيفة = @Job 
                AND حالة_المسار = 1", conn))
                                {
                                    detailCmd.Parameters.AddWithValue("@RequestType", requestType);
                                    detailCmd.Parameters.AddWithValue("@Nationality", nationality);
                                    detailCmd.Parameters.AddWithValue("@Job", job);

                                    using (SqlDataReader reader = detailCmd.ExecuteReader())
                                    {
                                        if (reader.Read())
                                        {
                                            string supervisors = reader["المشرفين"].ToString();
                                            foreach (ListItem item in cblAutoSupervisors.Items)
                                            {
                                                item.Selected = supervisors.Split(';').Contains(item.Text.Trim());
                                            }

                                            txtAutoNotes.Text = reader["ملاحظات"].ToString();

                                            string creator = reader["منشئ_المسار"].ToString();
                                            DateTime createDate = Convert.ToDateTime(reader["تاريخ_الإنشاء"]);
                                            ShowAlert($"تم تحميل إعدادات مسار مشابه بواسطة {creator} بتاريخ {createDate:dd/MM/yyyy HH:mm}", "info");
                                        }
                                    }
                                }
                            }
                            else
                            {
                                LabelAutoError.Visible = true;
                                LabelAutoMessage.Visible = false;
                                LabelAutoError.Text = "❌يوجد مسار مسجل مسبقاً لنفس الإعدادات";
                                ShowAlert("يوجد مسار مسجل مسبقاً لنفس نوع الطلب والجنسية والوظيفة", "warning");
                            }
                            return true;
                        }
                    }
                }
                return false;
            }
            catch (Exception ex)
            {
                LabelAutoError.Visible = true;
                LabelAutoMessage.Visible = false;
                LabelAutoError.Text = "❌خطأ في التحقق من المسار" + ex.Message;
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في التحقق من المسار: {ex.Message}");
                return false;
            }
        }






        // دالة تحميل المسارات السريعة
        private void LoadDirectRoutes()
        {
            try
            {
                using (SqlConnection conn = new SqlConnection(ConfigurationManager.ConnectionStrings["MyConnectionString"].ConnectionString))
                {
                    string query = @"
                SELECT 
                    ID,
                    نوع_الطلب,
                    الجنسية,
                    الوظيفة,
                    المشرفين,
                    حالة_المسار,
                    ملاحظات,
                    تاريخ_الإنشاء,
                    منشئ_المسار,
                    -- تنسيق حالة المسار
                    CASE 
                        WHEN حالة_المسار = 1 THEN N'نشط'
                        ELSE N'غير نشط'
                    END AS حالة_المسار_نص,
                    -- حساب عدد المشرفين
                    LEN(المشرفين) - LEN(REPLACE(المشرفين, ';', '')) + 1 AS عدد_المشرفين,
                    -- حساب إجمالي المسارات النشطة
                    (SELECT COUNT(*) FROM DirectRouting WHERE حالة_المسار = 1) AS عدد_المسارات_النشطة
                FROM DirectRouting 
                ORDER BY تاريخ_الإنشاء DESC";

                    using (SqlCommand cmd = new SqlCommand(query, conn))
                    {
                        using (SqlDataAdapter adapter = new SqlDataAdapter(cmd))
                        {
                            DataTable dt = new DataTable();
                            adapter.Fill(dt);

                            if (dt.Rows.Count > 0)
                            {
                                gvDirectRoutes.DataSource = dt;
                                gvDirectRoutes.DataBind();

                                // تحديث عداد المسارات النشطة
                                int activeCount = 0;
                                foreach (DataRow row in dt.Rows)
                                {
                                    if (Convert.ToBoolean(row["حالة_المسار"]))
                                    {
                                        activeCount++;
                                    }
                                }


                            }
                            else
                            {
                                gvDirectRoutes.DataSource = null;
                                gvDirectRoutes.DataBind();

                            }


                        }
                    }
                }
            }
            catch (Exception ex)
            {
                ShowAlert("حدث خطأ أثناء تحميل المسارات: " + ex.Message, "error");
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحميل المسارات: {ex.Message}");

                LabelError.Visible = true;
                LabelMessage.Visible = false;

                LabelError.Text = "❌حدث خطأ أثناء تحميل المسارات" + ex.Message;



            }
        }
        //auto

        private void LoadAutoRoutes()
        {
            try
            {
                using (SqlConnection conn = new SqlConnection(ConfigurationManager.ConnectionStrings["MyConnectionString"].ConnectionString))
                {
                    string query = @"
        SELECT 
            ID,
            نوع_الطلب,
            الجنسية,
            الوظيفة,
            المشرفين,
            حالة_المسار,
            ملاحظات,
            تاريخ_الإنشاء,
            منشئ_المسار,
            -- تنسيق حالة المسار
            CASE 
                WHEN حالة_المسار = 1 THEN N'نشط'
                ELSE N'غير نشط'
            END AS حالة_المسار_نص,
            -- حساب عدد المشرفين
            LEN(المشرفين) - LEN(REPLACE(المشرفين, ';', '')) + 1 AS عدد_المشرفين,
            -- حساب إجمالي المسارات النشطة
            (SELECT COUNT(*) FROM AutoRouting WHERE حالة_المسار = 1) AS عدد_المسارات_النشطة
        FROM AutoRouting 
        ORDER BY تاريخ_الإنشاء DESC";

                    using (SqlCommand cmd = new SqlCommand(query, conn))
                    {
                        using (SqlDataAdapter adapter = new SqlDataAdapter(cmd))
                        {
                            DataTable dt = new DataTable();
                            adapter.Fill(dt);

                            if (dt.Rows.Count > 0)
                            {
                                gvAutoRoutes.DataSource = dt;
                                gvAutoRoutes.DataBind();


                            }
                            else
                            {
                                gvAutoRoutes.DataSource = null;
                                gvAutoRoutes.DataBind();

                            }


                        }
                    }
                }
            }
            catch (Exception ex)
            {
                ShowAlert("حدث خطأ أثناء تحميل المسارات: " + ex.Message, "error");
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحميل المسارات: {ex.Message}");

                LabelAutoError.Visible = true;
                LabelAutoMessage.Visible = false;

                LabelAutoError.Text = "❌حدث خطأ أثناء تحميل المسارات" + ex.Message;
            }
        }




        // دالة تحديث عداد المسارات النشطة
        private void UpdateActiveCountLabel()
        {
            try
            {
                using (SqlConnection conn = new SqlConnection(ConfigurationManager.ConnectionStrings["MyConnectionString"].ConnectionString))
                {
                    string query = "SELECT COUNT(*) FROM DirectRouting WHERE حالة_المسار <> 0";

                    using (SqlCommand cmd = new SqlCommand(query, conn))
                    {
                        conn.Open();
                        int activeCount = (int)cmd.ExecuteScalar();
                        lblActiveCount.Text = $"عدد المسارات السريعة النشطة: {activeCount}";
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحديث عداد المسارات النشطة: {ex.Message}");
                lblActiveCount.Text = "خطأ في تحديث العداد";
            }
        }
        private void UpdateAutoActiveCountLabel()
        {
            try
            {
                using (SqlConnection conn = new SqlConnection(ConfigurationManager.ConnectionStrings["MyConnectionString"].ConnectionString))
                {
                    string query = "SELECT COUNT(*) FROM AutoRouting WHERE حالة_المسار <> 0";

                    using (SqlCommand cmd = new SqlCommand(query, conn))
                    {
                        conn.Open();
                        int autoActiveCount = (int)cmd.ExecuteScalar();
                        lblAutoActiveCount.Text = $"عدد المسارات التلقائية النشطة: {autoActiveCount}";
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحديث عداد المسارات التلقائية النشطة: {ex.Message}");
                lblAutoActiveCount.Text = "خطأ في تحديث العداد";
            }
        }







        // معالج أحداث GridView للتحكم بالمسارات
        protected void gvDirectRoutes_RowCommand(object sender, GridViewCommandEventArgs e)
        {
            try
            {
                int routeId = Convert.ToInt32(e.CommandArgument);

                switch (e.CommandName)
                {
                    case "EditRoute":
                        LoadRouteForEdit(routeId);
                        break;

                    case "ToggleStatus":
                        ToggleDirectRouteStatus(routeId);
                        break;

                    case "ViewSupervisors":
                        ShowSupervisorsList(routeId);
                        break;

                }
            }
            catch (Exception ex)
            {
                ShowAlert("حدث خطأ أثناء تنفيذ العملية: " + ex.Message, "error");
            }
        }


        private void LoadRouteForEdit(int routeId)
        {
            try
            {
                using (SqlConnection conn = new SqlConnection(ConfigurationManager.ConnectionStrings["MyConnectionString"].ConnectionString))
                {
                    string query = @"
                SELECT 
                    نوع_الطلب,
                    الجنسية,
                    الوظيفة,
                    المشرفين,
                    حالة_المسار,
                    ملاحظات
                FROM DirectRouting 
                WHERE ID = @RouteId";

                    using (SqlCommand cmd = new SqlCommand(query, conn))
                    {
                        cmd.Parameters.AddWithValue("@RouteId", routeId);

                        conn.Open();
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                // حفظ ID المسار في ViewState للاستخدام عند التحديث
                                ViewState["EditRouteId"] = routeId;

                                // تعبئة نوع الطلب
                                ddlDirectRequestType.SelectedValue = reader["نوع_الطلب"].ToString();

                                // تعبئة الجنسية
                                ddlDirectNationality.SelectedValue = reader["الجنسية"].ToString();

                                // تعبئة الوظيفة
                                ddlDirectRequestJob.SelectedValue = reader["الوظيفة"].ToString();

                                // تعبئة المشرفين
                                string supervisors = reader["المشرفين"].ToString();
                                foreach (ListItem item in cblDirectSupervisors.Items)
                                {
                                    item.Selected = supervisors.Split(';')
                                        .Contains(item.Text.Trim());
                                }

                                // تعبئة الحالة
                                chkDirectActive.Checked = Convert.ToBoolean(reader["حالة_المسار"]);

                                // تعبئة الملاحظات
                                txtDirectNotes.Text = reader["ملاحظات"].ToString();

                                // تغيير نص زر الحفظ
                                btnSaveDirectRoute.Text = "تحديث المسار";

                                // تمرير للوضعية التعديل
                                ViewState["IsEditing"] = true;

                                // إظهار رسالة للمستخدم
                                ShowAlert("تم تحميل بيانات المسار للتعديل", "info");
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                ShowAlert("حدث خطأ أثناء تحميل بيانات المسار: " + ex.Message, "error");
            }
        }

        // دالة تبديل حالة المسار (تفعيل/تعطيل)
        private void ToggleDirectRouteStatus(int routeId)
        {
            try
            {
                using (SqlConnection conn = new SqlConnection(ConfigurationManager.ConnectionStrings["MyConnectionString"].ConnectionString))
                {
                    string query = @"
                UPDATE DirectRouting 
                SET حالة_المسار = ~حالة_المسار 
                WHERE ID = @RouteId;
                
                SELECT حالة_المسار 
                FROM DirectRouting 
                WHERE ID = @RouteId";

                    using (SqlCommand cmd = new SqlCommand(query, conn))
                    {
                        cmd.Parameters.AddWithValue("@RouteId", routeId);

                        conn.Open();
                        bool newStatus = (bool)cmd.ExecuteScalar();

                        // تحديث العرض
                        LoadDirectRoutes();

                        // عرض رسالة مناسبة


                        //LabelMessage.Visible = true;
                        LabelError.Visible = false;

                        string statusMessage = newStatus ? "تم تفعيل" : "تم تعطيل";
                        ShowAlert($"{statusMessage} المسار بنجاح", "success");
                        UpdateActiveCountLabel();

                        //LabelMessage.Text = statusMessage + " المسار بنجاح";
                    }
                }
            }
            catch (Exception ex)
            {
                ShowAlert("حدث خطأ أثناء تغيير حالة المسار: " + ex.Message, "error");
            }
        }

        // دالة عرض قائمة المشرفين في نافذة منبثقة
        private void ShowSupervisorsList(int routeId)
        {
            try
            {
                using (SqlConnection conn = new SqlConnection(ConfigurationManager.ConnectionStrings["MyConnectionString"].ConnectionString))
                {
                    string query = "SELECT المشرفين FROM DirectRouting WHERE ID = @RouteId";

                    using (SqlCommand cmd = new SqlCommand(query, conn))
                    {
                        cmd.Parameters.AddWithValue("@RouteId", routeId);

                        conn.Open();
                        string supervisors = cmd.ExecuteScalar()?.ToString();

                        if (!string.IsNullOrEmpty(supervisors))
                        {
                            string formattedSupervisors = supervisors.Replace(";", "<br>");
                            string script = $@"
                        Swal.fire({{
                            title: 'قائمة المشرفين',
                            html: '{formattedSupervisors}',
                            icon: 'info'
                        }});";

                            ScriptManager.RegisterStartupScript(this, GetType(),
                                "showSupervisors", script, true);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                ShowAlert("حدث خطأ أثناء عرض قائمة المشرفين: " + ex.Message, "error");
            }
        }
        // إضافة زر إلغاء التعديل
        protected void btnCancelEdit_Click(object sender, EventArgs e)
        {
            ResetDirectRouteForm();
            ViewState["IsEditing"] = false;
            ViewState["EditRouteId"] = null;
            btnSaveDirectRoute.Text = "حفظ المسار";
            ShowAlert("تم إلغاء عملية التعديل", "info");
        }


        //تلقائي
        protected void gvAutoRoutes_RowCommand(object sender, GridViewCommandEventArgs e)
        {
            try
            {
                int routeId = Convert.ToInt32(e.CommandArgument);

                switch (e.CommandName)
                {
                    case "EditRoute":
                        LoadAutoRouteForEdit(routeId);
                        break;

                    case "ToggleStatus":
                        ToggleAutoRouteStatus(routeId);
                        break;

                    case "ViewSupervisors":
                        ShowAutoSupervisorsList(routeId);
                        break;
                }
            }
            catch (Exception ex)
            {
                ShowAlert("حدث خطأ أثناء تنفيذ العملية: " + ex.Message, "error");
            }
        }

        private void LoadAutoRouteForEdit(int routeId)
        {
            try
            {
                using (SqlConnection conn = new SqlConnection(ConfigurationManager.ConnectionStrings["MyConnectionString"].ConnectionString))
                {
                    string query = @"
        SELECT 
            نوع_الطلب,
            الجنسية,
            الوظيفة,
            المشرفين,
            حالة_المسار,
            ملاحظات
        FROM AutoRouting 
        WHERE ID = @RouteId";

                    using (SqlCommand cmd = new SqlCommand(query, conn))
                    {
                        cmd.Parameters.AddWithValue("@RouteId", routeId);

                        conn.Open();
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                ViewState["EditAutoRouteId"] = routeId;

                                ddlAutoRequestType.SelectedValue = reader["نوع_الطلب"].ToString();
                                ddlAutoNationality.SelectedValue = reader["الجنسية"].ToString();
                                ddlAutoRequestJob.SelectedValue = reader["الوظيفة"].ToString();

                                string supervisors = reader["المشرفين"].ToString();
                                foreach (ListItem item in cblAutoSupervisors.Items)
                                {
                                    item.Selected = supervisors.Split(';')
                                        .Contains(item.Text.Trim());
                                }

                                chkAutoActive.Checked = Convert.ToBoolean(reader["حالة_المسار"]);
                                txtAutoNotes.Text = reader["ملاحظات"].ToString();

                                btnSaveAutoRoute.Text = "تحديث المسار";
                                ViewState["IsEditingAuto"] = true;

                                ShowAlert("تم تحميل بيانات المسار للتعديل", "info");
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                ShowAlert("حدث خطأ أثناء تحميل بيانات المسار: " + ex.Message, "error");
            }
        }

        private void ToggleAutoRouteStatus(int routeId)
        {
            try
            {
                using (SqlConnection conn = new SqlConnection(ConfigurationManager.ConnectionStrings["MyConnectionString"].ConnectionString))
                {
                    string query = @"
        UPDATE AutoRouting 
        SET حالة_المسار = ~حالة_المسار 
        WHERE ID = @RouteId;
        
        SELECT حالة_المسار 
        FROM AutoRouting 
        WHERE ID = @RouteId";

                    using (SqlCommand cmd = new SqlCommand(query, conn))
                    {
                        cmd.Parameters.AddWithValue("@RouteId", routeId);

                        conn.Open();
                        bool newStatus = (bool)cmd.ExecuteScalar();

                        LoadAutoRoutes();

                        LabelAutoError.Visible = false;

                        string statusMessage = newStatus ? "تم تفعيل" : "تم تعطيل";
                        ShowAlert($"{statusMessage} المسار بنجاح", "success");

                        UpdateAutoActiveCountLabel();

                    }
                }
            }
            catch (Exception ex)
            {
                ShowAlert("حدث خطأ أثناء تغيير حالة المسار: " + ex.Message, "error");
            }
        }

        private void ShowAutoSupervisorsList(int routeId)
        {
            try
            {
                using (SqlConnection conn = new SqlConnection(ConfigurationManager.ConnectionStrings["MyConnectionString"].ConnectionString))
                {
                    string query = "SELECT المشرفين FROM AutoRouting WHERE ID = @RouteId";

                    using (SqlCommand cmd = new SqlCommand(query, conn))
                    {
                        cmd.Parameters.AddWithValue("@RouteId", routeId);

                        conn.Open();
                        string supervisors = cmd.ExecuteScalar()?.ToString();

                        if (!string.IsNullOrEmpty(supervisors))
                        {
                            string formattedSupervisors = supervisors.Replace(";", "<br>");
                            string script = $@"
                Swal.fire({{
                    title: 'قائمة المشرفين',
                    html: '{formattedSupervisors}',
                    icon: 'info'
                }});";

                            ScriptManager.RegisterStartupScript(this, GetType(),
                                "showSupervisors", script, true);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                ShowAlert("حدث خطأ أثناء عرض قائمة المشرفين: " + ex.Message, "error");
            }
        }

        protected void btnCancelEditAuto_Click(object sender, EventArgs e)
        {
            ResetAutoRouteForm();
            ViewState["IsAutoEditing"] = false;
            ViewState["EditAutoRouteId"] = null;
            btnSaveAutoRoute.Text = "حفظ المسار";
            ShowAlert("تم إلغاء عملية التعديل", "info");
        }





        // معالج حدث تغيير الجنسية





        // دوال التبويب
       private void HideAllPanels()
{
    pnlDirectRouting.Visible = false;
    pnlAutoRouting.Visible = false;
    pnlManualRouting.Visible = false;
    pnlSettings.Visible = false;
}

protected void btnManualTab_Click(object sender, EventArgs e)
{
    try
    {
        ViewState["ActiveTab"] = "Manual";
        HideAllPanels();
        pnlManualRouting.Visible = true;
        UpdateTabButtonsStyle("Manual");
    }
    catch (Exception ex)
    {
        ShowAlert("حدث خطأ أثناء تغيير التبويب: " + ex.Message, "error");
    }
}

protected void btnAutoTab_Click(object sender, EventArgs e)
{
    try
    {
        ViewState["ActiveTab"] = "Auto";
        HideAllPanels();
        pnlAutoRouting.Visible = true;
        UpdateTabButtonsStyle("Auto");
    }
    catch (Exception ex)
    {
        ShowAlert("حدث خطأ أثناء تغيير التبويب: " + ex.Message, "error");
    }
}

protected void btnDirectTab_Click(object sender, EventArgs e)
{
    try
    {
        ViewState["ActiveTab"] = "Direct";
        HideAllPanels();
        pnlDirectRouting.Visible = true;
        UpdateTabButtonsStyle("Direct");
    }
    catch (Exception ex)
    {
        ShowAlert("حدث خطأ أثناء تغيير التبويب: " + ex.Message, "error");
    }
}

protected void btnSettingsTab_Click(object sender, EventArgs e)
{
    try
    {
        ViewState["ActiveTab"] = "Settings";
        HideAllPanels();
        pnlSettings.Visible = true;
        UpdateTabButtonsStyle("Settings");
    }
    catch (Exception ex)
    {
        ShowAlert("حدث خطأ أثناء تغيير التبويب: " + ex.Message, "error");
    }
}

private void UpdateTabButtonsStyle(string activeTab)
{
    var tabs = new Dictionary<string, Button>
    {
        { "Manual", btnManualTab },
        { "Auto", btnAutoTab },
        { "Direct", btnDirectTab },
        { "Settings", btnSettingsTab }
    };

    foreach (var tab in tabs)
    {
        tab.Value.CssClass = "tab-btn" + (tab.Key == activeTab ? " active" : "");
    }
}
        // تعديل معالج الأحداث الحالي ليشمل حالة الحذف
        protected void gvDirectRoutes_RowCommand1(object sender, GridViewCommandEventArgs e)
        {
            try
            {
                int routeId = Convert.ToInt32(e.CommandArgument);

                switch (e.CommandName)
                {
                    case "DeleteRoute":
                        DeleteDirectRoute(routeId);
                        break;
                    // ... باقي الحالات الموجودة ...
                    case "EditRoute":
                        LoadRouteForEdit(routeId);
                        break;
                    case "ToggleStatus":
                        ToggleDirectRouteStatus(routeId);
                        break;
                }
            }
            catch (Exception ex)
            {
                ShowAlert("حدث خطأ أثناء تنفيذ العملية: " + ex.Message, "error");
            }
        }

        // دالة حذف المسار السريع
        private void DeleteDirectRoute(int routeId)
        {
            try
            {
                string connectionString = ConfigurationManager.ConnectionStrings["MyConnectionString"].ConnectionString;
                using (SqlConnection conn = new SqlConnection(connectionString))
                {
                    string deleteQuery = "DELETE FROM DirectRouting WHERE ID = @RouteId";
                    using (SqlCommand cmd = new SqlCommand(deleteQuery, conn))
                    {
                        cmd.Parameters.AddWithValue("@RouteId", routeId);
                        conn.Open();
                        int rowsAffected = cmd.ExecuteNonQuery();

                        if (rowsAffected > 0)
                        {
                            ShowAlert("تم حذف المسار بنجاح", "success");
                            LoadDirectRoutes(); // تحديث الجدول
                            UpdateActiveCountLabel(); // تحديث العداد
                        }
                        else
                        {
                            ShowAlert("لم يتم العثور على المسار", "warning");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                ShowAlert("حدث خطأ أثناء حذف المسار: " + ex.Message, "error");
                System.Diagnostics.Debug.WriteLine($"خطأ في حذف المسار: {ex.Message}");
            }
        }

        // تعديل معالج الأحداث للمسار التلقائي
        protected void gvAutoRoutes_RowCommand1(object sender, GridViewCommandEventArgs e)
        {
            try
            {
                int routeId = Convert.ToInt32(e.CommandArgument);

                switch (e.CommandName)
                {
                    case "DeleteRoute":
                        DeleteAutoRoute(routeId);
                        break;
                    // ... باقي الحالات الموجودة ...
                    case "EditRoute":
                        LoadAutoRouteForEdit(routeId);
                        break;
                    case "ToggleStatus":
                        ToggleAutoRouteStatus(routeId);
                        break;
                }
            }
            catch (Exception ex)
            {
                ShowAlert("حدث خطأ أثناء تنفيذ العملية: " + ex.Message, "error");
            }
        }

        // دالة حذف المسار التلقائي
        private void DeleteAutoRoute(int routeId)
        {
            try
            {
                string connectionString = ConfigurationManager.ConnectionStrings["MyConnectionString"].ConnectionString;
                using (SqlConnection conn = new SqlConnection(connectionString))
                {
                    string deleteQuery = "DELETE FROM AutoRouting WHERE ID = @RouteId";
                    using (SqlCommand cmd = new SqlCommand(deleteQuery, conn))
                    {
                        cmd.Parameters.AddWithValue("@RouteId", routeId);
                        conn.Open();
                        int rowsAffected = cmd.ExecuteNonQuery();

                        if (rowsAffected > 0)
                        {
                            ShowAlert("تم حذف المسار بنجاح", "success");
                            LoadAutoRoutes(); // تحديث الجدول
                            UpdateAutoActiveCountLabel(); // تحديث العداد
                        }
                        else
                        {
                            ShowAlert("لم يتم العثور على المسار", "warning");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                ShowAlert("حدث خطأ أثناء حذف المسار: " + ex.Message, "error");
                System.Diagnostics.Debug.WriteLine($"خطأ في حذف المسار: {ex.Message}");
            }
        }
        /// <summary>
        /// دالة للتحقق من تطابق المسمى الوظيفي مع مسار معين
        /// </summary>
        /// <param name="requestJobTitle">المسمى الوظيفي في الطلب</param>
        /// <param name="pathJobTitle">المسمى الوظيفي في المسار</param>
        /// <returns>true إذا كان هناك تطابق، false في حالة عدم التطابق</returns>
        private bool IsJobTitleMatching(string requestJobTitle, string pathJobTitle)
        {
            // إذا كان المسار مضبوط على "الكل"
            if (pathJobTitle == "ALL")
            {
                return true;
            }

            // إذا كان المسار مضبوط على "الكل باستثناء أخرى"
            if (pathJobTitle == "ALL_EXCEPT_OTHER")
            {
                return !requestJobTitle.StartsWith("أخرى - Other:");
            }

            // في حالة الوظائف المحددة بشكل صريح
            return requestJobTitle == pathJobTitle;
        }
       

        protected void btnSaveSettings_Click(object sender, EventArgs e)
        {
            try
            {
                // حفظ الإعدادات في قاعدة البيانات
                SaveSystemSettings();
                ShowAlert("تم حفظ الإعدادات بنجاح", "success");
            }
            catch (Exception ex)
            {
                ShowAlert("حدث خطأ أثناء حفظ الإعدادات: " + ex.Message, "error");
            }
        }

        private void SaveSystemSettings()
        {
            string connectionString = ConfigurationManager.ConnectionStrings["MyConnectionString"].ConnectionString;
            using (SqlConnection conn = new SqlConnection(connectionString))
            {
                string query = @"UPDATE SystemSettings SET 
            MaxActivePaths = @MaxActivePaths,
            MaxSupervisors = @MaxSupervisors,
            AutoDeactivate = @AutoDeactivate,
            RequireNotes = @RequireNotes";

                using (SqlCommand cmd = new SqlCommand(query, conn))
                {
                    cmd.Parameters.AddWithValue("@MaxActivePaths", Convert.ToInt32(txtMaxActivePaths.Text));
                    cmd.Parameters.AddWithValue("@MaxSupervisors", Convert.ToInt32(txtMaxSupervisors.Text));
                    cmd.Parameters.AddWithValue("@AutoDeactivate", chkAutoDeactivate.Checked);
                    cmd.Parameters.AddWithValue("@RequireNotes", chkRequireNotes.Checked);

                    conn.Open();
                    cmd.ExecuteNonQuery();
                }
            }
        }
        private readonly string connectionString = ConfigurationManager.ConnectionStrings["MyConnectionString"].ConnectionString;
        protected void btnExportEmployees_Click(object sender, EventArgs e)
        {
            try
            {
                using (SqlConnection con = new SqlConnection(connectionString))
                {
                    string query = @"SELECT [اسم الموظف], [الوظيفة], [رقم الموظف], 
                                    [السجل المدني], [الجنسية], [رقم الجوال], 
                                    [نوع التوظيف], [المؤهل] 
                             FROM Employees";

                    using (SqlCommand cmd = new SqlCommand(query, con))
                    {
                        con.Open();
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            StringBuilder sb = new StringBuilder();
                            sb.AppendLine("اسم الموظف;الوظيفة;رقم الموظف;السجل المدني;الجنسية;رقم الجوال;نوع التوظيف;المؤهل");

                            while (reader.Read())
                            {
                                sb.AppendLine($"{reader["اسم الموظف"]};{reader["الوظيفة"]};{reader["رقم الموظف"]};" +
                                              $"{reader["السجل المدني"]};{reader["الجنسية"]};{reader["رقم الجوال"]};" +
                                              $"{reader["نوع التوظيف"]};{reader["المؤهل"]}");
                            }

                            Response.Clear();
                            Response.Buffer = true;
                            Response.AddHeader("content-disposition", "attachment;filename=Employees.csv");
                            Response.Charset = "";
                            Response.ContentType = "application/text";
                            Response.ContentEncoding = System.Text.Encoding.UTF8;
                            Response.Output.Write("\uFEFF");
                            Response.Output.Write(sb.ToString());
                            Response.Flush();
                            Response.End();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                ShowAlert("خطأ في تصدير البيانات: " + ex.Message, "error");
            }
        }

        protected void btnImportEmployees_Click(object sender, EventArgs e)
        {
            if (fileUploadEmployees.HasFile)
            {
                try
                {
                    // استخدام الترميز UTF-8 لقراءة النصوص العربية بشكل صحيح
                    StreamReader sr = new StreamReader(fileUploadEmployees.FileContent, Encoding.UTF8);
                    string line;
                    int successCount = 0;
                    int errorCount = 0;
                    bool isFirstLine = true;
                    StringBuilder errorMessages = new StringBuilder();

                    using (SqlConnection con = new SqlConnection(connectionString))
                    {
                        con.Open();
                        using (SqlTransaction transaction = con.BeginTransaction())
                        {
                            try
                            {
                                while ((line = sr.ReadLine()) != null)
                                {
                                    if (isFirstLine)
                                    {
                                        isFirstLine = false;
                                        continue;
                                    }

                                    if (string.IsNullOrWhiteSpace(line)) continue;

                                    string[] fields = line.Split(';');
                                    if (fields.Length < 8)
                                    {
                                        errorCount++;
                                        errorMessages.AppendLine("خطأ: السطر لا يحتوي على جميع الحقول المطلوبة.");
                                        continue;
                                    }

                                    string checkQuery = "SELECT COUNT(*) FROM Employees WHERE [السجل المدني] = @CivilRegistry";
                                    using (SqlCommand checkCmd = new SqlCommand(checkQuery, con, transaction))
                                    {
                                        checkCmd.Parameters.AddWithValue("@CivilRegistry", fields[3].Trim());
                                        int count = (int)checkCmd.ExecuteScalar();

                                        string query = count > 0
                                        ? @"UPDATE Employees SET 
                                            [اسم الموظف] = @Name, [الوظيفة] = @Job, [رقم الموظف] = @EmpNo,
                                            [الجنسية] = @Nationality, [رقم الجوال] = @Mobile,
                                            [نوع التوظيف] = @EmploymentType, [المؤهل] = @Qualification
                                            WHERE [السجل المدني] = @CivilRegistry"
                                        : @"INSERT INTO Employees ([اسم الموظف], [الوظيفة], [رقم الموظف],
                                            [السجل المدني], [الجنسية], [رقم الجوال], [نوع التوظيف], [المؤهل])
                                            VALUES (@Name, @Job, @EmpNo, @CivilRegistry, @Nationality, @Mobile, @EmploymentType, @Qualification)";

                                        using (SqlCommand cmd = new SqlCommand(query, con, transaction))
                                        {
                                            cmd.Parameters.AddWithValue("@Name", fields[0].Trim());
                                            cmd.Parameters.AddWithValue("@Job", fields[1].Trim());
                                            cmd.Parameters.AddWithValue("@EmpNo", fields[2].Trim());
                                            cmd.Parameters.AddWithValue("@CivilRegistry", fields[3].Trim());
                                            cmd.Parameters.AddWithValue("@Nationality", fields[4].Trim());
                                            cmd.Parameters.AddWithValue("@Mobile", fields[5].Trim());
                                            cmd.Parameters.AddWithValue("@EmploymentType", fields[6].Trim());
                                            cmd.Parameters.AddWithValue("@Qualification", fields[7].Trim());

                                            try
                                            {
                                                cmd.ExecuteNonQuery();
                                                successCount++;
                                            }
                                            catch (Exception ex)
                                            {
                                                errorCount++;
                                                errorMessages.AppendLine($"خطأ في السطر {successCount + errorCount}: {ex.Message}");
                                            }
                                        }
                                    }
                                }

                                if (successCount > 0)
                                {
                                    transaction.Commit();
                                    ShowAlert($"تم استيراد/تحديث {successCount} سجل بنجاح" +
                                            (errorCount > 0 ? $". فشل {errorCount} سجل" : ""), "success");
                                }
                                else
                                {
                                    transaction.Rollback();
                                    ShowAlert("لم يتم استيراد أي سجلات. " + errorMessages.ToString(), "error");
                                }
                            }
                            catch (Exception ex)
                            {
                                transaction.Rollback();
                                throw ex;
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    ShowAlert("خطأ في استيراد الملف: " + ex.Message, "error");
                }
            }
            else
            {
                ShowAlert("الرجاء اختيار ملف للاستيراد", "warning");
            }
        }
        protected void btnDeleteEmployees_Click(object sender, EventArgs e)
        {
            try
            {
                using (SqlConnection con = new SqlConnection(connectionString))
                {
                    con.Open();
                    string deleteQuery = "DELETE FROM Employees";

                    using (SqlCommand cmd = new SqlCommand(deleteQuery, con))
                    {
                        int rowsAffected = cmd.ExecuteNonQuery();

                        if (rowsAffected > 0)
                        {
                            ShowAlert($"تم حذف {rowsAffected} سجل بنجاح", "success");
                        }
                        else
                        {
                            ShowAlert("لا توجد بيانات لحذفها", "info");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                ShowAlert("خطأ في عملية الحذف: " + ex.Message, "error");
            }
        }
        protected void UpdateEmployeeCount()
        {
            string query = "SELECT COUNT(*) FROM dbo.Employees";

            using (SqlConnection con = new SqlConnection(connectionString))
            {
                con.Open();
                using (SqlCommand cmd = new SqlCommand(query, con))
                {
                    int totalEmployees = (int)cmd.ExecuteScalar();
                    string script = $@"document.getElementById('employeeCount').innerText = '{totalEmployees}';";
                    ScriptManager.RegisterStartupScript(this, GetType(), "updateEmployeeCount", script, true);
                }
            }
        }




    }
}
