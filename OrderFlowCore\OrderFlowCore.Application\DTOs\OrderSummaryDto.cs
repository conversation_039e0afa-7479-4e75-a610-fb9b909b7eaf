using OrderFlowCore.Core.Entities;
using System;

namespace OrderFlowCore.Application.DTOs
{
    public class OrderSummaryDto
    {
        public int Id { get; set; }
        public string EmployeeName { get; set; }
        public string OrderType { get; set; }
        public string Department { get; set; }
        public DateTime CreatedAt { get; set; }
        public OrderStatus OrderStatus { get; set; }
    }
} 