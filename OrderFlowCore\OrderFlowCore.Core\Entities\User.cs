﻿using System.ComponentModel.DataAnnotations;

namespace OrderFlowCore.Core.Models;

public partial class User
{
    public int Id { get; set; }
    [StringLength(100)]
    public string Username { get; set; }

    [StringLength(128)]
    public string Password { get; set; }

    [StringLength(255)]
    public string Role { get; set; }

    [StringLength(100)]
    public string Email { get; set; }

    [StringLength(20)]
    public string? Phone { get; set; }

    [StringLength(100)]
    public string? ResetToken { get; set; }

    public DateTime? ResetTokenExpiry { get; set; }
}