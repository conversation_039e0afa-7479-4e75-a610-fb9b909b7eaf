using OrderFlowCore.Application.Interfaces.Repositories;
using OrderFlowCore.Core.Models;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using System.Linq;
using OrderFlowCore.Core.Entities;

namespace OrderFlowCore.Infrastructure.Data
{
    public class OrderRepository : IOrderRepository
    {
        private readonly ApplicationDbContext _context;
        public OrderRepository(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<int> AddAsync(OrdersTable order)
        {
            _context.OrdersTables.Add(order);
            return order.Id;
        }
        public async Task<int> UpdateAsync(OrdersTable order)
        {
            _context.OrdersTables.Update(order);
            return order.Id;
        }

        public async Task<OrdersTable> GetByIdAsync(int id)
        {
            return await _context.OrdersTables.FindAsync(id);
        }

        public async Task<List<OrdersTable>> GetAllAsync()
        {
            return await _context.OrdersTables.ToListAsync();
        }

        public async Task<List<OrdersTable>> GetPendingOrdersForDirectMangerAsync()
        {
            // Get orders that are pending manager approval (ConfirmedByDepartmentManager is null or empty)
            return await _context.OrdersTables
                .Where(o => o.OrderStatus == OrderStatus.DM || o.OrderStatus == OrderStatus.ReturnedByAssistantManager)
                .OrderByDescending(o => o.CreatedAt)
                .ToListAsync();
        }

        public async Task<List<OrdersTable>> GetAssistantManagerOrdersAsync(AssistantManagerType assistantManagerId)
        {
            OrderStatus status = assistantManagerId.ToOrderStatus();
            return await _context.OrdersTables
                .Where(o => o.OrderStatus == status || o.OrderStatus == OrderStatus.ReturnedByCoordinator )
                .OrderByDescending(o => o.CreatedAt)
                .ThenByDescending(o => o.Id)
                .ToListAsync();
        }

        public async Task<OrdersTable?> GetOrderByIdAsync(int orderId)
        {
            return await _context.OrdersTables.FindAsync(orderId);
        }

        public Task<List<OrdersTable>> GetOrdersByStatusesAsync(string[] statuses)
        {
            // Get orders by multiple statuses
            return _context.OrdersTables
                .Where(o => statuses.Contains(o.OrderStatus.ToString()))
                .OrderByDescending(o => o.CreatedAt)
                .ToListAsync();
        }

        public Task<List<OrdersTable>> GetOrdersByStatusAsync(string[] statuses)
        {
            // Alternative method name for compatibility
            return GetOrdersByStatusesAsync(statuses);
        }

        public async Task<List<SupervisorRejectionDto>> GetSupervisorRejectionsAsync(int orderId)
        {
            var order = await _context.OrdersTables.FindAsync(orderId);
            if (order == null) return new List<SupervisorRejectionDto>();

            var rejections = new List<SupervisorRejectionDto>();
            var supervisorColumns = new Dictionary<string, string>
            {
                { "مشرف خدمات الموظفين", order.EmployeeServicesSuper },
                { "مشرف إدارة تخطيط الموارد البشرية", order.HRPlanningSuper },
                { "مشرف إدارة تقنية المعلومات", order.ITSuper },
                { "مشرف مراقبة الدوام", order.AttendanceControlSuper },
                { "مشرف السجلات الطبية", order.MedicalRecordsSuper },
                { "مشرف إدارة الرواتب والاستحقاقات", order.PayrollSuper },
                { "مشرف إدارة القانونية والالتزام", order.LegalComplianceSuper },
                { "مشرف خدمات الموارد البشرية", order.HRServicesSuper },
                { "مشرف إدارة الإسكان", order.HousingSuper },
                { "مشرف قسم الملفات", order.FilesSectionSuper },
                { "مشرف العيادات الخارجية", order.OutpatientSuper },
                { "مشرف التأمينات الاجتماعية", order.SocialInsuranceSuper },
                { "مشرف وحدة مراقبة المخزون", order.InventoryControlSuper },
                { "مشرف إدارة تنمية الإيرادات", order.RevenueSuper },
                { "مشرف إدارة الأمن و السلامة", order.SecuritySuper },
                { "مشرف الطب الاتصالي", order.TeleMedicineSuper }
            };

            foreach (var supervisor in supervisorColumns)
            {
                if (!string.IsNullOrEmpty(supervisor.Value) && supervisor.Value.Contains("تم الإعادة"))
                {
                    rejections.Add(new SupervisorRejectionDto
                    {
                        SupervisorName = supervisor.Key,
                        RejectionDetails = supervisor.Value,
                        RejectionReason = supervisor.Value,
                        RejectionDate = DateTime.Now.ToString("yyyy-MM-dd") // You might want to extract actual date from the text
                    });
                }
            }

            return rejections;
        }

        public async Task UpdateSupervisorStatusesAsync(int orderId, List<string> selectedSupervisors, string statusWithDate)
        {
            var order = await _context.OrdersTables.FindAsync(orderId);
            if (order == null) return;

            var supervisorMapping = new Dictionary<string, Action<string>>
            {
                { "خدمات الموظفين", value => order.EmployeeServicesSuper = value },
                { "إدارة تخطيط الموارد البشرية", value => order.HRPlanningSuper = value },
                { "إدارة تقنية المعلومات", value => order.ITSuper = value },
                { "مراقبة الدوام", value => order.AttendanceControlSuper = value },
                { "السجلات الطبية", value => order.MedicalRecordsSuper = value },
                { "إدارة الرواتب والاستحقاقات", value => order.PayrollSuper = value },
                { "إدارة القانونية والالتزام", value => order.LegalComplianceSuper = value },
                { "خدمات الموارد البشرية", value => order.HRServicesSuper = value },
                { "إدارة الإسكان", value => order.HousingSuper = value },
                { "قسم الملفات", value => order.FilesSectionSuper = value },
                { "العيادات الخارجية", value => order.OutpatientSuper = value },
                { "التأمينات الاجتماعية", value => order.SocialInsuranceSuper = value },
                { "وحدة مراقبة المخزون", value => order.InventoryControlSuper = value },
                { "إدارة تنمية الإيرادات", value => order.RevenueSuper = value },
                { "إدارة الأمن و السلامة", value => order.SecuritySuper = value },
                { "الطب الاتصالي", value => order.TeleMedicineSuper = value }
            };

            foreach (var supervisor in selectedSupervisors)
            {
                if (supervisorMapping.ContainsKey(supervisor))
                {
                    supervisorMapping[supervisor](statusWithDate);
                }
            }

            _context.OrdersTables.Update(order);
        }

        public async Task ClearSupervisorStatusesAsync(int orderId)
        {
            var order = await _context.OrdersTables.FindAsync(orderId);
            if (order == null) return;

            // Clear all supervisor statuses
            order.EmployeeServicesSuper = null;
            order.HRPlanningSuper = null;
            order.ITSuper = null;
            order.AttendanceControlSuper = null;
            order.MedicalRecordsSuper = null;
            order.PayrollSuper = null;
            order.LegalComplianceSuper = null;
            order.HRServicesSuper = null;
            order.HousingSuper = null;
            order.FilesSectionSuper = null;
            order.OutpatientSuper = null;
            order.SocialInsuranceSuper = null;
            order.InventoryControlSuper = null;
            order.RevenueSuper = null;
            order.SecuritySuper = null;
            order.TeleMedicineSuper = null;

            _context.OrdersTables.Update(order);
        }

        public async Task<List<RestorableOrderDto>> GetRestorableOrdersAsync(string searchTerm, string filter)
        {
            var query = _context.OrdersTables
                .Where(o => o.OrderStatus.ToString() == "(C)" || o.OrderStatus.ToString() == "(D)");

            // Apply search filter
            if (!string.IsNullOrWhiteSpace(searchTerm))
            {
                query = query.Where(o => o.Id.ToString().Contains(searchTerm) ||
                                       o.EmployeeName.Contains(searchTerm));
            }

            // Apply date filter
            var today = DateTime.Today;
            switch (filter?.ToLower())
            {
                case "today":
                    query = query.Where(o => o.CreatedAt.HasValue && o.CreatedAt.Value.Date == today);
                    break;
                case "week":
                    var weekStart = today.AddDays(-(int)today.DayOfWeek);
                    query = query.Where(o => o.CreatedAt.HasValue && o.CreatedAt.Value.Date >= weekStart);
                    break;
                case "month":
                    var monthStart = new DateTime(today.Year, today.Month, 1);
                    query = query.Where(o => o.CreatedAt.HasValue && o.CreatedAt.Value.Date >= monthStart);
                    break;
                case "all":
                default:
                    // No additional filter
                    break;
            }

            var orders = await query
                .OrderByDescending(o => o.CreatedAt)
                .ToListAsync();

            return orders.Select(o => new RestorableOrderDto
            {
                Id = o.Id,
                OrderNumber = o.Id.ToString(),
                EmployeeName = o.EmployeeName,
                Department = o.Department,
                OrderDate = o.CreatedAt?.ToString("yyyy-MM-dd"),
                OrderStatus = o.OrderStatus,
                DisplayText = $"{o.Id} | {o.EmployeeName} | {o.Department}"
            }).ToList();
        }

        public async Task<RestoreDetailsDto> GetRestoreOrderDetailsAsync(int orderId)
        {
            var order = await _context.OrdersTables.FindAsync(orderId);
            if (order == null) return null;

            var assignedSupervisors = GetAssignedSupervisors(order);

            return new RestoreDetailsDto
            {
                CurrentStatus = order.OrderStatus.ToString(),
                TransferDate = order.CreatedAt?.ToString("yyyy-MM-dd") ?? DateTime.Now.ToString("yyyy-MM-dd"),
                AssignedSupervisors = string.Join(", ", assignedSupervisors),
                SupervisorCount = assignedSupervisors.Count
            };
        }

        public async Task UpdateOrderStatusesAsync()
        {
            var orders = await _context.OrdersTables
                .Where(o => o.OrderStatus.ToString() != "مقبول")
                .ToListAsync();

            foreach (var order in orders)
            {
                bool allSupervisorColumnsEmpty = true;
                bool anyColumnRejected = false;
                bool allNonEmptyColumnsSigned = true;

                var supervisorStatuses = GetSupervisorStatuses(order);

                foreach (var status in supervisorStatuses.Values)
                {
                    if (!string.IsNullOrEmpty(status))
                    {
                        allSupervisorColumnsEmpty = false;

                        if (status.Contains("تم الإعادة"))
                        {
                            anyColumnRejected = true;
                            break;
                        }
                        else if (!status.Contains("اعتماد بواسطة"))
                        {
                            allNonEmptyColumnsSigned = false;
                        }
                    }
                }

                if (!allSupervisorColumnsEmpty && anyColumnRejected)
                {
                    order.OrderStatus = OrderStatus.ReturnedBySupervisor;
                }
                else if (!allSupervisorColumnsEmpty && allNonEmptyColumnsSigned)
                {
                    order.OrderStatus = OrderStatus.ApprovedBySupervisors;
                }

                _context.OrdersTables.Update(order);
            }
        }

        private List<string> GetAssignedSupervisors(OrdersTable order)
        {
            var supervisors = new List<string>();
            var supervisorStatuses = GetSupervisorStatuses(order);

            foreach (var supervisor in supervisorStatuses)
            {
                if (!string.IsNullOrEmpty(supervisor.Value) && supervisor.Value != "/")
                {
                    supervisors.Add(supervisor.Key);
                }
            }

            return supervisors;
        }

        private Dictionary<string, string> GetSupervisorStatuses(OrdersTable order)
        {
            return new Dictionary<string, string>
            {
                { "مشرف خدمات الموظفين", order.EmployeeServicesSuper },
                { "مشرف إدارة تخطيط الموارد البشرية", order.HRPlanningSuper },
                { "مشرف إدارة تقنية المعلومات", order.ITSuper },
                { "مشرف مراقبة الدوام", order.AttendanceControlSuper },
                { "مشرف السجلات الطبية", order.MedicalRecordsSuper },
                { "مشرف إدارة الرواتب والاستحقاقات", order.PayrollSuper },
                { "مشرف إدارة القانونية والالتزام", order.LegalComplianceSuper },
                { "مشرف خدمات الموارد البشرية", order.HRServicesSuper },
                { "مشرف إدارة الإسكان", order.HousingSuper },
                { "مشرف قسم الملفات", order.FilesSectionSuper },
                { "مشرف العيادات الخارجية", order.OutpatientSuper },
                { "مشرف التأمينات الاجتماعية", order.SocialInsuranceSuper },
                { "مشرف وحدة مراقبة المخزون", order.InventoryControlSuper },
                { "مشرف إدارة تنمية الإيرادات", order.RevenueSuper },
                { "مشرف إدارة الأمن و السلامة", order.SecuritySuper },
                { "مشرف الطب الاتصالي", order.TeleMedicineSuper }
            };
        }
    }
}