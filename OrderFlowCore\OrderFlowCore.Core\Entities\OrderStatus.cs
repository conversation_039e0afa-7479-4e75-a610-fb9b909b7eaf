namespace OrderFlowCore.Core.Entities
{
    public enum OrderStatus
    {
        DM, // Direct Manager
        A1, A2, A3, A4, // Assistant Managers
        B, // Coordinator
        C, // Supervisor
        D, // Manager


        ReturnedByCoordinator,
        ReturnedBySupervisor,
        ReturnedByAssistantManager,
        ReturnedByManager,

        ActionRequired,
        ActionRequiredBySupervisor,

        CancelledByDepartmentManager,
        CancelledByAssistantManager,
        CancelledBySupervisor,
        CancelledByCoordinator,
        CancelledByManager,

        Accepted,
    }

}