using System.Collections.Generic;
using System.Threading.Tasks;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Application.Common;

namespace OrderFlowCore.Application.Interfaces.Services;

public interface INationalityService
{
    Task<ServiceResult<IEnumerable<NationalityDto>>> GetAllAsync();
    Task<ServiceResult<NationalityDto>> GetByIdAsync(int id);
    Task<ServiceResult> CreateAsync(NationalityDto dto);
    Task<ServiceResult> UpdateAsync(NationalityDto dto);
    Task<ServiceResult> DeleteAsync(int id);
}
