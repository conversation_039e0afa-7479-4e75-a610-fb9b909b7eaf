﻿using System;
using System.Configuration;
using System.Data.SqlClient;
using System.IO.Compression;
using System.IO;
using System.Web.UI.WebControls;
using abozyad.Helpers;
using System.Collections.Generic;

namespace abozyad
{

    // تقوم هذه الدالة بمعالجة تحميل الصفحة عند استدعائها:
    // 1. التحقق من صلاحيات المستخدم المخزنة في الجلسة:
    //    - إذا كانت الصلاحيات غير موجودة أو كانت للمستخدمين "منسق الموارد البشرية"، "مدير حسابات"، أو تبدأ بـ "مشرف"، يتم إعادة توجيه المستخدم إلى صفحة "AccessDenied.aspx".
    // 2. إذا كان يتم تحميل الصفحة لأول مرة (وليس بسبب PostBack)، يتم استدعاء الدالة PopulateOrderNumbers() لتحميل أرقام الطلبات في الصفحة.

    public partial class WebForm10 : System.Web.UI.Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            
            if (!IsPostBack)
            {
                PopulateOrderNumbers();
            }
        }

        private string CleanFileName(string fileName)
        {
            // إزالة الأحرف غير المسموح بها في أسماء الملفات
            char[] invalidChars = System.IO.Path.GetInvalidFileNameChars();
            foreach (char c in invalidChars)
            {
                fileName = fileName.Replace(c, '_');
            }

            // التأكد من أن الاسم لا يتجاوز طولاً معيناً
            const int maxLength = 100;
            if (fileName.Length > maxLength)
            {
                string extension = System.IO.Path.GetExtension(fileName);
                fileName = fileName.Substring(0, maxLength - extension.Length) + extension;
            }

            return fileName;
        }

        protected void btnDownload_Click(object sender, EventArgs e)
        {
            try
            {
                string selectedOrderNumber = ddlOrderNumbers.SelectedValue;
                string connectionString = ConfigurationManager.ConnectionStrings["MyConnectionString"].ConnectionString;

                using (SqlConnection con = new SqlConnection(connectionString))
                {
                    string query = @"SELECT ملف1, ملف2, ملف3, ملف4, [اسم الموظف] 
                           FROM ordersTable 
                           WHERE [رقم الطلب] = @OrderNumber";

                    using (SqlCommand cmd = new SqlCommand(query, con))
                    {
                        cmd.Parameters.AddWithValue("@OrderNumber", selectedOrderNumber);
                        con.Open();

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                string employeeName = reader["اسم الموظف"].ToString().Trim();
                                bool filesFound = false;

                                using (MemoryStream zipStream = new MemoryStream())
                                {
                                    using (ZipArchive zipArchive = new ZipArchive(zipStream, ZipArchiveMode.Create, true))
                                    {
                                        for (int i = 0; i < 4; i++)
                                        {
                                            byte[] compressedData = reader[i] as byte[];
                                            if (compressedData != null && compressedData.Length > 0)
                                            {
                                                try
                                                {
                                                    // فك ضغط البيانات
                                                    byte[] pdfData = FileCompressor.ExtractFile(compressedData);

                                                    if (pdfData != null && IsPdfFile(pdfData))
                                                    {
                                                        filesFound = true;
                                                        string fileName = CleanFileName($"مرفق_{i + 1}_طلب_{selectedOrderNumber}_{employeeName}.pdf");

                                                        ZipArchiveEntry zipEntry = zipArchive.CreateEntry(fileName);
                                                        using (Stream entryStream = zipEntry.Open())
                                                        {
                                                            entryStream.Write(pdfData, 0, pdfData.Length);
                                                        }

                                                        System.Diagnostics.Debug.WriteLine($"✅ تم إضافة الملف: {fileName}");
                                                    }
                                                    else
                                                    {
                                                        System.Diagnostics.Debug.WriteLine($"❌ الملف {i + 1} ليس PDF صالح");
                                                    }
                                                }
                                                catch (Exception ex)
                                                {
                                                    System.Diagnostics.Debug.WriteLine($"❌ خطأ في معالجة الملف {i + 1}: {ex.Message}");
                                                    continue;  // استمر مع الملف التالي
                                                }
                                            }
                                        }
                                    }

                                    if (filesFound)
                                    {
                                        string zipFileName = CleanFileName($"مرفقات_طلب_{selectedOrderNumber}_{employeeName}.zip");

                                        Response.Clear();
                                        Response.ContentType = "application/zip";
                                        Response.AddHeader("Content-Disposition", $"attachment; filename={zipFileName}");
                                        Response.BinaryWrite(zipStream.ToArray());
                                        Response.Flush();
                                        Response.End();
                                    }
                                    else
                                    {
                                        LabelError.Text = "لا توجد ملفات صالحة لتحميلها.";
                                        LabelError.Visible = true;
                                    }
                                }
                            }
                            else
                            {
                                LabelError.Text = "لم يتم العثور على الطلب.";
                                LabelError.Visible = true;
                            }
                        }
                    }
                }
            }
            catch (System.Threading.ThreadAbortException)
            {
                // تجاهل
            }
            catch (Exception ex)
            {
                LabelError.Text = "حدث خطأ أثناء تحميل الملفات: " + ex.Message;
                LabelError.Visible = true;
                System.Diagnostics.Debug.WriteLine($"❌ خطأ عام: {ex.Message}");
            }
        }

        private bool IsPdfFile(byte[] data)
        {
            if (data == null || data.Length < 4)
                return false;

            // التحقق من PDF header
            return data[0] == 0x25 && // %
                   data[1] == 0x50 && // P
                   data[2] == 0x44 && // D
                   data[3] == 0x46;   // F
        }



        /// <summary>
        /// تحديد امتداد الملف بناءً على محتواه - تم تحسينها للتعامل مع PDF فقط
        /// </summary>
        private string DetermineFileExtension(byte[] fileData)
        {
            return ValidatePdfFormat(fileData) ? ".pdf" : null;
        }

        /// <summary>
        /// التحقق من أن الملف هو PDF صالح
        /// </summary>
        private bool ValidatePdfFormat(byte[] data)
        {
            try
            {
                if (data == null || data.Length < 4)
                {
                    System.Diagnostics.Debug.WriteLine("❌ البيانات فارغة أو قصيرة جداً");
                    return false;
                }

                // فحص توقيع PDF (PDF Signature)
                bool isPdf = data[0] == 0x25 && // %
                            data[1] == 0x50 && // P
                            data[2] == 0x44 && // D
                            data[3] == 0x46;   // F

                if (!isPdf)
                {
                    System.Diagnostics.Debug.WriteLine("❌ الملف ليس بصيغة PDF صالحة");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("✅ تم التحقق من صحة ملف PDF");
                }

                return isPdf;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في فحص نوع الملف: {ex.Message}");
                return false;
            }
        }

        // تقوم هذه الدالة بمعالجة تغيير القيمة المختارة في القائمة المنسدلة لأرقام الطلبات:
        // 1. إذا كانت القيمة المختارة ليست "0"، يتم استدعاء الدالة LoadOrderDetails لتحميل تفاصيل الطلب بناءً على رقم الطلب المختار.

        protected void ddlOrderNumbers_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (ddlOrderNumbers.SelectedValue != "0")
            {
                LoadOrderDetails(ddlOrderNumbers.SelectedValue);
            }
        }


        // تقوم هذه الدالة بتحميل تفاصيل الطلب بناءً على رقم الطلب الممرر كمعامل.
        // 1. الاتصال بقاعدة البيانات باستخدام SqlConnection.
        // 2. تنفيذ استعلام SQL لاسترجاع تفاصيل الطلبات المرتبطة برقم الطلب المحدد.
        // 3. تعيين القيم المسترجعة في الحقول المناسبة (مثل رقم الطلب، حالة الطلب، تفاصيل المشرفين...إلخ).
        // 4. إذا تم العثور على الطلب، يتم عرض التفاصيل في OrderDetailsPanel.
        // 5. إذا لم يتم العثور على الطلب، يتم إخفاء OrderDetailsPanel.
        private void LoadOrderDetails(string orderNumber)
        {
            string connectionString = ConfigurationManager.ConnectionStrings["MyConnectionString"].ConnectionString;

            using (SqlConnection con = new SqlConnection(connectionString))
            {
                string query = @"SELECT [رقم الطلب], [تاريخ الطلب], [حالة الطلب], [نوع الطلب], [اسم الموظف],
                [القسم], [تفاصيل مقدم الطلب], [الوظيفة], [رقم الموظف], [السجل المدني], 
                [الجنسية], [رقم الجوال], [نوع التوظيف], [المؤهل], 
                [تم التأكيد/الإلغاء من مدير القسم], [تم التأكيد/الإلغاء من قبل المشرف], 
                [تم التحويل/الإلغاء من قبل المنسق], [سبب الإلغاء/الإعادة], [تفاصيل المنسق], 
                [مشرف خدمات الموظفين], [مشرف إدارة تخطيط الموارد البشرية], 
                [مشرف إدارة تقنية المعلومات], [مشرف مراقبة الدوام], [مشرف السجلات الطبية], 
                [مشرف إدارة الرواتب والاستحقاقات], [مشرف إدارة القانونية والالتزام], 
                [مشرف خدمات الموارد البشرية], [مشرف إدارة الإسكان], [مشرف قسم الملفات], 
                [مشرف العيادات الخارجية], [مشرف التأمينات الاجتماعية], [مشرف وحدة مراقبة المخزون], 
                [مشرف إدارة تنمية الإيرادات], [مشرف إدارة الأمن و السلامة], [مشرف الطب الاتصالي], 
                [مدير الموارد البشرية], [نوع التحويل], [ملاحظات المشرفين], 
                [ملف1], [ملف2], [ملف3], [ملف4]  
            FROM ordersTable 
            WHERE [رقم الطلب] = @OrderNumber AND [حالة الطلب] = '(DM)'";

                using (SqlCommand cmd = new SqlCommand(query, con))
                {
                    cmd.Parameters.AddWithValue("@OrderNumber", orderNumber);
                    con.Open();
                    SqlDataReader reader = cmd.ExecuteReader();

                    if (reader.Read())
                    {
                        SetLabelVisibilityAndText(LabelOrderNumber, reader["رقم الطلب"].ToString());
                        SetLabelVisibilityAndText(LabelOrderDate, reader["تاريخ الطلب"].ToString());
                        SetLabelVisibilityAndText(LabelOrderStatus, reader["حالة الطلب"].ToString());
                        SetLabelVisibilityAndText(LabelOrderType, reader["نوع الطلب"].ToString());
                        SetLabelVisibilityAndText(LabelEmployeeName, reader["اسم الموظف"].ToString());
                        SetLabelVisibilityAndText(LabelDepartment, reader["القسم"].ToString());
                        SetLabelVisibilityAndText(LabelNotes, reader["تفاصيل مقدم الطلب"].ToString());
                        SetLabelVisibilityAndText(LabelJobTitle, reader["الوظيفة"].ToString());
                        SetLabelVisibilityAndText(LabelEmployeeNumber, reader["رقم الموظف"].ToString());
                        SetLabelVisibilityAndText(LabelCivilRegistry, reader["السجل المدني"].ToString());
                        SetLabelVisibilityAndText(LabelNationality, reader["الجنسية"].ToString());
                        SetLabelVisibilityAndText(LabelMobileNumber, reader["رقم الجوال"].ToString());
                        SetLabelVisibilityAndText(LabelEmploymentType, reader["نوع التوظيف"].ToString());
                        SetLabelVisibilityAndText(LabelQualification, reader["المؤهل"].ToString());
                        SetLabelVisibilityAndText(LabelManagerApproval, reader["تم التأكيد/الإلغاء من مدير القسم"].ToString());
                        SetLabelVisibilityAndText(LabelSupervisorApproval, reader["تم التأكيد/الإلغاء من قبل المشرف"].ToString());
                        SetLabelVisibilityAndText(LabelCoordinatorApproval, reader["تم التحويل/الإلغاء من قبل المنسق"].ToString());
                        SetLabelVisibilityAndText(LabelCancellationReason, reader["سبب الإلغاء/الإعادة"].ToString());
                        SetLabelVisibilityAndText(LabelCoordinatorDetails, reader["تفاصيل المنسق"].ToString());
                        SetLabelVisibilityAndText1(LabelMedicalServicesPermission, reader["مشرف خدمات الموظفين"].ToString());
                        SetLabelVisibilityAndText1(LabelHRPlanningPermission, reader["مشرف إدارة تخطيط الموارد البشرية"].ToString());
                        SetLabelVisibilityAndText1(LabelITPermission, reader["مشرف إدارة تقنية المعلومات"].ToString());
                        SetLabelVisibilityAndText1(LabelAttendanceControlPermission, reader["مشرف مراقبة الدوام"].ToString());
                        SetLabelVisibilityAndText1(LabelMedicalRecordsPermission, reader["مشرف السجلات الطبية"].ToString());
                        SetLabelVisibilityAndText1(LabelPayrollPermission, reader["مشرف إدارة الرواتب والاستحقاقات"].ToString());
                        SetLabelVisibilityAndText1(LabelLegalCompliancePermission, reader["مشرف إدارة القانونية والالتزام"].ToString());
                        SetLabelVisibilityAndText1(LabelHRServicesPermission, reader["مشرف خدمات الموارد البشرية"].ToString());
                        SetLabelVisibilityAndText1(LabelHousingPermission, reader["مشرف إدارة الإسكان"].ToString());
                        SetLabelVisibilityAndText1(LabelFilesSectionPermission, reader["مشرف قسم الملفات"].ToString());
                        SetLabelVisibilityAndText1(LabelOutpatientPermission, reader["مشرف العيادات الخارجية"].ToString());
                        SetLabelVisibilityAndText1(LabelSocialInsurancePermission, reader["مشرف التأمينات الاجتماعية"].ToString());
                        SetLabelVisibilityAndText1(LabelInventoryControlPermission, reader["مشرف وحدة مراقبة المخزون"].ToString());
                        SetLabelVisibilityAndText1(LabelSelfResourcesPermission, reader["مشرف إدارة تنمية الإيرادات"].ToString());
                        SetLabelVisibilityAndText1(LabelNursingPermission, reader["مشرف إدارة الأمن و السلامة"].ToString());
                        SetLabelVisibilityAndText1(LabelEmployeeServicesPermission, reader["مشرف الطب الاتصالي"].ToString());

                        OrderDetailsPanel.Visible = true;
                    }
                    else
                    {
                        OrderDetailsPanel.Visible = false;
                    }
                }
            }
        }

        // تقوم هذه الدالة بتعيين نص معين إلى عنصر Label وتحديد ما إذا كان يجب إظهاره أو إخفاؤه.
        // 1. إذا كان النص الممرر غير فارغ أو يحتوي على مسافات فقط، يتم التحقق مما إذا كان يمكن تحويله إلى تاريخ.
        // 2. إذا كان النص يمثل تاريخًا، يتم تحويله إلى صيغة "yyyy-MM-dd" وعرضه في Label.
        // 3. إذا لم يكن النص يمثل تاريخًا، يتم تعيين النص كما هو إلى Label.
        // 4. إذا كان النص فارغًا أو null، يتم إخفاء الـ Label.
        private void SetLabelVisibilityAndText(Label label, string text)
        {
            if (!string.IsNullOrWhiteSpace(text))
            {


                DateTime dateValue;
                if (DateTime.TryParse(text, out dateValue))
                {
                    label.Text = dateValue.ToString("yyyy-MM-dd");
                    label.Visible = true;
                }
                else
                {

                    label.Text = text;
                    label.Visible = true;
                }
            }
            else
            {
                label.Visible = false;
            }


        }
        // تقوم هذه الدالة بتعيين نص إلى عنصر Label مع تعديل النص إذا احتوى على عبارة "اعتماد بواسطة".
        // 1. إذا كان النص يحتوي على "اعتماد بواسطة"، تتم إزالة هذه العبارة.
        // 2. إذا كان النص فارغًا أو null، يتم تعيين النص إلى "/".
        // 3. يتم عرض الـ Label وتعيين النص المعدل إليه.
        private void SetLabelVisibilityAndText1(Label label, string text)
        {
            if (text.Contains("اعتماد بواسطة"))
            {
                text = text.Replace("اعتماد بواسطة", "").Trim();
            }

            if (string.IsNullOrEmpty(text))
            {
                text = "/";
            }

            label.Visible = true; // Make sure the label is visible
            label.Text = text; // Set the text of the label

        }


        // تقوم هذه الدالة بتحميل أرقام الطلبات في القائمة المنسدلة بناءً على صلاحية المستخدم المخزنة في الجلسة:
        // 1. يتم التحقق من صلاحية المستخدم (القسم) المخزنة في الجلسة.
        // 2. إذا كانت الصلاحية غير موجودة أو فارغة، يتم عرض رسالة خطأ.
        // 3. إذا كانت الصلاحية "مدير الموارد البشرية"، يتم استرجاع أرقام الطلبات التي حالتها '(1)'.
        // 4. إذا كانت الصلاحية لقسم آخر، يتم استرجاع أرقام الطلبات التي حالتها '(A)' والمخصصة لهذا القسم.
        // 5. يتم تعيين البيانات المسترجعة إلى القائمة المنسدلة (ddlOrderNumbers)، وإضافة خيار افتراضي في بداية القائمة.
        private void PopulateOrderNumbers()
        {
            string department = "إدارة الأمن والسلامة - Safety & Security";
            if (string.IsNullOrEmpty(department))
            {
                LabelError.Text = "صلاحية المستخدم غير محددة.";
                LabelError.Visible = true;
                ddlOrderNumbers.Items.Clear();
                ddlOrderNumbers.Items.Insert(0, new ListItem("-- اختر رقم الطلب --", ""));
                return;
            }

            string connectionString = ConfigurationManager.ConnectionStrings["MyConnectionString"].ConnectionString;
            using (SqlConnection con = new SqlConnection(connectionString))
            {
                string query = @"
            SELECT DISTINCT
                [رقم الطلب],
                [اسم الموظف],
                CASE 
                    WHEN [اسم الموظف] IS NULL OR [اسم الموظف] = '' 
                    THEN CAST([رقم الطلب] AS NVARCHAR(50))
                    ELSE CAST([رقم الطلب] AS NVARCHAR(50)) + ' | ' + [اسم الموظف]
                END AS DisplayText
            FROM ordersTable 
            WHERE TRIM([القسم]) = @Department
            AND (
                [حالة الطلب] = '(DM)'
                OR (
                    [حالة الطلب] = '(DM)' 
                    AND [تم التأكيد/الإلغاء من قبل المشرف] LIKE '%تمت الإعادة من مساعد المدير%'
                )
                OR (
                    [حالة الطلب] = '(DM)'
                    AND [تم التحويل/الإلغاء من قبل المنسق] LIKE '%تم الإعادة بواسطة%'
                )
            )
            ORDER BY [رقم الطلب] DESC";

                using (SqlCommand cmd = new SqlCommand(query, con))
                {
                    cmd.Parameters.AddWithValue("@Department", department);
                    try
                    {
                        con.Open();
                        SqlDataReader reader = cmd.ExecuteReader();
                        if (reader.HasRows)
                        {
                            ddlOrderNumbers.DataSource = reader;
                            ddlOrderNumbers.DataTextField = "DisplayText";
                            ddlOrderNumbers.DataValueField = "رقم الطلب";
                            ddlOrderNumbers.DataBind();
                            ddlOrderNumbers.Items.Insert(0, new ListItem("-- اختر رقم الطلب --", ""));
                        }
                        else
                        {
                            ddlOrderNumbers.Items.Clear();
                            ddlOrderNumbers.Items.Insert(0, new ListItem("-- لا توجد طلبات متاحة --", ""));
                        }
                    }
                    catch (Exception ex)
                    {
                        LabelError.Text = $"حدث خطأ أثناء تحميل أرقام الطلبات: {ex.Message}";
                        LabelError.Visible = true;
                    }
                }
            }
        }


        // تقوم هذه الدالة بمعالجة تأكيد الطلب عند النقر على زر "Confirm Order":
        // 1. التحقق مما إذا تم اختيار رقم طلب صحيح من القائمة المنسدلة.
        // 2. إذا لم يتم اختيار رقم طلب، يتم عرض رسالة خطأ.
        // 3. يتم استرجاع رقم الطلب المحدد واسم المستخدم من الجلسة.
        // 4. يتم تحديث حالة الطلب في قاعدة البيانات إلى '(A)' وتحديث حقل المشرف الذي قام بالتأكيد.
        // 5. إذا تم التحديث بنجاح، يتم عرض رسالة تأكيد، إخفاء تفاصيل الطلب، وتحديث قائمة أرقام الطلبات.
        // 6. إذا فشل التحديث، يتم عرض رسالة خطأ.
        private readonly string connectionString = ConfigurationManager.ConnectionStrings["MyConnectionString"].ConnectionString;
        protected void btnConfirmOrder_Click(object sender, EventArgs e)
        {
            try
            {
                if (ddlOrderNumbers.SelectedIndex == 0)
                {
                    ShowError("يرجى اختيار رقم الطلب.");
                    return;
                }

                string selectedOrderNumber = ddlOrderNumbers.SelectedValue;
                string confirmedBy = Session["Username"]?.ToString();
                string currentDate = DateTime.Now.ToString("yyyy-MM-dd");
                string approvalText = $"{currentDate} | اعتماد بواسطة {confirmedBy} {currentDate}";

                using (SqlConnection con = new SqlConnection(connectionString))
                {
                    con.Open();
                    using (var transaction = con.BeginTransaction())
                    {
                        try
                        {
                            // الحصول على نوع القسم
                            string deptQuery = @"
                    SELECT d.AssistantManagerID,
                           o.[تم التأكيد/الإلغاء من قبل المشرف]
                    FROM ordersTable o
                    JOIN Departments d ON o.[القسم] = d.[الأقسام]
                    WHERE o.[رقم الطلب] = @OrderNumber";

                            string assistantManagerId;
                            string previousApproval;

                            using (var cmdDept = new SqlCommand(deptQuery, con, transaction))
                            {
                                cmdDept.Parameters.AddWithValue("@OrderNumber", selectedOrderNumber);
                                using (var reader = cmdDept.ExecuteReader())
                                {
                                    if (reader.Read())
                                    {
                                        assistantManagerId = reader[0]?.ToString() ?? "غير معروف";
                                        previousApproval = reader[1]?.ToString();
                                    }
                                    else
                                    {
                                        throw new Exception("لم يتم العثور على الطلب");
                                    }
                                }
                            }

                            // تحديد المسار والحالة الجديدة
                            string newStatus;
                            string statusMessage;
                            string supervisorStatus;

                            if (assistantManagerId == "B" ||
                                (!string.IsNullOrEmpty(previousApproval) && previousApproval.Contains("اعتماد بواسطة")))
                            {
                                // إذا كان القسم B أو كان هناك اعتماد سابق من مساعد المدير
                                newStatus = "(B)";
                                statusMessage = "تم التحويل إلى منسق الموارد البشرية";
                                supervisorStatus = previousApproval; // نحتفظ باعتماد مساعد المدير السابق
                            }
                            else
                            {
                                // المسار العادي للأقسام الأخرى
                                newStatus = $"({assistantManagerId})";
                                statusMessage = "تم التحويل إلى مساعد المدير";
                                supervisorStatus = $"{currentDate} الطلب تحت التنفيذ";
                            }

                            string updateQuery = @"
                    UPDATE ordersTable 
                    SET [حالة الطلب] = @NewStatus,
                        [تم التأكيد/الإلغاء من مدير القسم] = @ApprovalText,
                        [تم التأكيد/الإلغاء من قبل المشرف] = @SupervisorStatus
                    WHERE [رقم الطلب] = @OrderNumber";

                            using (var cmdUpdate = new SqlCommand(updateQuery, con, transaction))
                            {
                                cmdUpdate.Parameters.AddWithValue("@OrderNumber", selectedOrderNumber);
                                cmdUpdate.Parameters.AddWithValue("@NewStatus", newStatus);
                                cmdUpdate.Parameters.AddWithValue("@ApprovalText", approvalText);
                                cmdUpdate.Parameters.AddWithValue("@SupervisorStatus",
                                    string.IsNullOrEmpty(supervisorStatus) ? DBNull.Value : (object)supervisorStatus);

                                cmdUpdate.ExecuteNonQuery();
                            }

                            transaction.Commit();
                            ShowSuccess(statusMessage);
                            UpdateUI();
                        }
                        catch (Exception ex)
                        {
                            transaction.Rollback();
                            throw new Exception("حدث خطأ أثناء معالجة الطلب", ex);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                ShowError($"حدث خطأ: {ex.Message}");
            }
        }

        private void UpdateUI()
        {
            OrderDetailsPanel.Visible = false;
            PopulateOrderNumbers();
            if (Master is SiteMaster master)
            {
                master.UpdateNewOrdersCount();
            }
        }
        private void ShowError(string message)
        {
            LabelError.Text = message;
            LabelError.Visible = true;
            LabelMessage.Visible = false;
        }
        private void ShowSuccess(string message)
        {
            LabelMessage.Text = message;
            LabelMessage.Visible = true;
            LabelError.Visible = false;
        }

        private void UpdateNewOrdersCount()
        {
            SiteMaster master = (SiteMaster)Master;
            if (master != null)
            {
                master.UpdateNewOrdersCount();
            }
        }


        // تقوم هذه الدالة بمعالجة إلغاء الطلب عند النقر على زر "Reject Order":
        // 1. التحقق مما إذا تم اختيار رقم طلب صحيح من القائمة المنسدلة.
        // 2. إذا لم يتم اختيار رقم طلب، يتم عرض رسالة خطأ.
        // 3. التحقق مما إذا تم إدخال سبب الإلغاء. إذا كان السبب فارغًا، يتم عرض رسالة خطأ.
        // 4. يتم استرجاع رقم الطلب المحدد واسم المستخدم من الجلسة.
        // 5. يتم تحديث حالة الطلب في قاعدة البيانات إلى "تم الإلغاء من قبل المشرف"، وتحديث حقل سبب الإلغاء وحقل المشرف الذي قام بالإلغاء.
        // 6. إذا تم التحديث بنجاح، يتم عرض رسالة تأكيد، إخفاء تفاصيل الطلب، وتحديث قائمة أرقام الطلبات.
        // 7. إذا فشل التحديث، يتم عرض رسالة خطأ.
        protected void btnRejectOrder_Click(object sender, EventArgs e)
        {
            // Check if a valid order number is selected
            if (ddlOrderNumbers.SelectedIndex == 0)
            {
                LabelError.Text = "يرجى اختيار رقم الطلب.";
                LabelError.Visible = true;
                LabelMessage.Visible = false;
                return;
            }

            string rejectReason = txtRejectReason.Text.Trim();

            if (string.IsNullOrEmpty(rejectReason))
            {
                LabelError.Text = "يرجى إدخال سبب الإلغاء.";
                LabelError.Visible = true;
                return;
            }
            else
            {
                string selectedOrderNumber = ddlOrderNumbers.SelectedValue; // Use SelectedValue to get the actual value
                string confirmedBy = Session["Username"]?.ToString(); // Retrieve the username from session
                string connectionString = ConfigurationManager.ConnectionStrings["MyConnectionString"].ConnectionString;

                using (SqlConnection con = new SqlConnection(connectionString))
                {
                    string query = "UPDATE ordersTable SET [حالة الطلب] = 'تم الإلغاء من مدير القسم', [تم التأكيد/الإلغاء من مدير القسم] = @ConfirmedBy , [سبب الإلغاء/الإعادة] = @RejectReason WHERE [رقم الطلب] = @OrderNumber";
                    using (SqlCommand cmd = new SqlCommand(query, con))
                    {
                        cmd.Parameters.AddWithValue("@OrderNumber", selectedOrderNumber);
                        cmd.Parameters.AddWithValue("@ConfirmedBy", confirmedBy);
                        cmd.Parameters.AddWithValue("@RejectReason", rejectReason);

                        con.Open();
                        int rowsAffected = cmd.ExecuteNonQuery();

                        if (rowsAffected > 0)
                        {
                            LabelMessage.Text = "تم إلغاء الطلب بنجاح.";
                            OrderDetailsPanel.Visible = false;
                            LabelMessage.Visible = true;
                            LabelError.Visible = false;
                            PopulateOrderNumbers(); // Refresh the list after updating
                            SiteMaster master = (SiteMaster)Master;
                            if (master != null)
                            {
                                master.UpdateNewOrdersCount();
                            }
                        }
                        else
                        {
                            LabelError.Text = "حدث خطأ أثناء إلغاء الطلب.";
                            LabelError.Visible = true;
                            LabelMessage.Visible = false;
                        }
                    }
                }
            }
        }


    }
}
